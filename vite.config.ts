import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vite.dev/config/
export default defineConfig(() => ({
  //base: '/ts-management/',
  plugins: [
    react(),
    // {
    //   name: 'modify-asset-urls',
    //   transformIndexHtml(html) {
    //     // Modify the HTML to remove the extra `/admin/` in asset URLs
    //     console.log('html', html)
    //     console.log('\n\n\n New Html', html.replace(/\/ts-management\/assets\//g, '/assets/'))
    //     return html.replace(/\/ts-management\/assets\//g, '/assets/')
    //   },
    // },
  ],
  server: {
    port: 3000,
  },
  resolve: {
    alias: {
      '@pages': path.resolve(__dirname, 'src/pages'),
      '@components': path.resolve(__dirname, 'src/components'),
      '@hooks': path.resolve(__dirname, 'src/hooks'),
      '@config': path.resolve(__dirname, 'src/config'),
      '@store': path.resolve(__dirname, 'src/store'),
      '@utils': path.resolve(__dirname, 'src/utils'),
      '@assets': path.resolve(__dirname, 'src/assets'),
      '@types': path.resolve(__dirname, 'src/types'),
      '@auth': path.resolve(__dirname, 'src/auth'),
    },
  },
  build: {
    assetsDir: 'ts-management/assets',
  },
}))
