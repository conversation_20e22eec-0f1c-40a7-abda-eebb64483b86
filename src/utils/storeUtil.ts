import { create, StateCreator } from 'zustand'
import { devtools } from 'zustand/middleware'

export const createStoreWithDevtools = <T extends object>(
  initialState: Partial<T>,
  sliceFn: StateCreator<T, [['zustand/devtools', never]], [], T>,
  name: string,
) => {
  return create<T>()(
    devtools(
      (set, get, store) => ({
        ...initialState,
        ...sliceFn(set, get, store),
      }),
      { name },
    ),
  )
}
