import { extractTokenData } from '../auth/msalUtil'
import { fetchUserDetails } from '../service/hostService'
import { User } from 'types/User.model'
import { create } from 'zustand'

type HostState = {
  userDetail: User | null
  employeeCode: string
  loadingCount: number
  isAppTransitioning: boolean
}

type HostActions = {
  getUserDetail: () => void
  setAppTransitioning: (isTransitioning: boolean) => void
  incrementLoading: () => void
  decrementLoading: () => void
}

export const useHostStore = create<HostState & HostActions>((set, get) => ({
  userDetail: null,
  employeeCode: '',
  loadingCount: 0,
  isAppTransitioning: false,

  setAppTransitioning: (isTransitioning: boolean) => {
    set({ isAppTransitioning: isTransitioning })
  },

  incrementLoading: () => {
    set({ loadingCount: get().loadingCount + 1 })
  },

  decrementLoading: () => {
    set({ loadingCount: get().loadingCount - 1 })
  },

  getUserDetail: async () => {
    console.log('Storeeeeee')
    const loadingCount = get().loadingCount
    set({ loadingCount: loadingCount + 1 })

    try {
      const tokenData = await extractTokenData()
      const userDetail = await fetchUserDetails(tokenData?.CSID || '')
      set({ userDetail, employeeCode: userDetail?.employeeCode || '' })
      console.log('[DEBUG] User detail fetched:', userDetail)
    } catch (error) {
      console.error('[ERROR] Failed to fetch user details:', error)
      return
    } finally {
      set({ loadingCount: get().loadingCount - 1 })
    }
  },
}))
