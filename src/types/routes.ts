/**
 * Route constants and enums to prevent route mismatches
 * between navigation configuration and routing setup
 */

export enum AppRoutes {
  DASHBOARD = '/',
  TIMESHEET = '/timesheet',
  APPROVALS = '/approvals',
  PERMISSIONS = '/permissions',
}

/**
 * Route paths without leading slash for React Router
 */
export enum RoutePaths {
  DASHBOARD = '',
  TIMESHEET = 'timesheet',
  APPROVALS = 'approvals',
  PERMISSIONS = 'permissions',
}

/**
 * Navigation paths for NavLink components (with proper root path)
 */
export enum NavigationPaths {
  DASHBOARD = '/',
  TIMESHEET = 'timesheet',
  APPROVALS = 'approvals',
  PERMISSIONS = 'permissions',
}

/**
 * Navigation labels for consistency
 */
export enum NavigationLabels {
  DASHBOARD = 'Dashboard',
  TIMESHEET = 'Timesheet',
  APPROVALS = 'Approvals',
  PERMISSIONS = 'Permissions',
  REPORTS = 'Reports',
  HELP = 'Help',
}

/**
 * Submenu labels for timesheet section
 */
export enum TimesheetSubmenuLabels {
  MY_TIMESHEET = 'My Timesheet',
  TIMESHEET_HISTORY = 'Timesheet History',
  TIMESHEET_SETTINGS = 'Timesheet Settings',
  EXPORT_TIMESHEET = 'Export Timesheet',
}

/**
 * Submenu labels for approvals section
 */
export enum ApprovalsSubmenuLabels {
  PENDING_APPROVALS = 'Pending Approvals',
  APPROVED_TIMESHEETS = 'Approved Timesheets',
  REJECTED_TIMESHEETS = 'Rejected Timesheets',
  APPROVAL_SETTINGS = 'Approval Settings',
}

/**
 * Submenu labels for permissions section
 */
export enum PermissionsSubmenuLabels {
  USER_PERMISSIONS = 'User Permissions',
  ROLE_MANAGEMENT = 'Role Management',
  ACCESS_CONTROL = 'Access Control',
}
