import IFrameEnhanced from '@components/IFrameEnhanced'
import React from 'react'
import { useHostStore } from '@store/hostStore'
import { getIframeConfig } from '@config/iframeConfig'

const PermissionsApp: React.FC = () => {
  const { employeeCode } = useHostStore((state) => state)

  if (!employeeCode) {
    return null // Don't render until we have employee code
  }

  const config = getIframeConfig('permissions', employeeCode)
  // Add business role parameter for permissions
  const permissionsUrl = `${config.url}&businessRole=ROARCROSS COMPANY MANAGER`

  return <IFrameEnhanced url={permissionsUrl} preloadUrls={config.preloadUrls} />
}

export default PermissionsApp
