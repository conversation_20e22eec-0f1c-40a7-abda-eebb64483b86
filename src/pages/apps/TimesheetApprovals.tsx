import IFrameEnhanced from '@components/IFrameEnhanced'
import { useHostStore } from '@store/hostStore'
import { getIframeConfig } from '@config/iframeConfig'
import React from 'react'

const TimesheetApprovalsApp: React.FC = () => {
  const { employeeCode } = useHostStore((state) => state)

  if (!employeeCode) {
    return null // Don't render until we have employee code
  }

  const config = getIframeConfig('approvals', employeeCode)

  return <IFrameEnhanced url={config.url} preloadUrls={config.preloadUrls} />
}

export default TimesheetApprovalsApp
