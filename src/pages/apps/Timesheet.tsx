import IFrameEnhanced from '@components/IFrameEnhanced'
import React from 'react'
import { useHostStore } from '@store/hostStore'
import { getIframeConfig } from '@config/iframeConfig'

const TimesheetApp: React.FC = () => {
  const { employeeCode } = useHostStore((state) => state)

  if (!employeeCode) {
    return null // Don't render until we have employee code
  }

  const config = getIframeConfig('timesheet', employeeCode)

  return <IFrameEnhanced url={config.url} preloadUrls={config.preloadUrls} />
}

export default TimesheetApp
