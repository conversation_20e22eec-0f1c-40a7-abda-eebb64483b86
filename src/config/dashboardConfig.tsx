import { AccessTime } from '@mui/icons-material'

export const dashboardNavigationItems = [
  {
    label: 'My Actions',
    children: [
      {
        label: 'Timesheet',
        to: '/',
        icon: <AccessTime sx={{ fontSize: 32, color: 'rgba(122, 80, 6, 0.6)' }} />,
        iconColor: 'rgba(122, 80, 6, 0.6)',
        gradientFrom: '#FFD952',
        gradientTo: '#EA9809',
      },
      {
        label: 'Missing Timesheet Calendar',
        to: '/',
        icon: <AccessTime sx={{ fontSize: 32, color: 'rgba(255, 200, 102, 0.6)' }} />,
        iconColor: 'rgba(255, 200, 102, 0.6)',
        gradientFrom: '#FF8352',
        gradientTo: '#E45B06',
      },
      {
        label: 'Absence',
        to: '/',
        icon: <AccessTime sx={{ fontSize: 32, color: 'rgba(2, 51, 14, 0.6)' }} />,
        iconColor: 'rgba(2, 51, 14, 0.6)',
        gradientFrom: '#32AF4D',
        gradientTo: '#03801E',
      },
    ],
  },

  {
    label: 'rOar Manager Actions',
    children: [
      {
        label: 'Timesheet Approvals',
        to: '/',
        icon: <AccessTime sx={{ fontSize: 32, color: 'rgba(2, 51, 14, 0.6)' }} />,
        iconColor: 'rgba(2, 51, 14, 0.6)',
        gradientFrom: '#32AF9E',
        gradientTo: '#03806B',
      },
      {
        label: 'Absence Approvals',
        to: '/',
        icon: <AccessTime sx={{ fontSize: 32, color: 'rgba(9, 85, 26, 1)' }} />,
        iconColor: 'rgba(9, 85, 26, 1)',
        gradientFrom: '#32AF4D',
        gradientTo: '#03801E',
      },
    ],
  },

  {
    label: 'rOar Manager Access',
    children: [
      {
        label: 'Resource Management Calendar',
        to: '/',
        icon: <AccessTime sx={{ fontSize: 32, color: 'rgba(83, 6, 122, 0.6)' }} />,
        iconColor: 'rgba(83, 6, 122, 0.6)',
        gradientFrom: '#BE5CFF',
        gradientTo: '#760DEB',
      },
      {
        label: 'Cross Company Dashboard',
        to: '/',
        icon: <AccessTime sx={{ fontSize: 32, color: 'rgba(70, 0, 0, 0.6)' }} />,
        iconColor: 'rgba(70, 0, 0, 0.6)',
        gradientFrom: '#FE3247',
        gradientTo: '#C70014',
      },
      {
        label: 'Projects Page',
        to: '/',
        icon: <AccessTime sx={{ fontSize: 32, color: 'rgba(8, 6, 122, 0.6)' }} />,
        iconColor: 'rgba(8, 6, 122, 0.6)',
        gradientFrom: '#43A5F7',
        gradientTo: '#005ED7',
      },
      {
        label: 'Workfront Homepage',
        to: '/',
        icon: <AccessTime sx={{ fontSize: 32, color: 'rgba(62, 0, 75, 0.6)' }} />,
        iconColor: 'rgba(62, 0, 75, 0.6)',
        gradientFrom: '#F02FFE',
        gradientTo: '#AC00D6',
      },
    ],
  },
]
