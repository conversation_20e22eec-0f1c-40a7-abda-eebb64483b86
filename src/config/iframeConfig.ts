import { timesheetURL, timesheetApprovalsURL, icaURL } from '@utils/appConfig'

export interface IframeAppConfig {
  url: string
  preloadUrls?: string[]
  title: string
  loadingTimeout?: number
}

export const iframeAppsConfig: Record<string, IframeAppConfig> = {
  timesheet: {
    url: timesheetURL,
    title: 'Timesheet',
    preloadUrls: [timesheetApprovalsURL], // Preload approvals since users often navigate there
    loadingTimeout: 15000
  },
  approvals: {
    url: timesheetApprovalsURL,
    title: 'Timesheet Approvals',
    preloadUrls: [timesheetURL], // Preload timesheet since users often navigate back
    loadingTimeout: 15000
  },
  permissions: {
    url: icaURL,
    title: 'Permissions',
    loadingTimeout: 20000 // Permissions might take longer to load
  }
}

export const getIframeConfig = (appType: string, employeeCode: string): IframeAppConfig => {
  const config = iframeAppsConfig[appType]
  if (!config) {
    throw new Error(`Unknown iframe app type: ${appType}`)
  }

  // Build URL with employee code
  const buildUrl = (baseUrl: string) => {
    const separator = baseUrl.includes('?') ? '&' : '?'
    return `${baseUrl}${separator}employeeCode=${employeeCode}`
  }

  return {
    ...config,
    url: buildUrl(config.url),
    preloadUrls: config.preloadUrls?.map(buildUrl) || []
  }
}
