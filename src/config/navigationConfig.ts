import { MenuItem } from '../types/navigation'
import {
  NavigationPaths,
  NavigationLabels,
  TimesheetSubmenuLabels,
  ApprovalsSubmenuLabels,
  PermissionsSubmenuLabels
} from '../types/routes'

/**
 * Navigation configuration for the header menu
 * 
 * This configuration supports:
 * - Simple menu items with routes (to)
 * - External links (href)
 * - Custom click handlers (onClick)
 * - Submenus with nested items
 * - Dividers in submenus
 * - Disabled items
 */
export const navigationConfig: MenuItem[] = [
  {
    name: NavigationLabels.DASHBOARD,
    to: NavigationPaths.DASHBOARD,
  },
  {
    name: NavigationLabels.TIMESHEET,
    to: NavigationPaths.TIMESHEET,
    submenu: [
      { name: TimesheetSubmenuLabels.MY_TIMESHEET, to: NavigationPaths.TIMESHEET },
      { name: TimesheetSubmenuLabels.TIMESHEET_HISTORY, onClick: () => console.log('Timesheet History clicked') },
      { divider: true, name: '' },
      {
        name: TimesheetSubmenuLabels.TIMESHEET_SETTINGS,
        submenu: [
          { name: 'General Settings', onClick: () => console.log('General Settings clicked') },
          { name: 'Notification Settings', onClick: () => console.log('Notification Settings clicked') },
          {
            name: 'Advanced Settings',
            submenu: [
              { name: 'Time Tracking', onClick: () => console.log('Time Tracking clicked') },
              { name: 'Approval Workflow', onClick: () => console.log('Approval Workflow clicked') },
              { name: 'Integration Settings', onClick: () => console.log('Integration Settings clicked') },
            ]
          },
        ]
      },
      { name: TimesheetSubmenuLabels.EXPORT_TIMESHEET, onClick: () => console.log('Export clicked') },
    ]
  },
  {
    name: NavigationLabels.APPROVALS,
    to: NavigationPaths.APPROVALS,
    submenu: [
      { name: ApprovalsSubmenuLabels.PENDING_APPROVALS, to: NavigationPaths.APPROVALS },
      { name: ApprovalsSubmenuLabels.APPROVED_TIMESHEETS, onClick: () => console.log('Approved clicked') },
      { name: ApprovalsSubmenuLabels.REJECTED_TIMESHEETS, onClick: () => console.log('Rejected clicked') },
      { divider: true, name: '' },
      { name: ApprovalsSubmenuLabels.APPROVAL_SETTINGS, onClick: () => console.log('Approval Settings clicked') },
    ]
  },
  {
    name: NavigationLabels.PERMISSIONS,
    to: NavigationPaths.PERMISSIONS,
    submenu: [
      { name: PermissionsSubmenuLabels.USER_PERMISSIONS, to: NavigationPaths.PERMISSIONS },
      { name: PermissionsSubmenuLabels.ROLE_MANAGEMENT, onClick: () => console.log('Role Management clicked') },
      { name: PermissionsSubmenuLabels.ACCESS_CONTROL, onClick: () => console.log('Access Control clicked') },
    ]
  },
  {
    name: NavigationLabels.REPORTS,
    submenu: [
      {
        name: 'Time Reports',
        submenu: [
          { name: 'Weekly Report', onClick: () => console.log('Weekly Report clicked') },
          { name: 'Monthly Report', onClick: () => console.log('Monthly Report clicked') },
          { name: 'Annual Report', onClick: () => console.log('Annual Report clicked') },
        ]
      },
      {
        name: 'Analytics',
        submenu: [
          { name: 'Performance Analytics', onClick: () => console.log('Performance Analytics clicked') },
          { name: 'Team Productivity', onClick: () => console.log('Team Productivity clicked') },
          {
            name: 'Advanced Analytics',
            submenu: [
              { name: 'Trend Analysis', onClick: () => console.log('Trend Analysis clicked') },
              { name: 'Predictive Reports', onClick: () => console.log('Predictive Reports clicked') },
              { name: 'Custom Metrics', onClick: () => console.log('Custom Metrics clicked') },
            ]
          },
        ]
      },
      { divider: true, name: '' },
      { name: 'Custom Report', onClick: () => console.log('Custom Report clicked') },
      { name: 'Export Reports', onClick: () => console.log('Export Reports clicked') },
    ]
  },
  {
    name: NavigationLabels.HELP,
    submenu: [
      { name: 'Documentation', href: 'https://docs.example.com', },
      { name: 'Support', href: 'https://support.example.com' },
      { name: 'Contact Us', onClick: () => console.log('Contact Us clicked') },
      { divider: true, name: '' },
      { name: 'About', onClick: () => console.log('About clicked') },
    ]
  },
]

// Alternative simple configuration without submenus
export const simpleNavigationConfig: MenuItem[] = [
  { name: NavigationLabels.DASHBOARD, to: NavigationPaths.DASHBOARD },
  { name: NavigationLabels.TIMESHEET, to: NavigationPaths.TIMESHEET },
  { name: NavigationLabels.APPROVALS, to: NavigationPaths.APPROVALS },
  { name: NavigationLabels.PERMISSIONS, to: NavigationPaths.PERMISSIONS },
]
