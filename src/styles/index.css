html, body {
  margin: 0;
  padding: 0;
}

/* #root {
  padding: 0 1.5rem;
} */

.MuiTypography-root.field-label {
  display: inline-block;
  margin-bottom: 0.375rem;
}

.css-1w7cibh-MuiDataGrid-root .MuiDataGrid-cell {
  border-top: none;
}

.Toastify {
  .Toastify__toast-container {
    bottom: 6em;
    width: fit-content;

    .Toastify__toast {
      width: fit-content;
      min-width: 326px;
      max-width: 600px;

      .Toastify__close-button {
        color: white;
        opacity: 1;
      }
    }

    .Toastify__toast--success {
      background-color: #0f9156;
      color: white;
    }

    .Toastify__toast--error {
      background-color: #d7322d;
      color: white;
    }

    .Toastify__toast--warning {
      background-color: #d1990a;
      color: white;
    }
  }
}
.MuiDataGrid-cell {
  white-space: normal !important;
  overflow: visible !important;
  text-overflow: inherit !important;
  line-height: normal !important;
  align-items: center;
  display: flex;
  word-break: break-word;
}

.privacy-notice-container b, .privacy-notice-container strong {
  font-weight: 600;
}
.privacy-notice-container p {
  line-height: 2;
  margin-bottom: 1rem;
}

.privacy-notice-container .MuiBox-root li {
  padding-top: 0; 
  padding-bottom: 0;
}

.privacy-notice-container .MuiBox-root li span{
  line-height: 2;
}


.privacy-notice-container .MuiBox-root span > u {
  line-height: 4;
}

.privacy-notice-container a[href] {
  color: #1077D6;
  text-decoration: none;
}

.MuiPickersArrowSwitcher-root .MuiPickersArrowSwitcher-button[disabled]{
  opacity: 0.4;
}
 
 