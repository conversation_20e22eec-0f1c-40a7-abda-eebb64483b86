import { createTheme } from '@mui/material/styles'
import palette from '../palette'

declare module '@mui/material/Typography' {
  interface TypographyPropsVariantOverrides {
    BodyMBold: true
    BodyOverLineS: true
    BodyXSRegular: true
    BodyXSBold: true
    BodySRegular: true
    BodySBold: true
    BodyMRegular: true
    BodyMMedium: true
    BodyLBold: true
  }
}

const pxToRem = (sizeInPixel: number): string => `${sizeInPixel / 16}rem`

const theme = createTheme({
  spacing: 4,
  palette,
  typography: {
    fontSize: 14,
    fontFamily: 'Volte',
  },
  components: {
    MuiTypography: {
      styleOverrides: {
        root: {
          variants: [
            {
              props: { variant: 'h1' },
              style: {
                fontSize: pxToRem(30),
                fontWeight: 600,
                lineHeight: pxToRem(40),
              },
            },
            {
              props: { variant: 'h2' },
              style: {
                fontSize: pxToRem(24),
                fontWeight: 600,
                lineHeight: pxToRem(32),
              },
            },
            {
              props: { variant: 'h3' },
              style: {
                fontSize: pxToRem(18),
                fontWeight: 600,
                lineHeight: pxToRem(24),
              },
            },
            {
              props: { variant: 'BodySRegular' },
              style: {
                fontSize: pxToRem(12),
                fontWeight: 400,
                lineHeight: pxToRem(18),
              },
            },
            {
              props: { variant: 'BodySBold' },
              style: {
                fontSize: pxToRem(12),
                fontWeight: 600,
                lineHeight: pxToRem(18),
              },
            },
            {
              props: { variant: 'BodyXSBold' },
              style: {
                fontSize: pxToRem(10),
                fontWeight: 600,
                lineHeight: pxToRem(12),
              },
            },
            {
              props: { variant: 'BodyXSRegular' },
              style: {
                fontSize: pxToRem(10),
                fontWeight: 400,
                lineHeight: pxToRem(12),
              },
            },
            {
              props: { variant: 'BodyMRegular' },
              style: {
                fontSize: pxToRem(14),
                fontWeight: 400,
                lineHeight: pxToRem(20),
              },
            },
            {
              props: { variant: 'BodyMMedium' },
              style: {
                fontSize: pxToRem(14),
                fontWeight: 500,
                lineHeight: pxToRem(20),
              },
            },
            {
              props: { variant: 'BodyMBold' },
              style: {
                fontSize: pxToRem(14),
                fontWeight: 600,
                lineHeight: pxToRem(20),
              },
            },
            {
              props: { variant: 'BodyLBold' },
              style: {
                fontSize: pxToRem(16),
                fontWeight: 600,
                lineHeight: pxToRem(24),
              },
            },
            {
              props: { variant: 'BodyOverLineS' },
              style: {
                fontSize: pxToRem(12),
                fontWeight: 500,
                lineHeight: pxToRem(16),
              },
            },
          ],
        },
      },
    },
    MuiCssBaseline: {
      styleOverrides: {
        body: {
          '& button.MuiPickersDay-root:focus-visible': {
            outline: `2px solid ${palette['Stroke-Active-Outline'].main}`,
          },
          '& *:focus-visible:not(input):not(textarea):not(button.MuiPickersDay-root)': {
            outline: `2px solid ${palette['Stroke-Active-Outline'].main}`,
            outlineOffset: '2px',
          },
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          boxShadow: '0px 2px 12px 0px rgba(0, 0, 0, 0.08)',
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: '0.5rem',
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: '4rem',
          boxShadow: 'none',
          textTransform: 'none',
          borderColor: palette.secondary.main,
        },
        contained: {
          boxShadow: 'none',
          '&:hover': {
            boxShadow: 'none',
          },
        },
      },
    },
    MuiIconButton: {
      styleOverrides: {
        root: {
          '& .MuiSvgIcon-root': {
            fill: palette.grey[900],
          },
        },
      },
    },
    MuiCardContent: {
      styleOverrides: {
        root: {
          paddingBottom: '0 !important', // Force removal of padding
        },
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          '.MuiInputBase-root': {
            borderRadius: 8,
          },
          '& fieldset': {
            borderColor: palette.secondary.main,
          },
        },
      },
    },
    MuiCheckbox: {
      styleOverrides: {
        root: {
          color: palette.secondary.main,
          '&.Mui-checked': {
            color: palette['500-Accent-1'].main,
          },
          '&.Mui-disabled': {
            color: palette['Stroke-Grey'].main,
          },
        },
      },
    },
  },
})

export default theme
