import { PaletteOptions } from '@mui/material'
import palette from '../palette'

const themePalette: PaletteOptions = {
  common: {
    black: '#000',
    white: '#fff',
  },
  primary: {
    main: '#000',
    light: '#000',
    dark: '#000',
    contrastText: '#fff',
  },
  secondary: {
    main: '#E6E6E6',
    light: '#E6E6E6',
    dark: '#E6E6E6',
    contrastText: '#000',
  },
  error: {
    main: '#D7322D',
    light: '#D7322D',
    dark: '#D7322D',
    contrastText: '#fff',
  },
  warning: {
    main: '#ed6c02',
    light: '#ff9800',
    dark: '#e65100',
    contrastText: '#fff',
  },
  info: {
    main: '#0288d1',
    light: '#03a9f4',
    dark: '#01579b',
    contrastText: '#fff',
  },
  success: {
    main: '#2e7d32',
    light: '#4caf50',
    dark: '#1b5e20',
    contrastText: '#fff',
  },
  grey: {
    '50': '#F9F9F9',
    '100': '#F2F2F2',
    '200': '#DBDBDB',
    '300': '#B7B7B7',
    '400': '#999999',
    '500': '#808080',
    '600': '#666666',
    '700': '#4D4D4D',
    '800': '#333333',
    '900': '#202020',
    A100: '#f5f5f5',
    A200: '#eeeeee',
    A400: '#bdbdbd',
    A700: '#616161',
  },
  text: {
    primary: palette.primary.main,
    secondary: palette.grey['900'],
  },
}

export default themePalette
