import { User } from 'types/User.model'

export const fetchUserDetails = async (employeeCode: string): Promise<User> => {
  // const response = await fetch(`/api/user/${employeeCode}`
  const response = {
    employeeCode: '10082141',
    agencyCode: '537E',
    lionLogin: 'abhanshu',
    emailAddress: '<EMAIL>',
    firstName: 'Abhishek',
    lastName: 'Anshu',
    hireDate: '2022-02-14T00:00:00',
    communicationLanguage: 'EN',
    enableTimesheetNotifications: true,
    enableTaskAssignmentTimesheets: false,
    isApprover: false,
    __typename: 'UserDetails',
  }

  return response
}
