import { useEffect, useRef, useCallback } from 'react'

interface PreloadedIframe {
  url: string
  iframe: HTMLIFrameElement
  isLoaded: boolean
  lastUsed: number
}

class IframePreloader {
  private static instance: IframePreloader
  private preloadedIframes = new Map<string, PreloadedIframe>()
  private readonly MAX_PRELOADED = 3
  private readonly PRELOAD_TIMEOUT = 30000 // 30 seconds

  static getInstance(): IframePreloader {
    if (!IframePreloader.instance) {
      IframePreloader.instance = new IframePreloader()
    }
    return IframePreloader.instance
  }

  preloadIframe(url: string): Promise<HTMLIFrameElement> {
    return new Promise((resolve, reject) => {
      // Check if already preloaded
      const existing = this.preloadedIframes.get(url)
      if (existing && existing.isLoaded) {
        existing.lastUsed = Date.now()
        resolve(existing.iframe)
        return
      }

      // Create new iframe for preloading
      const iframe = document.createElement('iframe')
      iframe.style.display = 'none'
      iframe.style.position = 'absolute'
      iframe.style.top = '-9999px'
      iframe.style.left = '-9999px'
      iframe.style.width = '1px'
      iframe.style.height = '1px'

      const preloadedIframe: PreloadedIframe = {
        url,
        iframe,
        isLoaded: false,
        lastUsed: Date.now()
      }

      const timeout = setTimeout(() => {
        console.warn(`Preload timeout for ${url}`)
        this.cleanupPreloadedIframe(url)
        reject(new Error('Preload timeout'))
      }, this.PRELOAD_TIMEOUT)

      iframe.onload = () => {
        clearTimeout(timeout)
        preloadedIframe.isLoaded = true
        console.log(`Successfully preloaded iframe for ${url}`)
        resolve(iframe)
      }

      iframe.onerror = () => {
        clearTimeout(timeout)
        console.error(`Failed to preload iframe for ${url}`)
        this.cleanupPreloadedIframe(url)
        reject(new Error('Preload failed'))
      }

      // Add to DOM and start loading
      document.body.appendChild(iframe)
      iframe.src = url

      // Store the preloaded iframe
      this.preloadedIframes.set(url, preloadedIframe)
      this.cleanupOldPreloads()
    })
  }

  getPreloadedIframe(url: string): HTMLIFrameElement | null {
    const preloaded = this.preloadedIframes.get(url)
    if (preloaded && preloaded.isLoaded) {
      preloaded.lastUsed = Date.now()
      
      // Remove from preload cache since it's now being used
      this.preloadedIframes.delete(url)
      
      // Reset iframe styles for actual use
      preloaded.iframe.style.display = 'block'
      preloaded.iframe.style.position = 'static'
      preloaded.iframe.style.top = 'auto'
      preloaded.iframe.style.left = 'auto'
      preloaded.iframe.style.width = '100%'
      preloaded.iframe.style.height = 'calc(100vh - 64px)'
      preloaded.iframe.style.border = 'none'

      return preloaded.iframe
    }
    return null
  }

  private cleanupPreloadedIframe(url: string) {
    const preloaded = this.preloadedIframes.get(url)
    if (preloaded) {
      if (preloaded.iframe.parentNode) {
        preloaded.iframe.parentNode.removeChild(preloaded.iframe)
      }
      this.preloadedIframes.delete(url)
    }
  }

  private cleanupOldPreloads() {
    if (this.preloadedIframes.size <= this.MAX_PRELOADED) {
      return
    }

    // Sort by last used time and remove oldest
    const entries = Array.from(this.preloadedIframes.entries())
    entries.sort((a, b) => a[1].lastUsed - b[1].lastUsed)

    const toRemove = entries.slice(0, entries.length - this.MAX_PRELOADED)
    toRemove.forEach(([url]) => {
      this.cleanupPreloadedIframe(url)
    })
  }

  cleanup() {
    this.preloadedIframes.forEach((_, url) => {
      this.cleanupPreloadedIframe(url)
    })
  }
}

export const useIframePreloader = () => {
  const preloaderRef = useRef<IframePreloader>()

  useEffect(() => {
    preloaderRef.current = IframePreloader.getInstance()
    
    return () => {
      // Cleanup on unmount
      preloaderRef.current?.cleanup()
    }
  }, [])

  const preloadIframe = useCallback((url: string) => {
    return preloaderRef.current?.preloadIframe(url)
  }, [])

  const getPreloadedIframe = useCallback((url: string) => {
    return preloaderRef.current?.getPreloadedIframe(url)
  }, [])

  return {
    preloadIframe,
    getPreloadedIframe
  }
}
