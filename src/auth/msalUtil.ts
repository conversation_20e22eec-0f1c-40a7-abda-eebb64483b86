import { msalInstance as instance, loginRequest } from './msalConfig'
import { InteractionRequiredAuthError } from '@azure/msal-browser'

export const getAccessToken = async () => {
  try {
    let activeAccount = instance.getActiveAccount()

    // If no active account, get first available account and set it
    if (!activeAccount) {
      const accounts = instance.getAllAccounts()
      if (accounts.length === 0) {
        throw new Error('No user account found. Please log in.')
      }
      activeAccount = accounts[0]
      instance.setActiveAccount(activeAccount)
    }

    const response = await instance.acquireTokenSilent(loginRequest)

    return response.accessToken
  } catch (error) {
    console.error('%cToken Refresh Failed', 'color: red; font-size: 2em;', error)

    if (error instanceof InteractionRequiredAuthError) {
      console.log('%cUser interaction required. Redirecting to login...', 'color: orange; font-size: 2em;')

      // show login popup if in iframe
      if (window.self !== window.top) {
        instance.loginPopup(loginRequest).catch((error) => {
          //Uncaught (in promise) BrowserAuthError: popup_window_error: Error opening popup window. This can happen if you are using IE or if popups are blocked in the browser.
          if (error.errorCode === 'popup_window_error') {
            console.log('Popup blocked')
            // setShowPopupBlockedPage(true)
          }
          console.log('%cPopup Handling Error', 'color: red; font-size: 3em;', { error })
        })
      }

      // redirect to login page if in main window
      if (window.self === window.top) {
        instance.loginRedirect(loginRequest)
      }

      // await instance.loginRedirect({ scopes: ['api://fc4ada6e-f6d6-47c0-ab0f-2ae8307b40f6/timesheet_api_access'] })
    }
  }
}

/**
 * Extracts the CSID (custom security identifier) from the current user's JWT access token.
 * @returns The CSID value if present, otherwise undefined.
 */
export const extractTokenData = async (): Promise<Record<string, string> | undefined> => {
  try {
    const token = await getAccessToken()
    if (!token) return undefined
    const [, payload] = token.split('.')
    if (!payload) return undefined
    const decoded = JSON.parse(atob(payload.replace(/-/g, '+').replace(/_/g, '/')))
    // Replace 'csid' with the actual claim name if different
    return decoded
  } catch {
    return undefined
  }
}
