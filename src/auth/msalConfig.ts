import * as msal from '@azure/msal-browser'

let clientId = 'e69dd405-d623-44e0-a1e3-e126b460f819'
const tenantId = 'd52c9ea1-7c21-47b1-82a3-33a74b1f74b8'
let redirectUri = 'http://localhost:3000/'
let APIScope = 'api://fc4ada6e-f6d6-47c0-ab0f-2ae8307b40f6/timesheet_api_access'

if (typeof window !== 'undefined') {
  if (window.location.hostname.includes('dev1')) {
    console.log('DEV1')
    redirectUri = 'https://roar-dev1.marcel.ai/ts-management/'
  }

  if (window.location.hostname.includes('qa1')) {
    console.log('QA1')
    redirectUri = 'https://roar-qa1.marcel.ai/ts-management/'
  }

  if (window.location.hostname.includes('uat')) {
    console.log('UAT')
    redirectUri = 'https://roar-uat.marcel.ai/ts-management/'
  }

  if (window.location.hostname.includes('roar.marcel.ai')) {
    console.log('PROD')
    clientId = 'e61ad86d-5456-477e-84fe-698344593516'
    // tenantId = 'd52c9ea1-7c21-47b1-82a3-33a74b1f74b8'
    APIScope = 'api://8e1360e7-36fa-4ce4-9644-c6b13e658c26/timesheet_api_access'
    redirectUri = 'https://roar.marcel.ai/ts-management/'
  }
}

if (!clientId) {
  console.log('Client ID not present')
  // throw new Error('AZURE_AD_CLIENT_ID is missing in environment variables')
}

const msalConfig: msal.Configuration = {
  auth: {
    clientId,
    authority: `https://login.microsoftonline.com/${tenantId}`,
    redirectUri: redirectUri,
  },
  cache: {
    cacheLocation: 'sessionStorage',
    storeAuthStateInCookie: true,
  },
}

export const loginRequest = {
  scopes: [APIScope],
}

const msalInstance = new msal.PublicClientApplication(msalConfig)

export { msalInstance }
