import { useEffect } from 'react'
import { useMsal, useIsAuthenticated } from '@azure/msal-react'
import { loginRequest } from './msalConfig'
import RoarSplash from '@components/Header/RoarSplash'

const MSALAuth = () => {
  const { instance } = useMsal()
  const isAuthenticated = useIsAuthenticated()

  useEffect(() => {
    instance
      .handleRedirectPromise()
      .then((response) => {
        if (response) {
          console.log('%cAuth Response from Redirect', 'color: green; font-size: 3em;', response)
          // msalInstance.setActiveAccount(response.account)
        } else if (!isAuthenticated) {
          // Step 2: Attempt SSO silent login
          instance
            .ssoSilent(loginRequest)
            .then((authResponse) => {
              console.log('%cAuth Response', 'color: green; font-size: 3em;', authResponse)

              const options = {
                method: 'GET',
                headers: {
                  Authorization: `Bearer ${authResponse.accessToken}`,
                },
              }

              fetch('https://graph.microsoft.com/v1.0/me', options)
                .then((response) => response.json())
                .then((json) => {
                  console.log('%cGraphResponse', 'font-size: 3em; color: blue;', json)
                })
                .catch((error) => {
                  console.log('%cGraphError', 'color: red; font-size: 3em;', { error })
                })
            })
            .catch((error) => {
              console.log('%cSSO Silent Error', 'color: red; font-size: 3em;', { error })

              // Step 3: If SSO fails, initiate loginRedirect
              instance.loginRedirect(loginRequest)
            })
        }
      })
      .catch((error) => {
        console.log('%cRedirect Handling Error', 'color: red; font-size: 3em;', { error })
      })
  }, [isAuthenticated, instance])

  return <div>Loading...</div>
}

export default MSALAuth
