import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './styles/index.css'
import App from './App.tsx'
// import './lib/i18n'
import './assets/fonts/volte-font.css'
import { BrowserRouter } from 'react-router-dom'
import { msalInstance } from './auth/msalConfig'
import { MsalProvider } from '@azure/msal-react'

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    {/* <BrowserRouter basename="/roar"> */}
    <BrowserRouter basename="/ts-management">
      <MsalProvider instance={msalInstance}>
        <App />
      </MsalProvider>
    </BrowserRouter>
  </StrictMode>,
)
