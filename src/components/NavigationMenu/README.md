# NavigationMenu Component

A configurable navigation menu component with submenu support built with Material UI.

## Features

- ✅ Clean, modular design with no spaghetti code
- ✅ Support for regular menu items and unlimited nested submenus
- ✅ Material UI integration with consistent styling
- ✅ TypeScript support with proper type definitions
- ✅ Flexible configuration options
- ✅ Support for internal routes, external links, and custom actions
- ✅ Dividers in submenus
- ✅ Disabled menu items
- ✅ Hover-triggered submenus (no selected state)
- ✅ Consistent BodyMBold typography
- ✅ Right-pointing chevrons for nested submenus
- ✅ No chevrons on main navigation items

## Usage

### Basic Configuration

```typescript
import { MenuItem } from '../../types/navigation'

const navigationItems: MenuItem[] = [
  { name: 'Dashboard', to: '/' },
  { name: 'Profile', to: '/profile' },
  { name: 'Settings', to: '/settings' },
]
```

### Configuration with Multi-Level Submenus

```typescript
const navigationItems: MenuItem[] = [
  {
    name: 'Timesheet',
    to: 'timesheet', // Main route
    submenu: [
      { name: 'My Timesheet', to: 'timesheet' },
      { name: 'Timesheet History', onClick: () => console.log('History clicked') },
      { divider: true, name: '' }, // Adds a divider
      {
        name: 'Settings', // This will show a right chevron
        submenu: [
          { name: 'General Settings', onClick: () => openGeneralSettings() },
          { name: 'Notifications', onClick: () => openNotifications() },
          {
            name: 'Advanced', // Nested 3rd level
            submenu: [
              { name: 'Time Tracking', onClick: () => openTimeTracking() },
              { name: 'Integrations', onClick: () => openIntegrations() },
            ]
          }
        ]
      },
    ]
  },
  {
    name: 'Reports',
    submenu: [ // No main route, only submenu
      {
        name: 'Time Reports',
        submenu: [
          { name: 'Weekly Report', onClick: () => generateWeeklyReport() },
          { name: 'Monthly Report', onClick: () => generateMonthlyReport() },
        ]
      },
      { name: 'Export', href: '/api/export' }, // External link
    ]
  },
]
```

### Component Usage

```tsx
import NavigationMenu from '@components/NavigationMenu'
import { navigationConfig } from '@config/navigationConfig'

function Header() {
  return (
    <AppBar>
      <Toolbar>
        <NavigationMenu items={navigationConfig} />
      </Toolbar>
    </AppBar>
  )
}
```

## Configuration Options

### MenuItem Interface

```typescript
interface MenuItem {
  name: string           // Display name
  to?: string           // Internal route (React Router)
  href?: string         // External URL
  onClick?: () => void  // Custom click handler
  disabled?: boolean    // Disable the menu item
  submenu?: SubMenuItem[] // Submenu items
}
```

### SubMenuItem Interface

```typescript
interface SubMenuItem {
  name: string           // Display name
  to?: string           // Internal route
  href?: string         // External URL
  onClick?: () => void  // Custom click handler
  disabled?: boolean    // Disable the menu item
  divider?: boolean     // Show as divider (name can be empty)
  submenu?: SubMenuItem[] // Nested submenu items (unlimited levels)
}
```

## Examples

### Simple Menu
```typescript
const simpleMenu: MenuItem[] = [
  { name: 'Home', to: '/' },
  { name: 'About', to: '/about' },
  { name: 'Contact', href: 'mailto:<EMAIL>' },
]
```

### Complex Menu with Submenus
```typescript
const complexMenu: MenuItem[] = [
  {
    name: 'Products',
    submenu: [
      { name: 'Web Apps', to: '/products/web' },
      { name: 'Mobile Apps', to: '/products/mobile' },
      { divider: true, name: '' },
      { name: 'Enterprise', to: '/products/enterprise' },
    ]
  },
  {
    name: 'Resources',
    submenu: [
      { name: 'Documentation', href: 'https://docs.example.com' },
      { name: 'API Reference', href: 'https://api.example.com' },
      { name: 'Support', onClick: () => openSupportChat() },
    ]
  },
]
```

## Styling

The component uses Material UI's theming system and follows the project's typography standards:

- **Typography**: Uses the `BodyMBold` variant (14px, font-weight 600, line-height 20px)
- **Font Family**: Volte (as defined in the project theme)
- **Color**: #000 (high contrast black)

The component can be customized through:

1. **Theme customization**: Modify your Material UI theme
2. **Typography variants**: Update the BodyMBold variant in the theme
3. **Component props**: Pass custom sx props
4. **CSS classes**: Override specific component styles

## Behavior

### Hover-Based Submenus
- **No selected state**: Menu items don't show persistent selection indicators
- **Hover to open**: Submenus appear when hovering over menu items with submenus
- **Auto-close**: Submenus close when mouse leaves the menu area
- **Smooth interaction**: Designed for intuitive hover-based navigation

### Navigation
- **Internal routes**: Use `to` prop for React Router navigation
- **External links**: Use `href` prop for external URLs
- **Custom actions**: Use `onClick` prop for custom functionality

## Best Practices

1. **Keep menu structure shallow**: Avoid deeply nested submenus
2. **Use meaningful names**: Clear, concise menu item names
3. **Group related items**: Use dividers to separate logical groups
4. **Consistent navigation**: Use either `to` for internal routes or `href` for external links
5. **Handle loading states**: Disable menu items during async operations
6. **Hover-friendly design**: Ensure adequate spacing for easy hover navigation
