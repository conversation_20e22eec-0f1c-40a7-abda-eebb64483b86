import React, { useState } from 'react'
import {
  Button,
  <PERSON>u,
  <PERSON>uItem,
  Divider,
  Box,
  ListItemText,
  Typography,
} from '@mui/material'
import { NavLink } from 'react-router-dom'
import { ChevronRight as ChevronRightIcon } from '@mui/icons-material'
import { MenuItem as MenuItemType, SubMenuItem } from '../../types/navigation'

interface NavigationMenuProps {
  items: MenuItemType[]
}

interface MenuItemComponentProps {
  item: MenuItemType
}

interface NestedMenuItemProps {
  item: SubMenuItem
  onClose: () => void
}

const NestedMenuItem: React.FC<NestedMenuItemProps> = ({ item, onClose }) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const open = Boolean(anchorEl)

  const handleMouseEnter = (event: React.MouseEvent<HTMLElement>) => {
    if (item.submenu && item.submenu.length > 0) {
      setAnchorEl(event.currentTarget)
    }
  }

  const handleMouseLeave = (event: React.MouseEvent<HTMLElement>) => {
    // Only close if we're actually leaving the menu item area
    const relatedTarget = event.relatedTarget as HTMLElement
    const currentTarget = event.currentTarget as HTMLElement

    // Check if we're moving to the submenu
    if (relatedTarget && (currentTarget.contains(relatedTarget) ||
        relatedTarget.closest('[role="menu"]'))) {
      return // Don't close if moving to submenu
    }

    setAnchorEl(null)
  }

  const handleItemClick = () => {
    if (item.onClick) {
      item.onClick()
    }
    onClose()
  }

  if (item.divider) {
    return <Divider />
  }

  return (
    <Box
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      sx={{ position: 'relative' }}
    >
      {item.to ? (
        <MenuItem
          component={NavLink}
          to={item.to}
          onClick={onClose}
          disabled={item.disabled}
          sx={{
            textDecoration: 'none',
            '&:hover': {
              backgroundColor: 'rgba(0, 0, 0, 0.04)',
            },
          }}
        >
          <ListItemText
            primary={
              <Typography variant="BodyMBold" color="#000">
                {item.name}
              </Typography>
            }
          />
          {item.submenu && item.submenu.length > 0 && (
            <ChevronRightIcon sx={{ ml: 1, fontSize: 16 }} />
          )}
        </MenuItem>
      ) : item.href ? (
        <MenuItem
          component="a"
          href={item.href}
          onClick={onClose}
          disabled={item.disabled}
          sx={{
            textDecoration: 'none',
            '&:hover': {
              backgroundColor: 'rgba(0, 0, 0, 0.04)',
            },
          }}
        >
          <ListItemText
            primary={
              <Typography variant="BodyMBold" color="#000">
                {item.name}
              </Typography>
            }
          />
          {item.submenu && item.submenu.length > 0 && (
            <ChevronRightIcon sx={{ ml: 1, fontSize: 16 }} />
          )}
        </MenuItem>
      ) : (
        <MenuItem
          onClick={handleItemClick}
          disabled={item.disabled}
          sx={{
            '&:hover': {
              backgroundColor: 'rgba(0, 0, 0, 0.04)',
            },
          }}
        >
          <ListItemText
            primary={
              <Typography variant="BodyMBold" color="#000">
                {item.name}
              </Typography>
            }
          />
          {item.submenu && item.submenu.length > 0 && (
            <ChevronRightIcon sx={{ ml: 1, fontSize: 16 }} />
          )}
        </MenuItem>
      )}

      {item.submenu && item.submenu.length > 0 && (
        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={() => setAnchorEl(null)}
          disableAutoFocus
          disableEnforceFocus
          disableRestoreFocus
          anchorOrigin={{
            vertical: 'center',
            horizontal: 'right',
          }}
          transformOrigin={{
            vertical: 'center',
            horizontal: 'left',
          }}
          slotProps={{
            paper: {
              sx: {
                ml: 0, // No overlap to prevent conflicts
                minWidth: 200,
                boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.1)',
                border: '1px solid rgba(0, 0, 0, 0.05)',
              },
            },
          }}
        >
          {item.submenu.map((nestedItem, index) => (
            <NestedMenuItem
              key={`${nestedItem.name}-${index}`}
              item={nestedItem}
              onClose={onClose}
            />
          ))}
        </Menu>
      )}
    </Box>
  )
}

const MenuItemComponent: React.FC<MenuItemComponentProps> = ({ item }) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const open = Boolean(anchorEl)

  const handleMouseEnter = (event: React.MouseEvent<HTMLElement>) => {
    if (item.submenu && item.submenu.length > 0) {
      setAnchorEl(event.currentTarget.querySelector('button'))
    }
  }

  const handleMouseLeave = (event: React.MouseEvent<HTMLElement>) => {
    // Only close if we're actually leaving the entire menu area
    const relatedTarget = event.relatedTarget as HTMLElement
    const currentTarget = event.currentTarget as HTMLElement

    // Check if we're moving to a child element (submenu)
    if (relatedTarget && currentTarget.contains(relatedTarget)) {
      return // Don't close if moving to submenu
    }

    setAnchorEl(null)
  }

  const handleClick = () => {
    if (item.onClick && (!item.submenu || item.submenu.length === 0)) {
      item.onClick()
    }
  }



  const renderButton = () => {
    // If item has a route
    if (item.to) {
      return (
        <Button
          component={NavLink}
          to={item.to}
          variant="text"
          disabled={item.disabled}


          sx={{
            textTransform: 'none',
            textDecoration: 'none',
            '&:hover': {
              backgroundColor: 'rgba(0, 0, 0, 0.04)',
            },
          }}
        >
          <Typography variant="BodyMBold" color="#000">
            {item.name}
          </Typography>
        </Button>
      )
    }

    // If item has an external href
    if (item.href) {
      return (
        <Button
          component="a"
          href={item.href}
          variant="text"
          disabled={item.disabled}


          sx={{
            textTransform: 'none',
            '&:hover': {
              backgroundColor: 'rgba(0, 0, 0, 0.04)',
            },
          }}
        >
          <Typography variant="BodyMBold" color="#000">
            {item.name}
          </Typography>
        </Button>
      )
    }

    // Default button (for onClick or submenu-only items)
    return (
      <Button
        variant="text"
        onClick={handleClick}

        disabled={item.disabled}

        sx={{
          textTransform: 'none',
          '&:hover': {
            backgroundColor: 'rgba(0, 0, 0, 0.04)',
          },
        }}
      >
        <Typography variant="BodyMBold" color="#000">
          {item.name}
        </Typography>
      </Button>
    )
  }

  const renderSubmenu = () => {
    return (
      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={() => setAnchorEl(null)}
      disableAutoFocus
      disableEnforceFocus
      disableRestoreFocus
      anchorOrigin={{
        vertical: 'bottom',
        horizontal: 'left',
      }}
      transformOrigin={{
        vertical: 'top',
        horizontal: 'left',
      }}
      slotProps={{
        paper: {
          sx: {
            mt: 0.5,
            minWidth: 200,
            boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.1)',
            border: '1px solid rgba(0, 0, 0, 0.05)',
          },
        },
      }}
    >
      {item.submenu?.map((subItem, index) => (
        <NestedMenuItem
          key={`${subItem.name}-${index}`}
          item={subItem}
          onClose={() => setAnchorEl(null)}
        />
      ))}
    </Menu>
    )
  }

  return (
    <Box
      sx={{ position: 'relative', display: 'inline-block' }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {renderButton()}
      {item.submenu && item.submenu.length > 0 && renderSubmenu()}
    </Box>
  )
}

const NavigationMenu: React.FC<NavigationMenuProps> = ({ items }) => {
  return (
    <Box sx={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
      {items.map((item, index) => (
        <MenuItemComponent key={`${item.name}-${index}`} item={item} />
      ))}
    </Box>
  )
}

export default NavigationMenu
