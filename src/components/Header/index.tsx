import React from 'react'
import { AppBar, Toolbar, Typography, Box, Avatar } from '@mui/material'
// import { extractTokenData } from '../../../auth/msalUtil'
import logoFull from '../../assets/logo-full.svg'
import { useHostStore } from '@store/hostStore'
import NavigationMenu from '@components/NavigationMenu'
import { navigationConfig } from '../../config/navigationConfig'
import palette from '../../styles/palette'

const MarcelHeader: React.FC = () => {
  const { userDetail } = useHostStore((state) => state)
  return (
    <Box>
      <AppBar position="static" color="default" sx={{ bgcolor: 'white', color: '#202020' }}>
        <Toolbar
          disableGutters
          sx={{
            paddingX: 4,
            boxShadow: '0px 2px 16px 0px rgba(10, 73, 199, 0.1)',
          }}
        >
          <Box sx={{ width: '100%', display: 'flex', justifyContent: 'space-between' }}>
            {/* Logo and Nav */}
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <img
                src={logoFull}
                alt="Logo"
                style={{
                  height: 18,
                  marginRight: '40px',
                }}
              />
              {/* <img
            src="https://marcel.ai//content-assets/content/dam/content-admin/global/assets/images/publicis-shared-platforms/Shared-Platforms/PublicisReSources.png?JuuRmyjcWMwQ4Z3AcGwvD246sSyiCsTjg4TyJzf2Gk2I1pydS1tFFGkJz7VdYg6pN9fQ"
            alt="Logo"
            style={{
              height: 60,
              borderRadius: '5px',
            }}
          /> */}
              <NavigationMenu items={navigationConfig} />
            </Box>

            {/* Actions and Avatar */}
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 1,
                px: 1,
                py: 0.5,
                borderRadius: '20px',
              }}
            >
              <Avatar
                sx={{
                  width: 32,
                  height: 32,
                  bgcolor: palette['50-Accent-1'].main,
                  color: palette['500-Accent-1'].main,
                }}
              >
                <Typography variant="BodyMBold">
                  {userDetail?.firstName[0]}
                  {userDetail?.lastName?.[0]}
                </Typography>
              </Avatar>
              <Typography variant="BodyLBold" ml={1}>
                {userDetail?.firstName} {userDetail?.lastName}
              </Typography>
            </Box>
          </Box>
        </Toolbar>
      </AppBar>
    </Box>
  )
}

export default MarcelHeader
