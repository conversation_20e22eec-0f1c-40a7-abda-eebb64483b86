import React from 'react'
import { Box } from '@mui/material'
import { keyframes } from '@mui/system'
import logoResources from '../../../assets/logo-resources.png' // Adjust the path as necessary

// Define keyframes for the pulse effect
const pulse = keyframes`
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 1;
  }
`

const RoarSplash: React.FC = () => {
  return (
    <Box
      sx={{
        height: 'calc(100vh - 64px)',
        backgroundColor: 'white',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      <Box
        component="img"
        src={logoResources}
        alt=""
        sx={{
          animation: `${pulse} 2s ease-in-out infinite`,
        }}
      />
      {/* <Typography
        variant="BodyLBold"
        sx={{
          fontSize: '8rem',
          color: '#202020',
          animation: `${pulse} 2s ease-in-out infinite`,
          letterSpacing: '4px',
        }}
      >
        roar
      </Typography> */}
    </Box>
  )
}

export default RoarSplash
