import React from 'react'
import { Box, Fade } from '@mui/material'
import { keyframes } from '@mui/system'
import logoResources from '../../../assets/logo-resources.png' // Adjust the path as necessary

// Define keyframes for the pulse effect
const pulse = keyframes`
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 1;
  }
`

interface RoarSplashProps {
  isVisible?: boolean
  onExited?: () => void
}

const RoarSplash: React.FC<RoarSplashProps> = ({
  isVisible = true,
  onExited
}) => {
  return (
    <Fade
      in={isVisible}
      timeout={{ enter: 300, exit: 600 }} // Smooth fade in/out
      onExited={onExited}
      unmountOnExit
    >
      <Box
        sx={{
          height: 'calc(100vh - 64px)',
          backgroundColor: 'white',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          zIndex: 1000, // Ensure it's above iframe content
        }}
      >
        <Box
          component="img"
          src={logoResources}
          alt=""
          sx={{
            animation: `${pulse} 2s ease-in-out infinite`,
          }}
        />
        {/* <Typography
          variant="BodyLBold"
          sx={{
            fontSize: '8rem',
            color: '#202020',
            animation: `${pulse} 2s ease-in-out infinite`,
            letterSpacing: '4px',
          }}
        >
          roar
        </Typography> */}
      </Box>
    </Fade>
  )
}

export default RoarSplash
