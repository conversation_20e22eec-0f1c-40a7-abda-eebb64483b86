import React from 'react'
import { NavLink } from 'react-router-dom'
import { Box, Typography, Grid, Card } from '@mui/material'
import { ArrowOutward } from '@mui/icons-material'

type NavChild = {
  label: string
  to: string
  gradientFrom?: string
  gradientTo?: string
  icon?: React.ReactNode
}

type NavSection = {
  label?: string
  children: NavChild[]
}

type DashboardNavigationProps = {
  items: NavSection[]
}

const DashboardNavigation: React.FC<DashboardNavigationProps> = ({ items }) => {
  return (
    <Box>
      {items.map((navSection, idx) => {
        const label = navSection.label ?? 'Untitled'

        return (
          <Box key={idx} mb={9}>
            <Typography variant="h2" mb={3}>
              {label}
            </Typography>

            <Grid container spacing={4}>
              {navSection.children.map((child, index) => (
                <Grid size={6}>
                  <Card sx={{ boxShadow: 'none', border: '1px solid rgb(230, 230, 230)' }}>
                    <Box sx={{ display: 'flex', gap: 6, alignItems: 'center' }}>
                      <Box
                        py={5}
                        px={8}
                        borderRadius={2}
                        display={'flex'}
                        justifyContent={'center'}
                        alignItems={'center'}
                        sx={{
                          background: `linear-gradient(90deg, ${child.gradientFrom || '#FFD952'} 0%, ${child.gradientTo || '#EA9809'} 100%)`,
                        }}
                      >
                        <Box display={'flex'} sx={{ background: 'rgba(0,0,0,0.07)' }} borderRadius={'50% '} p={2}>
                          {child.icon}
                        </Box>
                      </Box>

                      <Box flexGrow={1}>
                        <Typography variant="BodyLBold">{child.label}</Typography>
                      </Box>
                      <Box px={8}>
                        <ArrowOutward sx={{ fontSize: 18 }} />
                      </Box>
                    </Box>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Box>
        )
      })}
    </Box>
  )
}

export default DashboardNavigation
