import { Box } from '@mui/material'
import React, { useEffect, useState, useRef, useCallback } from 'react'
import { useHostStore } from '@store/hostStore'
import RoarSplash from '@components/Header/RoarSplash'
import { useIframePreloader } from '@hooks/useIframePreloader'

interface IFrameEnhancedProps {
  url: string
  preloadUrls?: string[] // URLs to preload for faster navigation
}

enum IFrameLoadingState {
  LOADING = 'loading',
  CONTENT_CHECKING = 'content_checking',
  LOADED = 'loaded',
  ERROR = 'error'
}

const IFrameEnhanced: React.FC<IFrameEnhancedProps> = ({ url, preloadUrls = [] }) => {
  const { loadingCount, setAppTransitioning } = useHostStore((state) => state)
  const [loadingState, setLoadingState] = useState<IFrameLoadingState>(IFrameLoadingState.LOADING)
  const [retryCount, setRetryCount] = useState(0)
  const iframeRef = useRef<HTMLIFrameElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)
  const contentCheckIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const { preloadIframe, getPreloadedIframe } = useIframePreloader()

  const MAX_RETRIES = 3
  const CONTENT_CHECK_INTERVAL = 300 // Check every 300ms for better responsiveness
  const MAX_CONTENT_CHECK_TIME = 8000 // Reduced to 8 seconds
  const FALLBACK_TIMEOUT = 12000 // Reduced to 12 seconds

  // Preload URLs when component mounts
  useEffect(() => {
    preloadUrls.forEach(preloadUrl => {
      preloadIframe(preloadUrl).catch(error => {
        console.warn(`Failed to preload ${preloadUrl}:`, error)
      })
    })
  }, [preloadUrls, preloadIframe])

  // Enhanced content checking with better heuristics
  const checkIframeContent = useCallback((): boolean => {
    try {
      const iframe = iframeRef.current
      if (!iframe || !iframe.contentWindow) {
        return false
      }

      const iframeDoc = iframe.contentDocument || iframe.contentWindow.document
      if (!iframeDoc) {
        return false
      }

      const readyState = iframeDoc.readyState
      const hasBody = iframeDoc.body !== null
      
      if (readyState !== 'complete' || !hasBody) {
        return false
      }

      // More sophisticated content detection
      const bodyContent = iframeDoc.body
      const hasVisibleContent = (
        bodyContent.children.length > 0 ||
        (bodyContent.textContent?.trim().length || 0) > 10 || // At least 10 characters
        bodyContent.querySelector('img, canvas, svg, video') !== null // Has media content
      )

      // Check if there are any loading indicators that suggest content is still loading
      const hasLoadingIndicators = (
        bodyContent.textContent?.toLowerCase().includes('loading') ||
        bodyContent.querySelector('[class*="loading"], [class*="spinner"], [id*="loading"]') !== null
      )

      const isContentReady = hasVisibleContent && !hasLoadingIndicators

      console.log('Enhanced content check:', { 
        readyState, 
        hasBody, 
        hasVisibleContent, 
        hasLoadingIndicators,
        isContentReady,
        childrenCount: bodyContent.children.length,
        textLength: bodyContent.textContent?.trim().length || 0
      })

      return isContentReady
    } catch (error) {
      // Cross-origin restrictions - use timing-based fallback
      console.log('Cannot check iframe content (cross-origin), using fallback')
      return true
    }
  }, [])

  const handleContentLoaded = useCallback(() => {
    console.log('Enhanced iframe content fully loaded for URL:', url)
    setLoadingState(IFrameLoadingState.LOADED)
    setAppTransitioning(false)
    
    // Clear intervals/timeouts
    if (contentCheckIntervalRef.current) {
      clearInterval(contentCheckIntervalRef.current)
      contentCheckIntervalRef.current = null
    }
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
      timeoutRef.current = null
    }
  }, [url, setAppTransitioning])

  const startContentChecking = useCallback(() => {
    console.log('Starting enhanced content checking for URL:', url)
    setLoadingState(IFrameLoadingState.CONTENT_CHECKING)
    
    const checkStartTime = Date.now()
    let consecutiveSuccessCount = 0
    const REQUIRED_CONSECUTIVE_SUCCESS = 2 // Require 2 consecutive successful checks
    
    contentCheckIntervalRef.current = setInterval(() => {
      const hasContent = checkIframeContent()
      const elapsedTime = Date.now() - checkStartTime
      
      if (hasContent) {
        consecutiveSuccessCount++
        if (consecutiveSuccessCount >= REQUIRED_CONSECUTIVE_SUCCESS) {
          handleContentLoaded()
        }
      } else {
        consecutiveSuccessCount = 0
      }
      
      if (elapsedTime > MAX_CONTENT_CHECK_TIME) {
        console.log('Enhanced content check timeout reached, assuming loaded')
        handleContentLoaded()
      }
    }, CONTENT_CHECK_INTERVAL)
  }, [url, checkIframeContent, handleContentLoaded])

  const handleIframeLoad = useCallback(() => {
    console.log('Enhanced iframe onLoad event fired for URL:', url)
    
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
      timeoutRef.current = null
    }

    startContentChecking()
  }, [url, startContentChecking])

  const handleIframeError = useCallback(() => {
    console.error('Enhanced iframe failed to load:', url)
    setLoadingState(IFrameLoadingState.ERROR)
    
    if (retryCount < MAX_RETRIES) {
      console.log(`Retrying enhanced iframe load (${retryCount + 1}/${MAX_RETRIES})`)
      setRetryCount(prev => prev + 1)
      
      if (iframeRef.current) {
        const separator = url.includes('?') ? '&' : '?'
        iframeRef.current.src = `${url}${separator}_retry=${retryCount + 1}&_t=${Date.now()}`
      }
    } else {
      console.error('Max retries reached for enhanced iframe load')
      setAppTransitioning(false)
    }
  }, [url, retryCount, setAppTransitioning])

  // Enhanced URL change handling with preload support
  useEffect(() => {
    console.log('Enhanced iframe URL changed to:', url)
    setLoadingState(IFrameLoadingState.LOADING)
    setAppTransitioning(true)
    setRetryCount(0)

    // Clear existing intervals/timeouts
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
      timeoutRef.current = null
    }
    if (contentCheckIntervalRef.current) {
      clearInterval(contentCheckIntervalRef.current)
      contentCheckIntervalRef.current = null
    }

    // Try to use preloaded iframe first
    const preloadedIframe = getPreloadedIframe(url)
    if (preloadedIframe && containerRef.current) {
      console.log('Using preloaded iframe for faster loading')
      
      // Replace current iframe with preloaded one
      if (iframeRef.current) {
        containerRef.current.removeChild(iframeRef.current)
      }
      
      containerRef.current.appendChild(preloadedIframe)
      iframeRef.current = preloadedIframe
      
      // Preloaded iframe should be ready, but still do a quick check
      setTimeout(() => {
        if (loadingState !== IFrameLoadingState.LOADED) {
          handleContentLoaded()
        }
      }, 500)
    } else {
      // Set absolute fallback timeout
      timeoutRef.current = setTimeout(() => {
        console.log('Enhanced absolute fallback timeout reached')
        if (loadingState !== IFrameLoadingState.LOADED) {
          handleContentLoaded()
        }
      }, FALLBACK_TIMEOUT)
    }
  }, [url, setAppTransitioning, handleContentLoaded, loadingState, getPreloadedIframe])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
      if (contentCheckIntervalRef.current) {
        clearInterval(contentCheckIntervalRef.current)
      }
    }
  }, [])

  const isIframeLoading = loadingState !== IFrameLoadingState.LOADED
  const shouldShowLoading = loadingCount > 0 || isIframeLoading

  console.log('Enhanced loading states:', { 
    loadingCount, 
    loadingState, 
    isIframeLoading, 
    shouldShowLoading, 
    retryCount,
    url 
  })

  return shouldShowLoading ? (
    <RoarSplash />
  ) : (
    <Box ref={containerRef}>
      <iframe
        ref={iframeRef}
        src={url}
        title="Marcel"
        style={{ height: 'calc(100vh - 64px)', width: '100%', border: 'none' }}
        onLoad={handleIframeLoad}
        onError={handleIframeError}
      />
    </Box>
  )
}

export default IFrameEnhanced
