import { Box } from '@mui/material'
import React, { useEffect, useState, useRef } from 'react'
import { useHostStore } from '@store/hostStore'
import RoarSplash from '@components/Header/RoarSplash'

interface IFrameProps {
  url: string
}

const IFrame: React.FC<IFrameProps> = ({ url }) => {
  const { loadingCount, setAppTransitioning } = useHostStore((state) => state)
  const [isIframeLoading, setIsIframeLoading] = useState(true)
  const iframeRef = useRef<HTMLIFrameElement>(null)
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)
  const contentCheckIntervalRef = useRef<NodeJS.Timeout | null>(null)

  // Check if iframe has meaningful content - simplified version
  const checkIframeContent = (): boolean => {
    try {
      const iframe = iframeRef.current
      if (!iframe || !iframe.contentWindow) {
        return false
      }

      const iframeDoc = iframe.contentDocument || iframe.contentWindow.document
      if (!iframeDoc) {
        return false
      }

      // Simple check: document is complete and has body with some content
      const isComplete = iframeDoc.readyState === 'complete'
      const hasBody = iframeDoc.body !== null
      const hasContent = hasBody && (
        iframeDoc.body.children.length > 0 ||
        (iframeDoc.body.textContent?.trim().length || 0) > 10
      )

      console.log('Simple content check:', { isComplete, hasBody, hasContent, url })
      return isComplete && hasBody && hasContent
    } catch (error) {
      // Cross-origin - can't check content, assume loaded after onLoad
      console.log('Cross-origin iframe, assuming loaded:', url)
      return true
    }
  }

  // Simple iframe load handler with content checking
  const handleIframeLoad = () => {
    console.log('Iframe onLoad event fired for URL:', url)

    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
      timeoutRef.current = null
    }
    if (contentCheckIntervalRef.current) {
      clearInterval(contentCheckIntervalRef.current)
      contentCheckIntervalRef.current = null
    }

    // Try to check content immediately
    if (checkIframeContent()) {
      console.log('Content ready immediately')
      setIsIframeLoading(false)
      setAppTransitioning(false)
      return
    }

    // If content not ready, start checking periodically
    let checkCount = 0
    const maxChecks = 20 // Check for up to 10 seconds (500ms * 20)

    contentCheckIntervalRef.current = setInterval(() => {
      checkCount++

      if (checkIframeContent()) {
        console.log('Content ready after checking')
        setIsIframeLoading(false)
        setAppTransitioning(false)
        if (contentCheckIntervalRef.current) {
          clearInterval(contentCheckIntervalRef.current)
          contentCheckIntervalRef.current = null
        }
      } else if (checkCount >= maxChecks) {
        console.log('Content check timeout, assuming loaded')
        setIsIframeLoading(false)
        setAppTransitioning(false)
        if (contentCheckIntervalRef.current) {
          clearInterval(contentCheckIntervalRef.current)
          contentCheckIntervalRef.current = null
        }
      }
    }, 500)
  }

  // Reset loading state when URL changes
  useEffect(() => {
    console.log('URL changed to:', url)
    setIsIframeLoading(true)
    setAppTransitioning(true)

    // Clear any existing intervals/timeouts
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
      timeoutRef.current = null
    }
    if (contentCheckIntervalRef.current) {
      clearInterval(contentCheckIntervalRef.current)
      contentCheckIntervalRef.current = null
    }

    // Set absolute fallback timeout (reduced from 15s to 8s)
    timeoutRef.current = setTimeout(() => {
      console.log('Absolute fallback timeout reached, forcing load complete')
      setIsIframeLoading(false)
      setAppTransitioning(false)
    }, 8000)
  }, [url, setAppTransitioning])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
      if (contentCheckIntervalRef.current) {
        clearInterval(contentCheckIntervalRef.current)
      }
    }
  }, [])

  // Show RoarSplash if either host is loading or iframe is loading
  const shouldShowLoading = loadingCount > 0 || isIframeLoading

  console.log('Loading states:', { loadingCount, isIframeLoading, shouldShowLoading, url })

  return shouldShowLoading ? (
    <RoarSplash />
  ) : (
    <Box>
      <iframe
        ref={iframeRef}
        src={url}
        title="Marcel"
        style={{ height: 'calc(100vh - 64px)', width: '100%', border: 'none' }}
        onLoad={handleIframeLoad}
      />
    </Box>
  )
}

export default IFrame
