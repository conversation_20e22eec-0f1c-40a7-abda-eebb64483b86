import { Box } from '@mui/material'
import React, { useEffect, useState, useRef } from 'react'
import { useHostStore } from '@store/hostStore'
import RoarSplash from '@components/Header/RoarSplash'

interface IFrameProps {
  url: string
}

const IFrame: React.FC<IFrameProps> = ({ url }) => {
  const { loadingCount, setAppTransitioning } = useHostStore((state) => state)
  const [isIframeLoading, setIsIframeLoading] = useState(true)
  const iframeRef = useRef<HTMLIFrameElement>(null)
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)
  const contentCheckIntervalRef = useRef<NodeJS.Timeout | null>(null)

  // Check if iframe has meaningful content - improved version
  const checkIframeContent = (): boolean => {
    try {
      const iframe = iframeRef.current
      if (!iframe || !iframe.contentWindow) {
        return false
      }

      const iframeDoc = iframe.contentDocument || iframe.contentWindow.document
      if (!iframeDoc) {
        return false
      }

      // Check if document is complete and has body
      const isComplete = iframeDoc.readyState === 'complete'
      const hasBody = iframeDoc.body !== null

      if (!isComplete || !hasBody) {
        return false
      }

      const bodyContent = iframeDoc.body
      const textContent = bodyContent.textContent?.trim() || ''

      // Check if there's meaningful content
      const hasVisibleContent = (
        bodyContent.children.length > 0 ||
        textContent.length > 10
      )

      // Check for loading indicators with various formats
      // This covers all variants: "Loading", "loading", "Loading...", "Loading....", "LOADING", etc.
      const normalizedText = textContent.toLowerCase()
      const hasLoadingText = (
        normalizedText.includes('loading') ||
        normalizedText.includes('please wait') ||
        normalizedText.includes('wait') && normalizedText.length < 100 || // Expanded for longer wait messages
        normalizedText.includes('initializing') ||
        normalizedText.includes('processing')
      )

      // Also check for common loading class names and IDs (more comprehensive)
      const hasLoadingElements = (
        bodyContent.querySelector('[class*="loading" i], [class*="spinner" i], [id*="loading" i]') !== null ||
        bodyContent.querySelector('[class*="load" i], [class*="spin" i]') !== null ||
        bodyContent.querySelector('.loader, .loading-spinner, .progress') !== null
      )

      // More strict content readiness check
      // Content is ready ONLY if:
      // 1. We have substantial visible content (not just loading text)
      // 2. NO loading text is present
      // 3. NO loading elements are present
      // 4. Content length is reasonable (not just a loading message)
      const hasSubstantialContent = (
        bodyContent.children.length > 1 || // Multiple elements
        (textContent.length > 50 && !hasLoadingText) // Substantial text that's not loading-related
      )

      const isContentReady = hasSubstantialContent && !hasLoadingText && !hasLoadingElements

      console.log('Enhanced content check:', {
        isComplete,
        hasBody,
        hasVisibleContent,
        hasSubstantialContent,
        hasLoadingText,
        hasLoadingElements,
        isContentReady,
        textContent: textContent.substring(0, 150), // First 150 chars for debugging
        textLength: textContent.length,
        childrenCount: bodyContent.children.length,
        url
      })

      return isContentReady
    } catch (error) {
      // Cross-origin - can't check content, assume loaded after onLoad
      console.log('Cross-origin iframe, assuming loaded:', url)
      return true
    }
  }

  // Enhanced iframe load handler with better fallback logic
  const handleIframeLoad = () => {
    console.log('Iframe onLoad event fired for URL:', url)

    // Clear any existing timeout/interval
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
      timeoutRef.current = null
    }
    if (contentCheckIntervalRef.current) {
      clearInterval(contentCheckIntervalRef.current)
      contentCheckIntervalRef.current = null
    }

    // For cross-origin iframes, we can't check content, so use a shorter timeout
    let isCrossOrigin = false
    try {
      const iframe = iframeRef.current
      if (iframe && iframe.contentWindow) {
        // Try to access the document - this will throw for cross-origin
        const doc = iframe.contentDocument || iframe.contentWindow.document
        if (!doc) {
          isCrossOrigin = true
        }
      }
    } catch (error) {
      isCrossOrigin = true
    }

    if (isCrossOrigin) {
      console.log('Cross-origin iframe detected, using timeout fallback')
      // For cross-origin, wait longer since we can't check for loading text
      timeoutRef.current = setTimeout(() => {
        console.log('Cross-origin timeout complete, showing content')
        setIsIframeLoading(false)
        setAppTransitioning(false)
      }, 4000) // 4 seconds for cross-origin to ensure loading is complete
      return
    }

    // Try to check content immediately for same-origin iframes
    if (checkIframeContent()) {
      console.log('Content ready immediately')
      setIsIframeLoading(false)
      setAppTransitioning(false)
      return
    }

    // If content not ready, start checking periodically with consecutive success requirement
    let checkCount = 0
    let consecutiveSuccessCount = 0
    const maxChecks = 20 // Check for up to 10 seconds (500ms * 20)
    const requiredConsecutiveSuccess = 3 // Require 3 consecutive successful checks

    contentCheckIntervalRef.current = setInterval(() => {
      checkCount++

      if (checkIframeContent()) {
        consecutiveSuccessCount++
        console.log(`Content check success ${consecutiveSuccessCount}/${requiredConsecutiveSuccess}`)

        if (consecutiveSuccessCount >= requiredConsecutiveSuccess) {
          console.log('Content ready after consecutive successful checks')
          setIsIframeLoading(false)
          setAppTransitioning(false)
          if (contentCheckIntervalRef.current) {
            clearInterval(contentCheckIntervalRef.current)
            contentCheckIntervalRef.current = null
          }
        }
      } else {
        // Reset consecutive count if check fails
        consecutiveSuccessCount = 0
        console.log('Content not ready, resetting consecutive count')
      }

      if (checkCount >= maxChecks) {
        console.log('Content check timeout, assuming loaded')
        setIsIframeLoading(false)
        setAppTransitioning(false)
        if (contentCheckIntervalRef.current) {
          clearInterval(contentCheckIntervalRef.current)
          contentCheckIntervalRef.current = null
        }
      }
    }, 500)
  }

  // Reset loading state when URL changes
  useEffect(() => {
    console.log('URL changed to:', url)
    setIsIframeLoading(true)
    setAppTransitioning(true)

    // Clear any existing intervals/timeouts
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
      timeoutRef.current = null
    }
    if (contentCheckIntervalRef.current) {
      clearInterval(contentCheckIntervalRef.current)
      contentCheckIntervalRef.current = null
    }

    // Set absolute fallback timeout (increased to handle stricter content checking)
    timeoutRef.current = setTimeout(() => {
      console.log('Absolute fallback timeout reached, forcing load complete')
      setIsIframeLoading(false)
      setAppTransitioning(false)
    }, 12000) // 12 seconds to allow for proper content loading
  }, [url, setAppTransitioning])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
      if (contentCheckIntervalRef.current) {
        clearInterval(contentCheckIntervalRef.current)
      }
    }
  }, [])

  // Show RoarSplash if either host is loading or iframe is loading
  const shouldShowLoading = loadingCount > 0 || isIframeLoading

  console.log('Loading states:', { loadingCount, isIframeLoading, shouldShowLoading, url })

  return shouldShowLoading ? (
    <RoarSplash />
  ) : (
    <Box>
      <iframe
        ref={iframeRef}
        src={url}
        title="Marcel"
        style={{ height: 'calc(100vh - 64px)', width: '100%', border: 'none' }}
        onLoad={handleIframeLoad}
      />
    </Box>
  )
}

export default IFrame
