import { Box } from '@mui/material'
import React, { useEffect, useState, useRef, useCallback } from 'react'
import { useHostStore } from '@store/hostStore'
import RoarSplash from '@components/Header/RoarSplash'

interface IFrameProps {
  url: string
}

enum IFrameLoadingState {
  LOADING = 'loading',
  CONTENT_CHECKING = 'content_checking',
  LOADED = 'loaded',
  ERROR = 'error'
}

const IFrame: React.FC<IFrameProps> = ({ url }) => {
  const { loadingCount, setAppTransitioning } = useHostStore((state) => state)
  const [loadingState, setLoadingState] = useState<IFrameLoadingState>(IFrameLoadingState.LOADING)
  const [retryCount, setRetryCount] = useState(0)
  const iframeRef = useRef<HTMLIFrameElement>(null)
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)
  const contentCheckIntervalRef = useRef<NodeJS.Timeout | null>(null)

  const MAX_RETRIES = 3
  const CONTENT_CHECK_INTERVAL = 500 // Check every 500ms
  const MAX_CONTENT_CHECK_TIME = 10000 // Stop checking after 10 seconds
  const FALLBACK_TIMEOUT = 15000 // Absolute fallback after 15 seconds

  // Check if iframe has meaningful content
  const checkIframeContent = useCallback((): boolean => {
    try {
      const iframe = iframeRef.current
      if (!iframe || !iframe.contentWindow) {
        return false
      }

      // Try to access the iframe document
      const iframeDoc = iframe.contentDocument || iframe.contentWindow.document
      if (!iframeDoc) {
        return false
      }

      // Check if document has loaded and has content
      const readyState = iframeDoc.readyState
      const hasBody = iframeDoc.body !== null
      const hasContent = iframeDoc.body && (
        iframeDoc.body.children.length > 0 ||
        (iframeDoc.body.textContent?.trim().length || 0) > 0
      )

      console.log('Content check:', { readyState, hasBody, hasContent, childrenCount: iframeDoc.body?.children.length })

      return readyState === 'complete' && hasBody && hasContent
    } catch (error) {
      // Cross-origin restrictions - assume content is loaded if we can't check
      console.log('Cannot check iframe content (likely cross-origin):', error)
      return true
    }
  }, [])

  // Handle successful content load
  const handleContentLoaded = useCallback(() => {
    console.log('Iframe content fully loaded for URL:', url)
    setLoadingState(IFrameLoadingState.LOADED)
    setAppTransitioning(false)

    // Clear any running intervals/timeouts
    if (contentCheckIntervalRef.current) {
      clearInterval(contentCheckIntervalRef.current)
      contentCheckIntervalRef.current = null
    }
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
      timeoutRef.current = null
    }
  }, [url, setAppTransitioning])

  // Start content checking process
  const startContentChecking = useCallback(() => {
    console.log('Starting content checking for URL:', url)
    setLoadingState(IFrameLoadingState.CONTENT_CHECKING)

    let checkStartTime = Date.now()

    contentCheckIntervalRef.current = setInterval(() => {
      const hasContent = checkIframeContent()
      const elapsedTime = Date.now() - checkStartTime

      if (hasContent) {
        handleContentLoaded()
      } else if (elapsedTime > MAX_CONTENT_CHECK_TIME) {
        console.log('Content check timeout reached, assuming loaded')
        handleContentLoaded()
      }
    }, CONTENT_CHECK_INTERVAL)
  }, [url, checkIframeContent, handleContentLoaded])

  // Handle iframe load event
  const handleIframeLoad = useCallback(() => {
    console.log('Iframe onLoad event fired for URL:', url)

    // Clear any existing timeouts
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
      timeoutRef.current = null
    }

    // Start checking for actual content
    startContentChecking()
  }, [url, startContentChecking])

  // Handle iframe load error
  const handleIframeError = useCallback(() => {
    console.error('Iframe failed to load:', url)
    setLoadingState(IFrameLoadingState.ERROR)

    if (retryCount < MAX_RETRIES) {
      console.log(`Retrying iframe load (${retryCount + 1}/${MAX_RETRIES})`)
      setRetryCount(prev => prev + 1)
      // Force iframe reload by changing src
      if (iframeRef.current) {
        const separator = url.includes('?') ? '&' : '?'
        iframeRef.current.src = `${url}${separator}_retry=${retryCount + 1}&_t=${Date.now()}`
      }
    } else {
      console.error('Max retries reached for iframe load')
      setAppTransitioning(false)
    }
  }, [url, retryCount, setAppTransitioning])

  // Reset loading state when URL changes
  useEffect(() => {
    console.log('URL changed to:', url)
    setLoadingState(IFrameLoadingState.LOADING)
    setAppTransitioning(true)
    setRetryCount(0)

    // Clear any existing intervals/timeouts
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
      timeoutRef.current = null
    }
    if (contentCheckIntervalRef.current) {
      clearInterval(contentCheckIntervalRef.current)
      contentCheckIntervalRef.current = null
    }

    // Set absolute fallback timeout
    timeoutRef.current = setTimeout(() => {
      console.log('Absolute fallback timeout reached, forcing load complete')
      if (loadingState !== IFrameLoadingState.LOADED) {
        handleContentLoaded()
      }
    }, FALLBACK_TIMEOUT)
  }, [url, setAppTransitioning, handleContentLoaded, loadingState])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
      if (contentCheckIntervalRef.current) {
        clearInterval(contentCheckIntervalRef.current)
      }
    }
  }, [])

  // Determine if we should show loading
  const isIframeLoading = loadingState !== IFrameLoadingState.LOADED
  const shouldShowLoading = loadingCount > 0 || isIframeLoading

  console.log('Loading states:', {
    loadingCount,
    loadingState,
    isIframeLoading,
    shouldShowLoading,
    retryCount,
    url
  })

  return shouldShowLoading ? (
    <RoarSplash />
  ) : (
    <Box>
      <iframe
        ref={iframeRef}
        src={url}
        title="Marcel"
        style={{ height: 'calc(100vh - 64px)', width: '100%', border: 'none' }}
        onLoad={handleIframeLoad}
        onError={handleIframeError}
      />
    </Box>
  )
}

export default IFrame
