# IFrame Components

This directory contains enhanced iframe loading components with improved performance and user experience.

## Components

### IFrame.tsx (Original)
The original iframe component with basic timeout-based loading detection.

### IFrameEnhanced.tsx (Recommended)
Enhanced iframe component with the following improvements:

#### Features
- **Content-based loading detection**: Checks if iframe actually has meaningful content loaded
- **Intelligent content checking**: Detects loading indicators and waits for them to disappear
- **Preloading support**: Can preload iframes for faster navigation
- **Retry mechanism**: Automatically retries failed loads up to 3 times
- **Better error handling**: Graceful handling of cross-origin restrictions
- **Performance optimizations**: Reduced timeouts and more responsive checking

#### Loading States
- `LOADING`: Initial state when iframe starts loading
- `CONTENT_CHECKING`: Actively checking if content is ready
- `LOADED`: Content is fully loaded and ready
- `ERROR`: Loading failed (with retry logic)

#### Content Detection Logic
1. Checks if iframe document is complete
2. Verifies there's actual visible content (not just empty body)
3. Looks for loading indicators and waits for them to disappear
4. Requires consecutive successful checks for stability
5. Falls back to timeout if cross-origin restrictions prevent content checking

## Hooks

### useIframePreloader.ts
A singleton-based preloader that can cache iframes for faster navigation:

- **Preload management**: Preloads up to 3 iframes in background
- **Smart cleanup**: Removes oldest preloaded iframes when limit reached
- **Memory efficient**: Properly cleans up DOM elements
- **Timeout handling**: Prevents indefinite preloading

## Configuration

### iframeConfig.ts
Centralized configuration for iframe apps:

- **URL management**: Handles base URLs and employee code injection
- **Preload configuration**: Defines which URLs to preload for each app
- **Timeout settings**: Custom timeouts per app type

## Usage

### Basic Usage
```tsx
import IFrameEnhanced from '@components/IFrameEnhanced'

const MyApp = () => {
  return <IFrameEnhanced url="https://example.com" />
}
```

### With Preloading
```tsx
import IFrameEnhanced from '@components/IFrameEnhanced'

const MyApp = () => {
  return (
    <IFrameEnhanced 
      url="https://example.com" 
      preloadUrls={['https://related-app.com', 'https://another-app.com']}
    />
  )
}
```

### Using Configuration
```tsx
import IFrameEnhanced from '@components/IFrameEnhanced'
import { getIframeConfig } from '@config/iframeConfig'

const TimesheetApp = () => {
  const { employeeCode } = useHostStore()
  const config = getIframeConfig('timesheet', employeeCode)
  
  return <IFrameEnhanced url={config.url} preloadUrls={config.preloadUrls} />
}
```

## Performance Benefits

1. **Faster perceived loading**: Content-based detection shows content as soon as it's ready
2. **Preloading**: Related apps load instantly when navigated to
3. **Reduced timeouts**: More responsive with shorter, smarter timeouts
4. **Better retry logic**: Handles temporary network issues gracefully
5. **Memory management**: Efficient cleanup prevents memory leaks

## Browser Compatibility

- **Same-origin iframes**: Full content detection and optimization
- **Cross-origin iframes**: Falls back to timing-based detection with improved heuristics
- **All modern browsers**: Uses standard DOM APIs with proper fallbacks
