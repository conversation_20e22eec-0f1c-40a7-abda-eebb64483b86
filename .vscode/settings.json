{"editor.tabSize": 2, "editor.insertSpaces": true, "explorer.compactFolders": false, "breadcrumbs.icons": true, "workbench.editor.labelFormat": "medium", "workbench.editor.decorations.badges": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"]}