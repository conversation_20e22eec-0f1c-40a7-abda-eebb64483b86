{
  "compilerOptions": {
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",
    "incremental": true,
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": [
      "ES2020",
      "DOM",
      "DOM.Iterable"
    ],
    "module": "ESNext",
    "skipLibCheck": true,
    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "isolatedModules": true,
    "moduleDetection": "force",
    "noEmit": true,
    "jsx": "react-jsx",
    /* Linting */
    "strict": true,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noFallthroughCasesInSwitch": true,
    // "noUncheckedSideEffectImports": true
    "baseUrl": "./src",
    "paths": {
      "@pages/*": [
        "pages/*"
      ],
      "@components/*": [
        "components/*"
      ],
      "@hooks/*": [
        "hooks/*"
      ],
      "@config/*": [
          "config/*"
        ],
        "@utils/*": [
          "utils/*"
        ],
        "@store/*": [
          "store/*"
        ],
    }
  },
  "include": [
    "src"
  ]
}
