{"name": "management", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:dev": "vite --mode development", "dev:qa": "vite --mode qa", "dev:uat": "vite --mode uat", "dev:prod": "vite --mode production", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "codegen": "graphql-codegen --config codegen.ts", "lint:fix": "eslint . --fix"}, "dependencies": {"@apollo/client": "^3.13.1", "@azure/msal-browser": "^4.4.0", "@azure/msal-react": "^3.0.5", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.2.0", "@mui/material": "^7.2.0", "@mui/system": "^7.2.0", "@mui/x-data-grid": "^7.27.0", "@mui/x-date-pickers": "^7.27.0", "dayjs": "^1.11.13", "file-saver": "^2.0.5", "graphql": "^16.10.0", "i18next": "^24.2.2", "lodash": "^4.17.21", "lodash.debounce": "^4.0.8", "react": "^19.0.0", "react-dom": "^19.0.0", "react-i18next": "^15.4.1", "react-router-dom": "^7.2.0", "react-toastify": "^11.0.5", "recharts": "^2.15.1", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.19.0", "@graphql-codegen/cli": "5.0.2", "@graphql-codegen/client-preset": "4.3.3", "@graphql-codegen/introspection": "4.0.3", "@graphql-codegen/typescript": "^4.0.9", "@graphql-codegen/typescript-operations": "^4.2.3", "@types/file-saver": "^2.0.7", "@types/lodash": "^4.17.16", "@types/node": "^22.13.4", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.19.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "prettier": "^3.5.1", "typescript": "~5.7.2", "typescript-eslint": "^8.22.0", "vite": "^6.1.0"}}