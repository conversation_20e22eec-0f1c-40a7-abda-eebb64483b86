{"name": "management", "version": "0.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "management", "version": "0.0.0", "dependencies": {"@apollo/client": "^3.13.1", "@azure/msal-browser": "^4.4.0", "@azure/msal-react": "^3.0.5", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.4.5", "@mui/material": "^6.4.5", "@mui/x-data-grid": "^7.27.0", "@mui/x-date-pickers": "^7.27.0", "dayjs": "^1.11.13", "file-saver": "^2.0.5", "graphql": "^16.10.0", "i18next": "^24.2.2", "lodash": "^4.17.21", "lodash.debounce": "^4.0.8", "react": "^19.0.0", "react-dom": "^19.0.0", "react-i18next": "^15.4.1", "react-router-dom": "^7.2.0", "react-toastify": "^11.0.5", "recharts": "^2.15.1", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.19.0", "@graphql-codegen/cli": "5.0.2", "@graphql-codegen/client-preset": "4.3.3", "@graphql-codegen/introspection": "4.0.3", "@graphql-codegen/typescript": "^4.0.9", "@graphql-codegen/typescript-operations": "^4.2.3", "@types/file-saver": "^2.0.7", "@types/lodash": "^4.17.16", "@types/node": "^22.13.4", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.19.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "prettier": "^3.5.1", "typescript": "~5.7.2", "typescript-eslint": "^8.22.0", "vite": "^6.1.0"}}, "node_modules/@ampproject/remapping": {"version": "2.3.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@ampproject/remapping/-/remapping-2.3.0.tgz", "integrity": "sha1-7UQbb6YAByUgzhi0PSyMyMrsx/Q=", "dev": true, "dependencies": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.24"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@apollo/client": {"version": "3.13.8", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@apollo/client/-/client-3.13.8.tgz", "integrity": "sha1-7x1JpbE0xppV4/E3FkqOMjrvTio=", "dependencies": {"@graphql-typed-document-node/core": "^3.1.1", "@wry/caches": "^1.0.0", "@wry/equality": "^0.5.6", "@wry/trie": "^0.5.0", "graphql-tag": "^2.12.6", "hoist-non-react-statics": "^3.3.2", "optimism": "^0.18.0", "prop-types": "^15.7.2", "rehackt": "^0.1.0", "symbol-observable": "^4.0.0", "ts-invariant": "^0.10.3", "tslib": "^2.3.0", "zen-observable-ts": "^1.2.5"}, "peerDependencies": {"graphql": "^15.0.0 || ^16.0.0", "graphql-ws": "^5.5.5 || ^6.0.3", "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || >=19.0.0-rc", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0 || >=19.0.0-rc", "subscriptions-transport-ws": "^0.9.0 || ^0.11.0"}, "peerDependenciesMeta": {"graphql-ws": {"optional": true}, "react": {"optional": true}, "react-dom": {"optional": true}, "subscriptions-transport-ws": {"optional": true}}}, "node_modules/@ardatan/relay-compiler": {"version": "12.0.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@ardatan/relay-compiler/-/relay-compiler-12.0.3.tgz", "integrity": "sha1-pggkZy2n987yo4ee2DOxINKS+gg=", "dev": true, "dependencies": {"@babel/generator": "^7.26.10", "@babel/parser": "^7.26.10", "@babel/runtime": "^7.26.10", "chalk": "^4.0.0", "fb-watchman": "^2.0.0", "immutable": "~3.7.6", "invariant": "^2.2.4", "nullthrows": "^1.1.1", "relay-runtime": "12.0.0", "signedsource": "^1.0.0"}, "bin": {"relay-compiler": "bin/relay-compiler"}, "peerDependencies": {"graphql": "*"}}, "node_modules/@azure/msal-browser": {"version": "4.14.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@azure/msal-browser/-/msal-browser-4.14.0.tgz", "integrity": "sha1-D53VjXsPAPDfht5fAICtTG+xXq0=", "dependencies": {"@azure/msal-common": "15.8.0"}, "engines": {"node": ">=0.8.0"}}, "node_modules/@azure/msal-common": {"version": "15.8.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@azure/msal-common/-/msal-common-15.8.0.tgz", "integrity": "sha1-ql8Eqp+Zmo8Asz/PdbNb3EnXz18=", "engines": {"node": ">=0.8.0"}}, "node_modules/@azure/msal-react": {"version": "3.0.14", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@azure/msal-react/-/msal-react-3.0.14.tgz", "integrity": "sha1-tPAvT+nYEWiGx2h87cLIwPnOFR8=", "engines": {"node": ">=10"}, "peerDependencies": {"@azure/msal-browser": "^4.14.0", "react": "^16.8.0 || ^17 || ^18 || ^19"}}, "node_modules/@babel/code-frame": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@babel/code-frame/-/code-frame-7.27.1.tgz", "integrity": "sha1-IA9xXmbVKiOyIalDVTSpHME61b4=", "dependencies": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/compat-data": {"version": "7.28.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@babel/compat-data/-/compat-data-7.28.0.tgz", "integrity": "sha1-n8b9WMKmoVJDzROYMiSWg5IHB5A=", "dev": true, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/core": {"version": "7.28.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@babel/core/-/core-7.28.0.tgz", "integrity": "sha1-VdrYCNW/NEWhCO78iOo/3wNHSaQ=", "dev": true, "dependencies": {"@ampproject/remapping": "^2.2.0", "@babel/code-frame": "^7.27.1", "@babel/generator": "^7.28.0", "@babel/helper-compilation-targets": "^7.27.2", "@babel/helper-module-transforms": "^7.27.3", "@babel/helpers": "^7.27.6", "@babel/parser": "^7.28.0", "@babel/template": "^7.27.2", "@babel/traverse": "^7.28.0", "@babel/types": "^7.28.0", "convert-source-map": "^2.0.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.2", "json5": "^2.2.3", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/babel"}}, "node_modules/@babel/core/node_modules/convert-source-map": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/convert-source-map/-/convert-source-map-2.0.0.tgz", "integrity": "sha1-S1YPZJ/E6RjdCrdc9JYei8iC2Co=", "dev": true}, "node_modules/@babel/generator": {"version": "7.28.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@babel/generator/-/generator-7.28.0.tgz", "integrity": "sha1-nML3vW6wVNd9xmwmZBSKDFEYrNI=", "dependencies": {"@babel/parser": "^7.28.0", "@babel/types": "^7.28.0", "@jridgewell/gen-mapping": "^0.3.12", "@jridgewell/trace-mapping": "^0.3.28", "jsesc": "^3.0.2"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-compilation-targets": {"version": "7.27.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz", "integrity": "sha1-RqD276uAjVHSnOloWN0Qzocycz0=", "dev": true, "dependencies": {"@babel/compat-data": "^7.27.2", "@babel/helper-validator-option": "^7.27.1", "browserslist": "^4.24.0", "lru-cache": "^5.1.1", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-globals": {"version": "7.28.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@babel/helper-globals/-/helper-globals-7.28.0.tgz", "integrity": "sha1-uUMN8qpOF7woZl6t6uiqHZheZnQ=", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-imports": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz", "integrity": "sha1-fvdpoyPiZV4SZnO7bS1pE7vq0gQ=", "dependencies": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-transforms": {"version": "7.27.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@babel/helper-module-transforms/-/helper-module-transforms-7.27.3.tgz", "integrity": "sha1-2wu8+6WAL573hwcFp++HiFCO3gI=", "dev": true, "dependencies": {"@babel/helper-module-imports": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1", "@babel/traverse": "^7.27.3"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha1-3bL4dlNP+AE+bCspm/TTmzxR1Ew=", "dev": true, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-string-parser": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz", "integrity": "sha1-VNp5YJerGc5n7Z+ItHuy7Ek2doc=", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-identifier": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz", "integrity": "sha1-pwVNzBRaln3U3I/uhFpXwTFsnfg=", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-option": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz", "integrity": "sha1-+lL1sefbGrBJRFtCHERxMDiXcC8=", "dev": true, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helpers": {"version": "7.27.6", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@babel/helpers/-/helpers-7.27.6.tgz", "integrity": "sha1-ZFb+0VsstmnS0fq+hLZrNJkdgSw=", "dev": true, "dependencies": {"@babel/template": "^7.27.2", "@babel/types": "^7.27.6"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/parser": {"version": "7.28.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@babel/parser/-/parser-7.28.0.tgz", "integrity": "sha1-l5gp+6tRop4TkB5agHE9vLhAgl4=", "dependencies": {"@babel/types": "^7.28.0"}, "bin": {"parser": "bin/babel-parser.js"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@babel/plugin-syntax-import-assertions": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-7.27.1.tgz", "integrity": "sha1-iIlK79KwO17mrRVip8jhWHSWrs0=", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-react-jsx-self": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.27.1.tgz", "integrity": "sha1-r2eNhQas9SxXfKxz/3/mYVyF/JI=", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-react-jsx-source": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.27.1.tgz", "integrity": "sha1-3P4sJAlLt1e/c5YDdOfFXkNPGfA=", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/runtime": {"version": "7.27.6", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@babel/runtime/-/runtime-7.27.6.tgz", "integrity": "sha1-7EBwoE12uujduxB3C6VXFKQXt8Y=", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/template": {"version": "7.27.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@babel/template/-/template-7.27.2.tgz", "integrity": "sha1-+njO7TxOe2Pr9ss55YUvykX2gJ0=", "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/parser": "^7.27.2", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/traverse": {"version": "7.28.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@babel/traverse/-/traverse-7.28.0.tgz", "integrity": "sha1-UYqhEzWbBiBCN54zPbGDgLU340s=", "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/generator": "^7.28.0", "@babel/helper-globals": "^7.28.0", "@babel/parser": "^7.28.0", "@babel/template": "^7.27.2", "@babel/types": "^7.28.0", "debug": "^4.3.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/types": {"version": "7.28.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@babel/types/-/types-7.28.0.tgz", "integrity": "sha1-L9AVmm3HNTkzkgxDE2M1qbJk2VA=", "dependencies": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@emotion/babel-plugin": {"version": "11.13.5", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@emotion/babel-plugin/-/babel-plugin-11.13.5.tgz", "integrity": "sha1-6rjWXb3tdODs/SjcIY51YHxOe8A=", "dependencies": {"@babel/helper-module-imports": "^7.16.7", "@babel/runtime": "^7.18.3", "@emotion/hash": "^0.9.2", "@emotion/memoize": "^0.9.0", "@emotion/serialize": "^1.3.3", "babel-plugin-macros": "^3.1.0", "convert-source-map": "^1.5.0", "escape-string-regexp": "^4.0.0", "find-root": "^1.1.0", "source-map": "^0.5.7", "stylis": "4.2.0"}}, "node_modules/@emotion/cache": {"version": "11.14.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@emotion/cache/-/cache-11.14.0.tgz", "integrity": "sha1-7kSyaYbuuTyL6Cu5Lx96myGy7XY=", "dependencies": {"@emotion/memoize": "^0.9.0", "@emotion/sheet": "^1.4.0", "@emotion/utils": "^1.4.2", "@emotion/weak-memoize": "^0.4.0", "stylis": "4.2.0"}}, "node_modules/@emotion/hash": {"version": "0.9.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@emotion/hash/-/hash-0.9.2.tgz", "integrity": "sha1-/5IhufWLTf5h5hmneIc0vWP2iYs="}, "node_modules/@emotion/is-prop-valid": {"version": "1.3.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@emotion/is-prop-valid/-/is-prop-valid-1.3.1.tgz", "integrity": "sha1-jVzxEy+DbXrb5CzwtJ33gW/IgkA=", "dependencies": {"@emotion/memoize": "^0.9.0"}}, "node_modules/@emotion/memoize": {"version": "0.9.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@emotion/memoize/-/memoize-0.9.0.tgz", "integrity": "sha1-dFlp1kmXd3a0P8dkjFVqqkYrQQI="}, "node_modules/@emotion/react": {"version": "11.14.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@emotion/react/-/react-11.14.0.tgz", "integrity": "sha1-z6rjXrxn3Z706i6azGzSnhV90F0=", "dependencies": {"@babel/runtime": "^7.18.3", "@emotion/babel-plugin": "^11.13.5", "@emotion/cache": "^11.14.0", "@emotion/serialize": "^1.3.3", "@emotion/use-insertion-effect-with-fallbacks": "^1.2.0", "@emotion/utils": "^1.4.2", "@emotion/weak-memoize": "^0.4.0", "hoist-non-react-statics": "^3.3.1"}, "peerDependencies": {"react": ">=16.8.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@emotion/serialize": {"version": "1.3.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@emotion/serialize/-/serialize-1.3.3.tgz", "integrity": "sha1-0pFTEAXxfXBNBGOgMv5nnzdlCeg=", "dependencies": {"@emotion/hash": "^0.9.2", "@emotion/memoize": "^0.9.0", "@emotion/unitless": "^0.10.0", "@emotion/utils": "^1.4.2", "csstype": "^3.0.2"}}, "node_modules/@emotion/sheet": {"version": "1.4.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@emotion/sheet/-/sheet-1.4.0.tgz", "integrity": "sha1-ySmcNNJIvCboJWNzX3iVPS78qDw="}, "node_modules/@emotion/styled": {"version": "11.14.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@emotion/styled/-/styled-11.14.1.tgz", "integrity": "sha1-jDS+0pSOg+GYA3AwVhTCCVWqzRw=", "dependencies": {"@babel/runtime": "^7.18.3", "@emotion/babel-plugin": "^11.13.5", "@emotion/is-prop-valid": "^1.3.0", "@emotion/serialize": "^1.3.3", "@emotion/use-insertion-effect-with-fallbacks": "^1.2.0", "@emotion/utils": "^1.4.2"}, "peerDependencies": {"@emotion/react": "^11.0.0-rc.0", "react": ">=16.8.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@emotion/unitless": {"version": "0.10.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@emotion/unitless/-/unitless-0.10.0.tgz", "integrity": "sha1-KvL3x+UVD0l72r2EjOeyGKJ890U="}, "node_modules/@emotion/use-insertion-effect-with-fallbacks": {"version": "1.2.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@emotion/use-insertion-effect-with-fallbacks/-/use-insertion-effect-with-fallbacks-1.2.0.tgz", "integrity": "sha1-ioy3e1kOCa/7lg9P8emonlMnOL8=", "peerDependencies": {"react": ">=16.8.0"}}, "node_modules/@emotion/utils": {"version": "1.4.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@emotion/utils/-/utils-1.4.2.tgz", "integrity": "sha1-bfbEWIH8scQS1miKMRqYt/WcG1I="}, "node_modules/@emotion/weak-memoize": {"version": "0.4.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@emotion/weak-memoize/-/weak-memoize-0.4.0.tgz", "integrity": "sha1-XhP6yIfwjET3awzK8zcOsA/sm7Y="}, "node_modules/@envelop/core": {"version": "5.3.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@envelop/core/-/core-5.3.0.tgz", "integrity": "sha1-7mZsqpTcL9ywDJZZz8Bndf+V+qE=", "dev": true, "dependencies": {"@envelop/instrumentation": "^1.0.0", "@envelop/types": "^5.2.1", "@whatwg-node/promise-helpers": "^1.2.4", "tslib": "^2.5.0"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@envelop/instrumentation": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@envelop/instrumentation/-/instrumentation-1.0.0.tgz", "integrity": "sha1-QyaDkuBl2LqXXKy99Pwpff4+EeU=", "dev": true, "dependencies": {"@whatwg-node/promise-helpers": "^1.2.1", "tslib": "^2.5.0"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@envelop/types": {"version": "5.2.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@envelop/types/-/types-5.2.1.tgz", "integrity": "sha1-a8lxPyrqVtfePqOei7cANcBHWzY=", "dev": true, "dependencies": {"@whatwg-node/promise-helpers": "^1.0.0", "tslib": "^2.5.0"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@esbuild/aix-ppc64": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@esbuild/aix-ppc64/-/aix-ppc64-0.25.5.tgz", "integrity": "sha1-Tg+Rd2wrNA51VY9gVSGV9vrQnxg=", "cpu": ["ppc64"], "dev": true, "optional": true, "os": ["aix"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/android-arm": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@esbuild/android-arm/-/android-arm-0.25.5.tgz", "integrity": "sha1-QpDW00B7rjiDrSze0QgaI0RzziY=", "cpu": ["arm"], "dev": true, "optional": true, "os": ["android"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/android-arm64": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@esbuild/android-arm64/-/android-arm64-0.25.5.tgz", "integrity": "sha1-vHZkB/FxiSP2uAecjGG/hqw6ak8=", "cpu": ["arm64"], "dev": true, "optional": true, "os": ["android"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/android-x64": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@esbuild/android-x64/-/android-x64-0.25.5.tgz", "integrity": "sha1-QMEdnLyk8kBlSMipiV0yG8OzXv8=", "cpu": ["x64"], "dev": true, "optional": true, "os": ["android"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/darwin-arm64": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@esbuild/darwin-arm64/-/darwin-arm64-0.25.5.tgz", "integrity": "sha1-Sdi/ix35X3WayB6x0HNgGABtfjQ=", "cpu": ["arm64"], "dev": true, "optional": true, "os": ["darwin"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/darwin-x64": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@esbuild/darwin-x64/-/darwin-x64-0.25.5.tgz", "integrity": "sha1-4npdkqFIhu8dSS/VD8YaLU2H5Bg=", "cpu": ["x64"], "dev": true, "optional": true, "os": ["darwin"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/freebsd-arm64": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@esbuild/freebsd-arm64/-/freebsd-arm64-0.25.5.tgz", "integrity": "sha1-l87eWdY4hAyhBOYFzbnxsRi6Cxw=", "cpu": ["arm64"], "dev": true, "optional": true, "os": ["freebsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/freebsd-x64": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@esbuild/freebsd-x64/-/freebsd-x64-0.25.5.tgz", "integrity": "sha1-ccd4EgQqGoGQw9WB4UDRW4drnG8=", "cpu": ["x64"], "dev": true, "optional": true, "os": ["freebsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-arm": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@esbuild/linux-arm/-/linux-arm-0.25.5.tgz", "integrity": "sha1-KgvnG2zYIB+lWa6kVZjf+rwF2RE=", "cpu": ["arm"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-arm64": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@esbuild/linux-arm64/-/linux-arm64-0.25.5.tgz", "integrity": "sha1-97fI+X7/j/0uR/bGfrXJdl8hgbg=", "cpu": ["arm64"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-ia32": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@esbuild/linux-ia32/-/linux-ia32-0.25.5.tgz", "integrity": "sha1-djQURjzZ6m+h+WVV0nYvn4TGF4M=", "cpu": ["ia32"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-loong64": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@esbuild/linux-loong64/-/linux-loong64-0.25.5.tgz", "integrity": "sha1-QozyIT/3hqUCpSyWzynR/PHrhQY=", "cpu": ["loong64"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-mips64el": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@esbuild/linux-mips64el/-/linux-mips64el-0.25.5.tgz", "integrity": "sha1-XLzH/YQbTNUzWK/TNSfNOU4yXZY=", "cpu": ["mips64el"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-ppc64": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@esbuild/linux-ppc64/-/linux-ppc64-0.25.5.tgz", "integrity": "sha1-DZVKs5zk9eUPAMT4xP04+XbBOtk=", "cpu": ["ppc64"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-riscv64": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@esbuild/linux-riscv64/-/linux-riscv64-0.25.5.tgz", "integrity": "sha1-Dn3TBzBQWr2AiDIehJfpS1R7+x4=", "cpu": ["riscv64"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-s390x": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@esbuild/linux-s390x/-/linux-s390x-0.25.5.tgz", "integrity": "sha1-VmmvgTJ6OYozbX5A4yC1u9bm5y0=", "cpu": ["s390x"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-x64": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@esbuild/linux-x64/-/linux-x64-0.25.5.tgz", "integrity": "sha1-sjV90VOqSQOJZ93B/9kMaKnSoNQ=", "cpu": ["x64"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/netbsd-arm64": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@esbuild/netbsd-arm64/-/netbsd-arm64-0.25.5.tgz", "integrity": "sha1-U7TfuP4c7pN3fJ42aJO9Paprpj0=", "cpu": ["arm64"], "dev": true, "optional": true, "os": ["netbsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/netbsd-x64": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@esbuild/netbsd-x64/-/netbsd-x64-0.25.5.tgz", "integrity": "sha1-oCBvYxTOfchxO3cycD0PWN4dHnk=", "cpu": ["x64"], "dev": true, "optional": true, "os": ["netbsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/openbsd-arm64": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@esbuild/openbsd-arm64/-/openbsd-arm64-0.25.5.tgz", "integrity": "sha1-Knlsh8ROjeeAAdgIx32UiiHsIv0=", "cpu": ["arm64"], "dev": true, "optional": true, "os": ["openbsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/openbsd-x64": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@esbuild/openbsd-x64/-/openbsd-x64-0.25.5.tgz", "integrity": "sha1-KNDNiQm3+jlTr5mPKy7TT1dnKPA=", "cpu": ["x64"], "dev": true, "optional": true, "os": ["openbsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/sunos-x64": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@esbuild/sunos-x64/-/sunos-x64-0.25.5.tgz", "integrity": "sha1-ooFk9bmX6CR9QH42yQ0/1d2+DcU=", "cpu": ["x64"], "dev": true, "optional": true, "os": ["sunos"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/win32-arm64": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@esbuild/win32-arm64/-/win32-arm64-0.25.5.tgz", "integrity": "sha1-bq2+rTjovRL2M6UZDkXv+A4kAH4=", "cpu": ["arm64"], "dev": true, "optional": true, "os": ["win32"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/win32-ia32": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@esbuild/win32-ia32/-/win32-ia32-0.25.5.tgz", "integrity": "sha1-urYogAVIL57Srbne1+iOuppizA0=", "cpu": ["ia32"], "dev": true, "optional": true, "os": ["win32"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/win32-x64": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@esbuild/win32-x64/-/win32-x64-0.25.5.tgz", "integrity": "sha1-f8EUr19lY/GfczJLXV/zbs4IA9E=", "cpu": ["x64"], "dev": true, "optional": true, "os": ["win32"], "engines": {"node": ">=18"}}, "node_modules/@eslint-community/eslint-utils": {"version": "4.7.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@eslint-community/eslint-utils/-/eslint-utils-4.7.0.tgz", "integrity": "sha1-YHCEYwxsAzmSoILebm+8GotSF1o=", "dev": true, "dependencies": {"eslint-visitor-keys": "^3.4.3"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || >=8.0.0"}}, "node_modules/@eslint-community/eslint-utils/node_modules/eslint-visitor-keys": {"version": "3.4.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz", "integrity": "sha1-DNcv6FUOPC6uFWqWpN3c0cisWAA=", "dev": true, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/@eslint-community/regexpp": {"version": "4.12.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@eslint-community/regexpp/-/regexpp-4.12.1.tgz", "integrity": "sha1-z8bP/jnfOQo4Qc3iq8z5Lqp64OA=", "dev": true, "engines": {"node": "^12.0.0 || ^14.0.0 || >=16.0.0"}}, "node_modules/@eslint/config-array": {"version": "0.21.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@eslint/config-array/-/config-array-0.21.0.tgz", "integrity": "sha1-q9vL0WsSTGOAgXZjkqTWtQn3JjY=", "dev": true, "dependencies": {"@eslint/object-schema": "^2.1.6", "debug": "^4.3.1", "minimatch": "^3.1.2"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@eslint/config-helpers": {"version": "0.3.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@eslint/config-helpers/-/config-helpers-0.3.0.tgz", "integrity": "sha1-PgmpDfuH4ABcdpR5HljpcHcnEoY=", "dev": true, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@eslint/core": {"version": "0.14.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@eslint/core/-/core-0.14.0.tgz", "integrity": "sha1-MmKJOAlo6vfpbzZOHkz4863y0AM=", "dev": true, "dependencies": {"@types/json-schema": "^7.0.15"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@eslint/eslintrc": {"version": "3.3.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@eslint/eslintrc/-/eslintrc-3.3.1.tgz", "integrity": "sha1-5V9/HdQAYA3QZtu6NJxMC6yRaWQ=", "dev": true, "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^10.0.1", "globals": "^14.0.0", "ignore": "^5.2.0", "import-fresh": "^3.2.1", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "strip-json-comments": "^3.1.1"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/@eslint/eslintrc/node_modules/globals": {"version": "14.0.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/globals/-/globals-14.0.0.tgz", "integrity": "sha1-iY10E8Kbq89rr+Vvyt3thYrack4=", "dev": true, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@eslint/js": {"version": "9.30.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@eslint/js/-/js-9.30.1.tgz", "integrity": "sha1-6+ndUqODRXhMSGMAF1ooxgE8CI0=", "dev": true, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://eslint.org/donate"}}, "node_modules/@eslint/object-schema": {"version": "2.1.6", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@eslint/object-schema/-/object-schema-2.1.6.tgz", "integrity": "sha1-WDaatbWzyhF4gMD2wLDzL2lQ8k8=", "dev": true, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@eslint/plugin-kit": {"version": "0.3.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@eslint/plugin-kit/-/plugin-kit-0.3.3.tgz", "integrity": "sha1-MpJrWb1AfVjYF5QeSLKnBJNZsf0=", "dev": true, "dependencies": {"@eslint/core": "^0.15.1", "levn": "^0.4.1"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@eslint/plugin-kit/node_modules/@eslint/core": {"version": "0.15.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@eslint/core/-/core-0.15.1.tgz", "integrity": "sha1-1TDUQgnL/i+C74bWugh2AZbdO2A=", "dev": true, "dependencies": {"@types/json-schema": "^7.0.15"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@fastify/busboy": {"version": "3.1.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@fastify/busboy/-/busboy-3.1.1.tgz", "integrity": "sha1-rzrqfx5S7JFti1ydzA8J1MBgo/w=", "dev": true}, "node_modules/@graphql-codegen/add": {"version": "5.0.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-codegen/add/-/add-5.0.3.tgz", "integrity": "sha1-Ht5rrJqTZh7X+lgIsgPQeeGx0hU=", "dev": true, "dependencies": {"@graphql-codegen/plugin-helpers": "^5.0.3", "tslib": "~2.6.0"}, "peerDependencies": {"graphql": "^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@graphql-codegen/add/node_modules/tslib": {"version": "2.6.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/tslib/-/tslib-2.6.3.tgz", "integrity": "sha1-BDj4EK16ntzeeiQcPYDbaTyMv+A=", "dev": true}, "node_modules/@graphql-codegen/cli": {"version": "5.0.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-codegen/cli/-/cli-5.0.2.tgz", "integrity": "sha1-B/9pHBbaTD3MDhmV0yMVMDeasxc=", "dev": true, "dependencies": {"@babel/generator": "^7.18.13", "@babel/template": "^7.18.10", "@babel/types": "^7.18.13", "@graphql-codegen/client-preset": "^4.2.2", "@graphql-codegen/core": "^4.0.2", "@graphql-codegen/plugin-helpers": "^5.0.3", "@graphql-tools/apollo-engine-loader": "^8.0.0", "@graphql-tools/code-file-loader": "^8.0.0", "@graphql-tools/git-loader": "^8.0.0", "@graphql-tools/github-loader": "^8.0.0", "@graphql-tools/graphql-file-loader": "^8.0.0", "@graphql-tools/json-file-loader": "^8.0.0", "@graphql-tools/load": "^8.0.0", "@graphql-tools/prisma-loader": "^8.0.0", "@graphql-tools/url-loader": "^8.0.0", "@graphql-tools/utils": "^10.0.0", "@whatwg-node/fetch": "^0.8.0", "chalk": "^4.1.0", "cosmiconfig": "^8.1.3", "debounce": "^1.2.0", "detect-indent": "^6.0.0", "graphql-config": "^5.0.2", "inquirer": "^8.0.0", "is-glob": "^4.0.1", "jiti": "^1.17.1", "json-to-pretty-yaml": "^1.2.2", "listr2": "^4.0.5", "log-symbols": "^4.0.0", "micromatch": "^4.0.5", "shell-quote": "^1.7.3", "string-env-interpolation": "^1.0.1", "ts-log": "^2.2.3", "tslib": "^2.4.0", "yaml": "^2.3.1", "yargs": "^17.0.0"}, "bin": {"gql-gen": "cjs/bin.js", "graphql-code-generator": "cjs/bin.js", "graphql-codegen": "cjs/bin.js", "graphql-codegen-esm": "esm/bin.js"}, "peerDependencies": {"@parcel/watcher": "^2.1.0", "graphql": "^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0"}, "peerDependenciesMeta": {"@parcel/watcher": {"optional": true}}}, "node_modules/@graphql-codegen/client-preset": {"version": "4.3.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-codegen/client-preset/-/client-preset-4.3.3.tgz", "integrity": "sha1-O75d9cSajJdjz8kZZ5sV7u+/FSY=", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.20.2", "@babel/template": "^7.20.7", "@graphql-codegen/add": "^5.0.3", "@graphql-codegen/gql-tag-operations": "4.0.9", "@graphql-codegen/plugin-helpers": "^5.0.4", "@graphql-codegen/typed-document-node": "^5.0.9", "@graphql-codegen/typescript": "^4.0.9", "@graphql-codegen/typescript-operations": "^4.2.3", "@graphql-codegen/visitor-plugin-common": "^5.3.1", "@graphql-tools/documents": "^1.0.0", "@graphql-tools/utils": "^10.0.0", "@graphql-typed-document-node/core": "3.2.0", "tslib": "~2.6.0"}, "peerDependencies": {"graphql": "^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@graphql-codegen/client-preset/node_modules/tslib": {"version": "2.6.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/tslib/-/tslib-2.6.3.tgz", "integrity": "sha1-BDj4EK16ntzeeiQcPYDbaTyMv+A=", "dev": true}, "node_modules/@graphql-codegen/core": {"version": "4.0.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-codegen/core/-/core-4.0.2.tgz", "integrity": "sha1-fm7CZidvVLvwL2BZnZ5Rj0pZ2F4=", "dev": true, "dependencies": {"@graphql-codegen/plugin-helpers": "^5.0.3", "@graphql-tools/schema": "^10.0.0", "@graphql-tools/utils": "^10.0.0", "tslib": "~2.6.0"}, "peerDependencies": {"graphql": "^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@graphql-codegen/core/node_modules/tslib": {"version": "2.6.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/tslib/-/tslib-2.6.3.tgz", "integrity": "sha1-BDj4EK16ntzeeiQcPYDbaTyMv+A=", "dev": true}, "node_modules/@graphql-codegen/gql-tag-operations": {"version": "4.0.9", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-codegen/gql-tag-operations/-/gql-tag-operations-4.0.9.tgz", "integrity": "sha1-Jh7LwuldUlyqEsrS4A18JldVMuQ=", "dev": true, "dependencies": {"@graphql-codegen/plugin-helpers": "^5.0.4", "@graphql-codegen/visitor-plugin-common": "5.3.1", "@graphql-tools/utils": "^10.0.0", "auto-bind": "~4.0.0", "tslib": "~2.6.0"}, "peerDependencies": {"graphql": "^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@graphql-codegen/gql-tag-operations/node_modules/@graphql-codegen/visitor-plugin-common": {"version": "5.3.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-codegen/visitor-plugin-common/-/visitor-plugin-common-5.3.1.tgz", "integrity": "sha1-0/tfYzbL71jilgRxQi2j88r/fxc=", "dev": true, "dependencies": {"@graphql-codegen/plugin-helpers": "^5.0.4", "@graphql-tools/optimize": "^2.0.0", "@graphql-tools/relay-operation-optimizer": "^7.0.0", "@graphql-tools/utils": "^10.0.0", "auto-bind": "~4.0.0", "change-case-all": "1.0.15", "dependency-graph": "^0.11.0", "graphql-tag": "^2.11.0", "parse-filepath": "^1.0.2", "tslib": "~2.6.0"}, "peerDependencies": {"graphql": "^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@graphql-codegen/gql-tag-operations/node_modules/tslib": {"version": "2.6.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/tslib/-/tslib-2.6.3.tgz", "integrity": "sha1-BDj4EK16ntzeeiQcPYDbaTyMv+A=", "dev": true}, "node_modules/@graphql-codegen/introspection": {"version": "4.0.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-codegen/introspection/-/introspection-4.0.3.tgz", "integrity": "sha1-dW45+0Up6hXTKhIvC86bCmZCU3k=", "dev": true, "dependencies": {"@graphql-codegen/plugin-helpers": "^5.0.3", "@graphql-codegen/visitor-plugin-common": "^5.0.0", "tslib": "~2.6.0"}, "peerDependencies": {"graphql": "^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@graphql-codegen/introspection/node_modules/tslib": {"version": "2.6.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/tslib/-/tslib-2.6.3.tgz", "integrity": "sha1-BDj4EK16ntzeeiQcPYDbaTyMv+A=", "dev": true}, "node_modules/@graphql-codegen/plugin-helpers": {"version": "5.1.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-codegen/plugin-helpers/-/plugin-helpers-5.1.1.tgz", "integrity": "sha1-t8dEyIJjZ8MALDRhEt480bD5mxY=", "dev": true, "dependencies": {"@graphql-tools/utils": "^10.0.0", "change-case-all": "1.0.15", "common-tags": "1.8.2", "import-from": "4.0.0", "lodash": "~4.17.0", "tslib": "~2.6.0"}, "engines": {"node": ">=16"}, "peerDependencies": {"graphql": "^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@graphql-codegen/plugin-helpers/node_modules/tslib": {"version": "2.6.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/tslib/-/tslib-2.6.3.tgz", "integrity": "sha1-BDj4EK16ntzeeiQcPYDbaTyMv+A=", "dev": true}, "node_modules/@graphql-codegen/schema-ast": {"version": "4.1.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-codegen/schema-ast/-/schema-ast-4.1.0.tgz", "integrity": "sha1-oecfmTRklbknIWGp7Qd1boJkhyY=", "dev": true, "dependencies": {"@graphql-codegen/plugin-helpers": "^5.0.3", "@graphql-tools/utils": "^10.0.0", "tslib": "~2.6.0"}, "peerDependencies": {"graphql": "^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@graphql-codegen/schema-ast/node_modules/tslib": {"version": "2.6.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/tslib/-/tslib-2.6.3.tgz", "integrity": "sha1-BDj4EK16ntzeeiQcPYDbaTyMv+A=", "dev": true}, "node_modules/@graphql-codegen/typed-document-node": {"version": "5.1.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-codegen/typed-document-node/-/typed-document-node-5.1.2.tgz", "integrity": "sha1-oaPaLZ/hhHgxrabkHgnFu4Hdazw=", "dev": true, "dependencies": {"@graphql-codegen/plugin-helpers": "^5.1.0", "@graphql-codegen/visitor-plugin-common": "5.8.0", "auto-bind": "~4.0.0", "change-case-all": "1.0.15", "tslib": "~2.6.0"}, "engines": {"node": ">=16"}, "peerDependencies": {"graphql": "^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@graphql-codegen/typed-document-node/node_modules/tslib": {"version": "2.6.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/tslib/-/tslib-2.6.3.tgz", "integrity": "sha1-BDj4EK16ntzeeiQcPYDbaTyMv+A=", "dev": true}, "node_modules/@graphql-codegen/typescript": {"version": "4.1.6", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-codegen/typescript/-/typescript-4.1.6.tgz", "integrity": "sha1-80gczBZW6WiS1ilnH7LP9dq8RYs=", "dev": true, "dependencies": {"@graphql-codegen/plugin-helpers": "^5.1.0", "@graphql-codegen/schema-ast": "^4.0.2", "@graphql-codegen/visitor-plugin-common": "5.8.0", "auto-bind": "~4.0.0", "tslib": "~2.6.0"}, "engines": {"node": ">=16"}, "peerDependencies": {"graphql": "^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@graphql-codegen/typescript-operations": {"version": "4.6.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-codegen/typescript-operations/-/typescript-operations-4.6.1.tgz", "integrity": "sha1-dj7aKMd/3e4rmum9e6rQUM//8KU=", "dev": true, "dependencies": {"@graphql-codegen/plugin-helpers": "^5.1.0", "@graphql-codegen/typescript": "^4.1.6", "@graphql-codegen/visitor-plugin-common": "5.8.0", "auto-bind": "~4.0.0", "tslib": "~2.6.0"}, "engines": {"node": ">=16"}, "peerDependencies": {"graphql": "^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0", "graphql-sock": "^1.0.0"}, "peerDependenciesMeta": {"graphql-sock": {"optional": true}}}, "node_modules/@graphql-codegen/typescript-operations/node_modules/tslib": {"version": "2.6.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/tslib/-/tslib-2.6.3.tgz", "integrity": "sha1-BDj4EK16ntzeeiQcPYDbaTyMv+A=", "dev": true}, "node_modules/@graphql-codegen/typescript/node_modules/tslib": {"version": "2.6.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/tslib/-/tslib-2.6.3.tgz", "integrity": "sha1-BDj4EK16ntzeeiQcPYDbaTyMv+A=", "dev": true}, "node_modules/@graphql-codegen/visitor-plugin-common": {"version": "5.8.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-codegen/visitor-plugin-common/-/visitor-plugin-common-5.8.0.tgz", "integrity": "sha1-G3lkU+uW2OatXU06z/ME5GcngaA=", "dev": true, "dependencies": {"@graphql-codegen/plugin-helpers": "^5.1.0", "@graphql-tools/optimize": "^2.0.0", "@graphql-tools/relay-operation-optimizer": "^7.0.0", "@graphql-tools/utils": "^10.0.0", "auto-bind": "~4.0.0", "change-case-all": "1.0.15", "dependency-graph": "^0.11.0", "graphql-tag": "^2.11.0", "parse-filepath": "^1.0.2", "tslib": "~2.6.0"}, "engines": {"node": ">=16"}, "peerDependencies": {"graphql": "^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@graphql-codegen/visitor-plugin-common/node_modules/tslib": {"version": "2.6.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/tslib/-/tslib-2.6.3.tgz", "integrity": "sha1-BDj4EK16ntzeeiQcPYDbaTyMv+A=", "dev": true}, "node_modules/@graphql-hive/signal": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-hive/signal/-/signal-1.0.0.tgz", "integrity": "sha1-biGTZgpHySWrrb5yKT38lDDiT48=", "dev": true, "engines": {"node": ">=18.0.0"}}, "node_modules/@graphql-tools/apollo-engine-loader": {"version": "8.0.20", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/apollo-engine-loader/-/apollo-engine-loader-8.0.20.tgz", "integrity": "sha1-27fSlKP78B7LBQBL1uNKAQKXGM0=", "dev": true, "dependencies": {"@graphql-tools/utils": "^10.8.6", "@whatwg-node/fetch": "^0.10.0", "sync-fetch": "0.6.0-2", "tslib": "^2.4.0"}, "engines": {"node": ">=16.0.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/apollo-engine-loader/node_modules/@whatwg-node/fetch": {"version": "0.10.8", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@whatwg-node/fetch/-/fetch-0.10.8.tgz", "integrity": "sha1-FGf5UFgm+nJxxn368NclGrjCucw=", "dev": true, "dependencies": {"@whatwg-node/node-fetch": "^0.7.21", "urlpattern-polyfill": "^10.0.0"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@graphql-tools/apollo-engine-loader/node_modules/@whatwg-node/node-fetch": {"version": "0.7.21", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@whatwg-node/node-fetch/-/node-fetch-0.7.21.tgz", "integrity": "sha1-upRO6naEBHyRrH9QCXJDYz9sn18=", "dev": true, "dependencies": {"@fastify/busboy": "^3.1.1", "@whatwg-node/disposablestack": "^0.0.6", "@whatwg-node/promise-helpers": "^1.3.2", "tslib": "^2.6.3"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@graphql-tools/apollo-engine-loader/node_modules/urlpattern-polyfill": {"version": "10.1.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/urlpattern-polyfill/-/urlpattern-polyfill-10.1.0.tgz", "integrity": "sha1-GyUX5hQTbHO6MpSNXno6Bjy6jnQ=", "dev": true}, "node_modules/@graphql-tools/batch-execute": {"version": "9.0.17", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/batch-execute/-/batch-execute-9.0.17.tgz", "integrity": "sha1-wu8S5sKy4J9eYRbCCiIpv67NOZw=", "dev": true, "dependencies": {"@graphql-tools/utils": "^10.8.1", "@whatwg-node/promise-helpers": "^1.3.0", "dataloader": "^2.2.3", "tslib": "^2.8.1"}, "engines": {"node": ">=18.0.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/code-file-loader": {"version": "8.1.20", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/code-file-loader/-/code-file-loader-8.1.20.tgz", "integrity": "sha1-g/GvewUeDiqR2fZBLjaKU26ruGw=", "dev": true, "dependencies": {"@graphql-tools/graphql-tag-pluck": "8.3.19", "@graphql-tools/utils": "^10.8.6", "globby": "^11.0.3", "tslib": "^2.4.0", "unixify": "^1.0.0"}, "engines": {"node": ">=16.0.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/delegate": {"version": "10.2.20", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/delegate/-/delegate-10.2.20.tgz", "integrity": "sha1-ECcN5fYcxKfFekMEfJr1Q3aq11w=", "dev": true, "dependencies": {"@graphql-tools/batch-execute": "^9.0.17", "@graphql-tools/executor": "^1.4.7", "@graphql-tools/schema": "^10.0.11", "@graphql-tools/utils": "^10.8.1", "@repeaterjs/repeater": "^3.0.6", "@whatwg-node/promise-helpers": "^1.3.0", "dataloader": "^2.2.3", "dset": "^3.1.2", "tslib": "^2.8.1"}, "engines": {"node": ">=18.0.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/documents": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/documents/-/documents-1.0.1.tgz", "integrity": "sha1-rhnNVmfSLCOzMdOhQpRD7XEw+u4=", "dev": true, "dependencies": {"lodash.sortby": "^4.7.0", "tslib": "^2.4.0"}, "engines": {"node": ">=16.0.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/executor": {"version": "1.4.7", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/executor/-/executor-1.4.7.tgz", "integrity": "sha1-hr8LJvKt1baG7JboZu4i0bgfm2s=", "dev": true, "dependencies": {"@graphql-tools/utils": "^10.8.6", "@graphql-typed-document-node/core": "^3.2.0", "@repeaterjs/repeater": "^3.0.4", "@whatwg-node/disposablestack": "^0.0.6", "@whatwg-node/promise-helpers": "^1.0.0", "tslib": "^2.4.0"}, "engines": {"node": ">=16.0.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/executor-common": {"version": "0.0.4", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/executor-common/-/executor-common-0.0.4.tgz", "integrity": "sha1-djYDpteiK7CdZ85oLoSg1zD/K/k=", "dev": true, "dependencies": {"@envelop/core": "^5.2.3", "@graphql-tools/utils": "^10.8.1"}, "engines": {"node": ">=18.0.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/executor-graphql-ws": {"version": "2.0.5", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/executor-graphql-ws/-/executor-graphql-ws-2.0.5.tgz", "integrity": "sha1-Tlj8lsLwgDOO5bmvrujV4maB1zI=", "dev": true, "dependencies": {"@graphql-tools/executor-common": "^0.0.4", "@graphql-tools/utils": "^10.8.1", "@whatwg-node/disposablestack": "^0.0.6", "graphql-ws": "^6.0.3", "isomorphic-ws": "^5.0.0", "tslib": "^2.8.1", "ws": "^8.17.1"}, "engines": {"node": ">=18.0.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/executor-http": {"version": "1.3.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/executor-http/-/executor-http-1.3.3.tgz", "integrity": "sha1-BX3nnryQ7b0kIlnMyxJo037mxXk=", "dev": true, "dependencies": {"@graphql-hive/signal": "^1.0.0", "@graphql-tools/executor-common": "^0.0.4", "@graphql-tools/utils": "^10.8.1", "@repeaterjs/repeater": "^3.0.4", "@whatwg-node/disposablestack": "^0.0.6", "@whatwg-node/fetch": "^0.10.4", "@whatwg-node/promise-helpers": "^1.3.0", "meros": "^1.2.1", "tslib": "^2.8.1"}, "engines": {"node": ">=18.0.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/executor-http/node_modules/@whatwg-node/fetch": {"version": "0.10.8", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@whatwg-node/fetch/-/fetch-0.10.8.tgz", "integrity": "sha1-FGf5UFgm+nJxxn368NclGrjCucw=", "dev": true, "dependencies": {"@whatwg-node/node-fetch": "^0.7.21", "urlpattern-polyfill": "^10.0.0"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@graphql-tools/executor-http/node_modules/@whatwg-node/node-fetch": {"version": "0.7.21", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@whatwg-node/node-fetch/-/node-fetch-0.7.21.tgz", "integrity": "sha1-upRO6naEBHyRrH9QCXJDYz9sn18=", "dev": true, "dependencies": {"@fastify/busboy": "^3.1.1", "@whatwg-node/disposablestack": "^0.0.6", "@whatwg-node/promise-helpers": "^1.3.2", "tslib": "^2.6.3"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@graphql-tools/executor-http/node_modules/urlpattern-polyfill": {"version": "10.1.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/urlpattern-polyfill/-/urlpattern-polyfill-10.1.0.tgz", "integrity": "sha1-GyUX5hQTbHO6MpSNXno6Bjy6jnQ=", "dev": true}, "node_modules/@graphql-tools/executor-legacy-ws": {"version": "1.1.17", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/executor-legacy-ws/-/executor-legacy-ws-1.1.17.tgz", "integrity": "sha1-Vc82mGb/8LJolj2vlAQbyGIbs0U=", "dev": true, "dependencies": {"@graphql-tools/utils": "^10.8.6", "@types/ws": "^8.0.0", "isomorphic-ws": "^5.0.0", "tslib": "^2.4.0", "ws": "^8.17.1"}, "engines": {"node": ">=16.0.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/git-loader": {"version": "8.0.24", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/git-loader/-/git-loader-8.0.24.tgz", "integrity": "sha1-ztaG6CZMo2rVELGWeAQjiqTwSFg=", "dev": true, "dependencies": {"@graphql-tools/graphql-tag-pluck": "8.3.19", "@graphql-tools/utils": "^10.8.6", "is-glob": "4.0.3", "micromatch": "^4.0.8", "tslib": "^2.4.0", "unixify": "^1.0.0"}, "engines": {"node": ">=16.0.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/github-loader": {"version": "8.0.20", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/github-loader/-/github-loader-8.0.20.tgz", "integrity": "sha1-dpcXgoxH91H1waJbROwnDNzSIr4=", "dev": true, "dependencies": {"@graphql-tools/executor-http": "^1.1.9", "@graphql-tools/graphql-tag-pluck": "^8.3.19", "@graphql-tools/utils": "^10.8.6", "@whatwg-node/fetch": "^0.10.0", "@whatwg-node/promise-helpers": "^1.0.0", "sync-fetch": "0.6.0-2", "tslib": "^2.4.0"}, "engines": {"node": ">=16.0.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/github-loader/node_modules/@whatwg-node/fetch": {"version": "0.10.8", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@whatwg-node/fetch/-/fetch-0.10.8.tgz", "integrity": "sha1-FGf5UFgm+nJxxn368NclGrjCucw=", "dev": true, "dependencies": {"@whatwg-node/node-fetch": "^0.7.21", "urlpattern-polyfill": "^10.0.0"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@graphql-tools/github-loader/node_modules/@whatwg-node/node-fetch": {"version": "0.7.21", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@whatwg-node/node-fetch/-/node-fetch-0.7.21.tgz", "integrity": "sha1-upRO6naEBHyRrH9QCXJDYz9sn18=", "dev": true, "dependencies": {"@fastify/busboy": "^3.1.1", "@whatwg-node/disposablestack": "^0.0.6", "@whatwg-node/promise-helpers": "^1.3.2", "tslib": "^2.6.3"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@graphql-tools/github-loader/node_modules/urlpattern-polyfill": {"version": "10.1.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/urlpattern-polyfill/-/urlpattern-polyfill-10.1.0.tgz", "integrity": "sha1-GyUX5hQTbHO6MpSNXno6Bjy6jnQ=", "dev": true}, "node_modules/@graphql-tools/graphql-file-loader": {"version": "8.0.20", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/graphql-file-loader/-/graphql-file-loader-8.0.20.tgz", "integrity": "sha1-2m6ymXDBkb1bFkMvFfkZJHjEq6s=", "dev": true, "dependencies": {"@graphql-tools/import": "7.0.19", "@graphql-tools/utils": "^10.8.6", "globby": "^11.0.3", "tslib": "^2.4.0", "unixify": "^1.0.0"}, "engines": {"node": ">=16.0.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/graphql-tag-pluck": {"version": "8.3.19", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/graphql-tag-pluck/-/graphql-tag-pluck-8.3.19.tgz", "integrity": "sha1-pk2hSXQuY3F7QJADrC8fWWYF5Sk=", "dev": true, "dependencies": {"@babel/core": "^7.26.10", "@babel/parser": "^7.26.10", "@babel/plugin-syntax-import-assertions": "^7.26.0", "@babel/traverse": "^7.26.10", "@babel/types": "^7.26.10", "@graphql-tools/utils": "^10.8.6", "tslib": "^2.4.0"}, "engines": {"node": ">=16.0.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/import": {"version": "7.0.19", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/import/-/import-7.0.19.tgz", "integrity": "sha1-TYo9xegwJz1i5HKk8NwCFwxZvuo=", "dev": true, "dependencies": {"@graphql-tools/utils": "^10.8.6", "resolve-from": "5.0.0", "tslib": "^2.4.0"}, "engines": {"node": ">=16.0.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/json-file-loader": {"version": "8.0.18", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/json-file-loader/-/json-file-loader-8.0.18.tgz", "integrity": "sha1-+JQ4wUY9t1+X9BnBe+I02VJb710=", "dev": true, "dependencies": {"@graphql-tools/utils": "^10.8.6", "globby": "^11.0.3", "tslib": "^2.4.0", "unixify": "^1.0.0"}, "engines": {"node": ">=16.0.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/load": {"version": "8.1.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/load/-/load-8.1.0.tgz", "integrity": "sha1-SqA/BxqHd+MUsQKJt9YJfaqvh4M=", "dev": true, "dependencies": {"@graphql-tools/schema": "^10.0.23", "@graphql-tools/utils": "^10.8.6", "p-limit": "3.1.0", "tslib": "^2.4.0"}, "engines": {"node": ">=16.0.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/merge": {"version": "9.0.24", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/merge/-/merge-9.0.24.tgz", "integrity": "sha1-HzZuhViIlMtJa9HDMr52ZdsUPfI=", "dev": true, "dependencies": {"@graphql-tools/utils": "^10.8.6", "tslib": "^2.4.0"}, "engines": {"node": ">=16.0.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/optimize": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/optimize/-/optimize-2.0.0.tgz", "integrity": "sha1-epd50YCCRREkilDFokHv9uei2QY=", "dev": true, "dependencies": {"tslib": "^2.4.0"}, "engines": {"node": ">=16.0.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/prisma-loader": {"version": "8.0.17", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/prisma-loader/-/prisma-loader-8.0.17.tgz", "integrity": "sha1-vH7921efuFgAVA3TUoQ4Dp34UaI=", "dev": true, "dependencies": {"@graphql-tools/url-loader": "^8.0.15", "@graphql-tools/utils": "^10.5.6", "@types/js-yaml": "^4.0.0", "@whatwg-node/fetch": "^0.10.0", "chalk": "^4.1.0", "debug": "^4.3.1", "dotenv": "^16.0.0", "graphql-request": "^6.0.0", "http-proxy-agent": "^7.0.0", "https-proxy-agent": "^7.0.0", "jose": "^5.0.0", "js-yaml": "^4.0.0", "lodash": "^4.17.20", "scuid": "^1.1.0", "tslib": "^2.4.0", "yaml-ast-parser": "^0.0.43"}, "engines": {"node": ">=16.0.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/prisma-loader/node_modules/@whatwg-node/fetch": {"version": "0.10.8", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@whatwg-node/fetch/-/fetch-0.10.8.tgz", "integrity": "sha1-FGf5UFgm+nJxxn368NclGrjCucw=", "dev": true, "dependencies": {"@whatwg-node/node-fetch": "^0.7.21", "urlpattern-polyfill": "^10.0.0"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@graphql-tools/prisma-loader/node_modules/@whatwg-node/node-fetch": {"version": "0.7.21", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@whatwg-node/node-fetch/-/node-fetch-0.7.21.tgz", "integrity": "sha1-upRO6naEBHyRrH9QCXJDYz9sn18=", "dev": true, "dependencies": {"@fastify/busboy": "^3.1.1", "@whatwg-node/disposablestack": "^0.0.6", "@whatwg-node/promise-helpers": "^1.3.2", "tslib": "^2.6.3"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@graphql-tools/prisma-loader/node_modules/urlpattern-polyfill": {"version": "10.1.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/urlpattern-polyfill/-/urlpattern-polyfill-10.1.0.tgz", "integrity": "sha1-GyUX5hQTbHO6MpSNXno6Bjy6jnQ=", "dev": true}, "node_modules/@graphql-tools/relay-operation-optimizer": {"version": "7.0.19", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/relay-operation-optimizer/-/relay-operation-optimizer-7.0.19.tgz", "integrity": "sha1-8EvQmYd9xHC57UxlpTdaGj4jNfE=", "dev": true, "dependencies": {"@ardatan/relay-compiler": "^12.0.3", "@graphql-tools/utils": "^10.8.6", "tslib": "^2.4.0"}, "engines": {"node": ">=16.0.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/schema": {"version": "10.0.23", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/schema/-/schema-10.0.23.tgz", "integrity": "sha1-2IZelvN6BMpDMD1wJK3X7b4MXtQ=", "dev": true, "dependencies": {"@graphql-tools/merge": "^9.0.24", "@graphql-tools/utils": "^10.8.6", "tslib": "^2.4.0"}, "engines": {"node": ">=16.0.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/url-loader": {"version": "8.0.31", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/url-loader/-/url-loader-8.0.31.tgz", "integrity": "sha1-dc02uLRch5k7TdIUmoP6mtPNKmc=", "dev": true, "dependencies": {"@graphql-tools/executor-graphql-ws": "^2.0.1", "@graphql-tools/executor-http": "^1.1.9", "@graphql-tools/executor-legacy-ws": "^1.1.17", "@graphql-tools/utils": "^10.8.6", "@graphql-tools/wrap": "^10.0.16", "@types/ws": "^8.0.0", "@whatwg-node/fetch": "^0.10.0", "@whatwg-node/promise-helpers": "^1.0.0", "isomorphic-ws": "^5.0.0", "sync-fetch": "0.6.0-2", "tslib": "^2.4.0", "ws": "^8.17.1"}, "engines": {"node": ">=16.0.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/url-loader/node_modules/@whatwg-node/fetch": {"version": "0.10.8", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@whatwg-node/fetch/-/fetch-0.10.8.tgz", "integrity": "sha1-FGf5UFgm+nJxxn368NclGrjCucw=", "dev": true, "dependencies": {"@whatwg-node/node-fetch": "^0.7.21", "urlpattern-polyfill": "^10.0.0"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@graphql-tools/url-loader/node_modules/@whatwg-node/node-fetch": {"version": "0.7.21", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@whatwg-node/node-fetch/-/node-fetch-0.7.21.tgz", "integrity": "sha1-upRO6naEBHyRrH9QCXJDYz9sn18=", "dev": true, "dependencies": {"@fastify/busboy": "^3.1.1", "@whatwg-node/disposablestack": "^0.0.6", "@whatwg-node/promise-helpers": "^1.3.2", "tslib": "^2.6.3"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@graphql-tools/url-loader/node_modules/urlpattern-polyfill": {"version": "10.1.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/urlpattern-polyfill/-/urlpattern-polyfill-10.1.0.tgz", "integrity": "sha1-GyUX5hQTbHO6MpSNXno6Bjy6jnQ=", "dev": true}, "node_modules/@graphql-tools/utils": {"version": "10.8.6", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/utils/-/utils-10.8.6.tgz", "integrity": "sha1-ae8p5AiieRkQiysiJ/6LRlrPnlw=", "dev": true, "dependencies": {"@graphql-typed-document-node/core": "^3.1.1", "@whatwg-node/promise-helpers": "^1.0.0", "cross-inspect": "1.0.1", "dset": "^3.1.4", "tslib": "^2.4.0"}, "engines": {"node": ">=16.0.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/wrap": {"version": "10.1.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/wrap/-/wrap-10.1.1.tgz", "integrity": "sha1-1ubacBZv6QtWO+ObUvxBZVUoeAg=", "dev": true, "dependencies": {"@graphql-tools/delegate": "^10.2.20", "@graphql-tools/schema": "^10.0.11", "@graphql-tools/utils": "^10.8.1", "@whatwg-node/promise-helpers": "^1.3.0", "tslib": "^2.8.1"}, "engines": {"node": ">=18.0.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-typed-document-node/core": {"version": "3.2.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-typed-document-node/core/-/core-3.2.0.tgz", "integrity": "sha1-Xz2W7GsjVK1tiii/IWodl7VCaGE=", "peerDependencies": {"graphql": "^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@humanfs/core": {"version": "0.19.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@humanfs/core/-/core-0.19.1.tgz", "integrity": "sha1-F8Vcp9Qmcz/jxWGQa4Fzwza0Cnc=", "dev": true, "engines": {"node": ">=18.18.0"}}, "node_modules/@humanfs/node": {"version": "0.16.6", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@humanfs/node/-/node-0.16.6.tgz", "integrity": "sha1-7ioQ6qvRExmHvwSI/ZuCAXTNdl4=", "dev": true, "dependencies": {"@humanfs/core": "^0.19.1", "@humanwhocodes/retry": "^0.3.0"}, "engines": {"node": ">=18.18.0"}}, "node_modules/@humanfs/node/node_modules/@humanwhocodes/retry": {"version": "0.3.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@humanwhocodes/retry/-/retry-0.3.1.tgz", "integrity": "sha1-xypcdqn7rzSI4jGxPcUsDae6tCo=", "dev": true, "engines": {"node": ">=18.18"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}, "node_modules/@humanwhocodes/module-importer": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz", "integrity": "sha1-r1smkaIrRL6EewyoFkHF+2rQFyw=", "dev": true, "engines": {"node": ">=12.22"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}, "node_modules/@humanwhocodes/retry": {"version": "0.4.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@humanwhocodes/retry/-/retry-0.4.3.tgz", "integrity": "sha1-wrnS43TuYsWG062+qHGZsdenpro=", "dev": true, "engines": {"node": ">=18.18"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}, "node_modules/@jridgewell/gen-mapping": {"version": "0.3.12", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@jridgewell/gen-mapping/-/gen-mapping-0.3.12.tgz", "integrity": "sha1-IjTOJsYoifA9s9f+pDwZMqs+kns=", "dependencies": {"@jridgewell/sourcemap-codec": "^1.5.0", "@jridgewell/trace-mapping": "^0.3.24"}}, "node_modules/@jridgewell/resolve-uri": {"version": "3.1.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz", "integrity": "sha1-eg7mAfYPmaIMfHxf8MgDiMEYm9Y=", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/sourcemap-codec": {"version": "1.5.4", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.4.tgz", "integrity": "sha1-c1gENDOy5dpWmqAsvEwSHaOvJ9c="}, "node_modules/@jridgewell/trace-mapping": {"version": "0.3.29", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@jridgewell/trace-mapping/-/trace-mapping-0.3.29.tgz", "integrity": "sha1-pY0x6q2vksZpVoCy4dRkqbj79/w=", "dependencies": {"@jridgewell/resolve-uri": "^3.1.0", "@jridgewell/sourcemap-codec": "^1.4.14"}}, "node_modules/@mui/core-downloads-tracker": {"version": "6.4.12", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@mui/core-downloads-tracker/-/core-downloads-tracker-6.4.12.tgz", "integrity": "sha1-5TvFWGQdAvNZ6fQRKi7xn/1NtlI=", "funding": {"type": "opencollective", "url": "https://opencollective.com/mui-org"}}, "node_modules/@mui/icons-material": {"version": "6.4.12", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@mui/icons-material/-/icons-material-6.4.12.tgz", "integrity": "sha1-6ONc0IqHq+cMHHYMSbTSbCEVRSc=", "dependencies": {"@babel/runtime": "^7.26.0"}, "engines": {"node": ">=14.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/mui-org"}, "peerDependencies": {"@mui/material": "^6.4.12", "@types/react": "^17.0.0 || ^18.0.0 || ^19.0.0", "react": "^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@mui/material": {"version": "6.4.12", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@mui/material/-/material-6.4.12.tgz", "integrity": "sha1-EbmGaB+wsKZtP6VrbHlMK9HbNis=", "dependencies": {"@babel/runtime": "^7.26.0", "@mui/core-downloads-tracker": "^6.4.12", "@mui/system": "^6.4.12", "@mui/types": "~7.2.24", "@mui/utils": "^6.4.9", "@popperjs/core": "^2.11.8", "@types/react-transition-group": "^4.4.12", "clsx": "^2.1.1", "csstype": "^3.1.3", "prop-types": "^15.8.1", "react-is": "^19.0.0", "react-transition-group": "^4.4.5"}, "engines": {"node": ">=14.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/mui-org"}, "peerDependencies": {"@emotion/react": "^11.5.0", "@emotion/styled": "^11.3.0", "@mui/material-pigment-css": "^6.4.12", "@types/react": "^17.0.0 || ^18.0.0 || ^19.0.0", "react": "^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@emotion/react": {"optional": true}, "@emotion/styled": {"optional": true}, "@mui/material-pigment-css": {"optional": true}, "@types/react": {"optional": true}}}, "node_modules/@mui/material/node_modules/@mui/private-theming": {"version": "6.4.9", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@mui/private-theming/-/private-theming-6.4.9.tgz", "integrity": "sha1-DB1lpjihdAqtDrcV1552Rxq+gXU=", "dependencies": {"@babel/runtime": "^7.26.0", "@mui/utils": "^6.4.9", "prop-types": "^15.8.1"}, "engines": {"node": ">=14.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/mui-org"}, "peerDependencies": {"@types/react": "^17.0.0 || ^18.0.0 || ^19.0.0", "react": "^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@mui/material/node_modules/@mui/styled-engine": {"version": "6.4.11", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@mui/styled-engine/-/styled-engine-6.4.11.tgz", "integrity": "sha1-pI9IFlOClDAY9wUZ3h0x4DarsFQ=", "dependencies": {"@babel/runtime": "^7.26.0", "@emotion/cache": "^11.13.5", "@emotion/serialize": "^1.3.3", "@emotion/sheet": "^1.4.0", "csstype": "^3.1.3", "prop-types": "^15.8.1"}, "engines": {"node": ">=14.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/mui-org"}, "peerDependencies": {"@emotion/react": "^11.4.1", "@emotion/styled": "^11.3.0", "react": "^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@emotion/react": {"optional": true}, "@emotion/styled": {"optional": true}}}, "node_modules/@mui/material/node_modules/@mui/system": {"version": "6.4.12", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@mui/system/-/system-6.4.12.tgz", "integrity": "sha1-TowE5ZLFn0pCjGXHrrNuK6ZZ3N8=", "dependencies": {"@babel/runtime": "^7.26.0", "@mui/private-theming": "^6.4.9", "@mui/styled-engine": "^6.4.11", "@mui/types": "~7.2.24", "@mui/utils": "^6.4.9", "clsx": "^2.1.1", "csstype": "^3.1.3", "prop-types": "^15.8.1"}, "engines": {"node": ">=14.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/mui-org"}, "peerDependencies": {"@emotion/react": "^11.5.0", "@emotion/styled": "^11.3.0", "@types/react": "^17.0.0 || ^18.0.0 || ^19.0.0", "react": "^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@emotion/react": {"optional": true}, "@emotion/styled": {"optional": true}, "@types/react": {"optional": true}}}, "node_modules/@mui/private-theming": {"version": "7.2.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@mui/private-theming/-/private-theming-7.2.0.tgz", "integrity": "sha1-jWAeCUnIFZjaRiFVkYHxrIIx78U=", "peer": true, "dependencies": {"@babel/runtime": "^7.27.6", "@mui/utils": "^7.2.0", "prop-types": "^15.8.1"}, "engines": {"node": ">=14.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/mui-org"}, "peerDependencies": {"@types/react": "^17.0.0 || ^18.0.0 || ^19.0.0", "react": "^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@mui/private-theming/node_modules/@mui/types": {"version": "7.4.4", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@mui/types/-/types-7.4.4.tgz", "integrity": "sha1-DFzVaQUjHicJa0HQlvHJSMJr3V0=", "peer": true, "dependencies": {"@babel/runtime": "^7.27.6"}, "peerDependencies": {"@types/react": "^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@mui/private-theming/node_modules/@mui/utils": {"version": "7.2.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@mui/utils/-/utils-7.2.0.tgz", "integrity": "sha1-MQYml7QaqOqO8E49P63KHew+HeE=", "peer": true, "dependencies": {"@babel/runtime": "^7.27.6", "@mui/types": "^7.4.4", "@types/prop-types": "^15.7.15", "clsx": "^2.1.1", "prop-types": "^15.8.1", "react-is": "^19.1.0"}, "engines": {"node": ">=14.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/mui-org"}, "peerDependencies": {"@types/react": "^17.0.0 || ^18.0.0 || ^19.0.0", "react": "^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@mui/styled-engine": {"version": "7.2.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@mui/styled-engine/-/styled-engine-7.2.0.tgz", "integrity": "sha1-mL9avh+AravWbU+cE+qeSiYWkI4=", "peer": true, "dependencies": {"@babel/runtime": "^7.27.6", "@emotion/cache": "^11.14.0", "@emotion/serialize": "^1.3.3", "@emotion/sheet": "^1.4.0", "csstype": "^3.1.3", "prop-types": "^15.8.1"}, "engines": {"node": ">=14.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/mui-org"}, "peerDependencies": {"@emotion/react": "^11.4.1", "@emotion/styled": "^11.3.0", "react": "^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@emotion/react": {"optional": true}, "@emotion/styled": {"optional": true}}}, "node_modules/@mui/system": {"version": "7.2.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@mui/system/-/system-7.2.0.tgz", "integrity": "sha1-sT+Zy1k3kSIo8pF11upnhX1ibXA=", "peer": true, "dependencies": {"@babel/runtime": "^7.27.6", "@mui/private-theming": "^7.2.0", "@mui/styled-engine": "^7.2.0", "@mui/types": "^7.4.4", "@mui/utils": "^7.2.0", "clsx": "^2.1.1", "csstype": "^3.1.3", "prop-types": "^15.8.1"}, "engines": {"node": ">=14.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/mui-org"}, "peerDependencies": {"@emotion/react": "^11.5.0", "@emotion/styled": "^11.3.0", "@types/react": "^17.0.0 || ^18.0.0 || ^19.0.0", "react": "^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@emotion/react": {"optional": true}, "@emotion/styled": {"optional": true}, "@types/react": {"optional": true}}}, "node_modules/@mui/system/node_modules/@mui/types": {"version": "7.4.4", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@mui/types/-/types-7.4.4.tgz", "integrity": "sha1-DFzVaQUjHicJa0HQlvHJSMJr3V0=", "peer": true, "dependencies": {"@babel/runtime": "^7.27.6"}, "peerDependencies": {"@types/react": "^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@mui/system/node_modules/@mui/utils": {"version": "7.2.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@mui/utils/-/utils-7.2.0.tgz", "integrity": "sha1-MQYml7QaqOqO8E49P63KHew+HeE=", "peer": true, "dependencies": {"@babel/runtime": "^7.27.6", "@mui/types": "^7.4.4", "@types/prop-types": "^15.7.15", "clsx": "^2.1.1", "prop-types": "^15.8.1", "react-is": "^19.1.0"}, "engines": {"node": ">=14.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/mui-org"}, "peerDependencies": {"@types/react": "^17.0.0 || ^18.0.0 || ^19.0.0", "react": "^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@mui/types": {"version": "7.2.24", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@mui/types/-/types-7.2.24.tgz", "integrity": "sha1-Xv9jEp2cKdgLvy0uVhvQaQMU3sI=", "peerDependencies": {"@types/react": "^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@mui/utils": {"version": "6.4.9", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@mui/utils/-/utils-6.4.9.tgz", "integrity": "sha1-sN8B2qJUx8MqGjCzClF54Z7wcac=", "dependencies": {"@babel/runtime": "^7.26.0", "@mui/types": "~7.2.24", "@types/prop-types": "^15.7.14", "clsx": "^2.1.1", "prop-types": "^15.8.1", "react-is": "^19.0.0"}, "engines": {"node": ">=14.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/mui-org"}, "peerDependencies": {"@types/react": "^17.0.0 || ^18.0.0 || ^19.0.0", "react": "^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@mui/x-data-grid": {"version": "7.29.8", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@mui/x-data-grid/-/x-data-grid-7.29.8.tgz", "integrity": "sha1-6jd2wCxqpEzmsYrvKSQnCppNfmY=", "dependencies": {"@babel/runtime": "^7.25.7", "@mui/utils": "^5.16.6 || ^6.0.0 || ^7.0.0", "@mui/x-internals": "7.29.0", "clsx": "^2.1.1", "prop-types": "^15.8.1", "reselect": "^5.1.1", "use-sync-external-store": "^1.0.0"}, "engines": {"node": ">=14.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/mui-org"}, "peerDependencies": {"@emotion/react": "^11.9.0", "@emotion/styled": "^11.8.1", "@mui/material": "^5.15.14 || ^6.0.0 || ^7.0.0", "@mui/system": "^5.15.14 || ^6.0.0 || ^7.0.0", "react": "^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@emotion/react": {"optional": true}, "@emotion/styled": {"optional": true}}}, "node_modules/@mui/x-date-pickers": {"version": "7.29.4", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@mui/x-date-pickers/-/x-date-pickers-7.29.4.tgz", "integrity": "sha1-uICMuOKMHU5SizezNu/8gHTmX68=", "dependencies": {"@babel/runtime": "^7.25.7", "@mui/utils": "^5.16.6 || ^6.0.0 || ^7.0.0", "@mui/x-internals": "7.29.0", "@types/react-transition-group": "^4.4.11", "clsx": "^2.1.1", "prop-types": "^15.8.1", "react-transition-group": "^4.4.5"}, "engines": {"node": ">=14.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/mui-org"}, "peerDependencies": {"@emotion/react": "^11.9.0", "@emotion/styled": "^11.8.1", "@mui/material": "^5.15.14 || ^6.0.0 || ^7.0.0", "@mui/system": "^5.15.14 || ^6.0.0 || ^7.0.0", "date-fns": "^2.25.0 || ^3.2.0 || ^4.0.0", "date-fns-jalali": "^2.13.0-0 || ^3.2.0-0 || ^4.0.0-0", "dayjs": "^1.10.7", "luxon": "^3.0.2", "moment": "^2.29.4", "moment-hijri": "^2.1.2 || ^3.0.0", "moment-jalaali": "^0.7.4 || ^0.8.0 || ^0.9.0 || ^0.10.0", "react": "^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@emotion/react": {"optional": true}, "@emotion/styled": {"optional": true}, "date-fns": {"optional": true}, "date-fns-jalali": {"optional": true}, "dayjs": {"optional": true}, "luxon": {"optional": true}, "moment": {"optional": true}, "moment-hijri": {"optional": true}, "moment-jalaali": {"optional": true}}}, "node_modules/@mui/x-internals": {"version": "7.29.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@mui/x-internals/-/x-internals-7.29.0.tgz", "integrity": "sha1-HzU7aX7Rv1WUrFSVVq3i5oQfS/U=", "dependencies": {"@babel/runtime": "^7.25.7", "@mui/utils": "^5.16.6 || ^6.0.0 || ^7.0.0"}, "engines": {"node": ">=14.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/mui-org"}, "peerDependencies": {"react": "^17.0.0 || ^18.0.0 || ^19.0.0"}}, "node_modules/@nodelib/fs.scandir": {"version": "2.1.5", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz", "integrity": "sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U=", "dev": true, "dependencies": {"@nodelib/fs.stat": "2.0.5", "run-parallel": "^1.1.9"}, "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.stat": {"version": "2.0.5", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz", "integrity": "sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos=", "dev": true, "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.walk": {"version": "1.2.8", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz", "integrity": "sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po=", "dev": true, "dependencies": {"@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0"}, "engines": {"node": ">= 8"}}, "node_modules/@peculiar/asn1-schema": {"version": "2.3.15", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@peculiar/asn1-schema/-/asn1-schema-2.3.15.tgz", "integrity": "sha1-6Sa/3u1RlFoG84vnA0mefYNBpdM=", "dev": true, "dependencies": {"asn1js": "^3.0.5", "pvtsutils": "^1.3.6", "tslib": "^2.8.1"}}, "node_modules/@peculiar/json-schema": {"version": "1.1.12", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@peculiar/json-schema/-/json-schema-1.1.12.tgz", "integrity": "sha1-/mHoUlnjtbpa1WbLYsp1s9PNUzk=", "dev": true, "dependencies": {"tslib": "^2.0.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/@peculiar/webcrypto": {"version": "1.5.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@peculiar/webcrypto/-/webcrypto-1.5.0.tgz", "integrity": "sha1-nlcXTALBKRBRxVNgA0fhK4FGnhA=", "dev": true, "dependencies": {"@peculiar/asn1-schema": "^2.3.8", "@peculiar/json-schema": "^1.1.12", "pvtsutils": "^1.3.5", "tslib": "^2.6.2", "webcrypto-core": "^1.8.0"}, "engines": {"node": ">=10.12.0"}}, "node_modules/@pkgr/core": {"version": "0.2.7", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@pkgr/core/-/core-0.2.7.tgz", "integrity": "sha1-61AU39CwPn87ou7v9Qbu2JsCgFg=", "dev": true, "engines": {"node": "^12.20.0 || ^14.18.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/pkgr"}}, "node_modules/@popperjs/core": {"version": "2.11.8", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@popperjs/core/-/core-2.11.8.tgz", "integrity": "sha1-a3kDLnYKCJnNQgRxC+7elyo6GF8=", "funding": {"type": "opencollective", "url": "https://opencollective.com/popperjs"}}, "node_modules/@repeaterjs/repeater": {"version": "3.0.6", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@repeaterjs/repeater/-/repeater-3.0.6.tgz", "integrity": "sha1-viPfAUPO7Dxp+LbCUXlxpVeP2qI=", "dev": true}, "node_modules/@rolldown/pluginutils": {"version": "1.0.0-beta.19", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.19.tgz", "integrity": "sha1-/DuVFFqOejv5J1QmnY5PQO6ookQ=", "dev": true}, "node_modules/@rollup/rollup-android-arm-eabi": {"version": "4.44.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.44.2.tgz", "integrity": "sha1-aBm38eQaSa9Wb2KaFVbq7qd00EM=", "cpu": ["arm"], "dev": true, "optional": true, "os": ["android"]}, "node_modules/@rollup/rollup-android-arm64": {"version": "4.44.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.44.2.tgz", "integrity": "sha1-e9VZGvaMZKdb4XeeKyDxh4eNq6k=", "cpu": ["arm64"], "dev": true, "optional": true, "os": ["android"]}, "node_modules/@rollup/rollup-darwin-arm64": {"version": "4.44.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.44.2.tgz", "integrity": "sha1-4hbDM+RIxnlzOG5G2/6OOBqvsFU=", "cpu": ["arm64"], "dev": true, "optional": true, "os": ["darwin"]}, "node_modules/@rollup/rollup-darwin-x64": {"version": "4.44.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.44.2.tgz", "integrity": "sha1-IC+A7qOs/j9nSW/t/6AGpfHOf1o=", "cpu": ["x64"], "dev": true, "optional": true, "os": ["darwin"]}, "node_modules/@rollup/rollup-freebsd-arm64": {"version": "4.44.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.44.2.tgz", "integrity": "sha1-SID5dp8afuxDa5wUbh1xQzjCZWc=", "cpu": ["arm64"], "dev": true, "optional": true, "os": ["freebsd"]}, "node_modules/@rollup/rollup-freebsd-x64": {"version": "4.44.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.44.2.tgz", "integrity": "sha1-ZH1uMzNJscD7MiwoJ7oaU6DxAwE=", "cpu": ["x64"], "dev": true, "optional": true, "os": ["freebsd"]}, "node_modules/@rollup/rollup-linux-arm-gnueabihf": {"version": "4.44.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.44.2.tgz", "integrity": "sha1-e6XJenIk9JYYhh0JPEp7QPpQhns=", "cpu": ["arm"], "dev": true, "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-arm-musleabihf": {"version": "4.44.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@rollup/rollup-linux-arm-musleabihf/-/rollup-linux-arm-musleabihf-4.44.2.tgz", "integrity": "sha1-+Fjc9JgpnWxiXsaXpRkeDkFCOQU=", "cpu": ["arm"], "dev": true, "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-arm64-gnu": {"version": "4.44.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.44.2.tgz", "integrity": "sha1-wPH8IMUGZsYfV0U2oAzdSGtqquE=", "cpu": ["arm64"], "dev": true, "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-arm64-musl": {"version": "4.44.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.44.2.tgz", "integrity": "sha1-AhTvw+QE3fEI6UatX35O4nkqFVo=", "cpu": ["arm64"], "dev": true, "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-loongarch64-gnu": {"version": "4.44.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.44.2.tgz", "integrity": "sha1-gwPE6irnvLuWssd8+1NSfZZL/Os=", "cpu": ["loong64"], "dev": true, "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-powerpc64le-gnu": {"version": "4.44.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.44.2.tgz", "integrity": "sha1-QZf/vGGAlikJTA/M+CXkOkD7wMo=", "cpu": ["ppc64"], "dev": true, "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-riscv64-gnu": {"version": "4.44.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.44.2.tgz", "integrity": "sha1-vLmckATJuR43BKanDIkssFmbH0I=", "cpu": ["riscv64"], "dev": true, "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-riscv64-musl": {"version": "4.44.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@rollup/rollup-linux-riscv64-musl/-/rollup-linux-riscv64-musl-4.44.2.tgz", "integrity": "sha1-PpQ7rpuLRjfFc8GSI5K+uKXoGss=", "cpu": ["riscv64"], "dev": true, "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-s390x-gnu": {"version": "4.44.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.44.2.tgz", "integrity": "sha1-3EP7Rnv/lUf1uZN/OGaNoH+o+p8=", "cpu": ["s390x"], "dev": true, "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-x64-gnu": {"version": "4.44.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.44.2.tgz", "integrity": "sha1-BpnFYPps5rhGWBp+bDDIXCKj8No=", "cpu": ["x64"], "dev": true, "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-x64-musl": {"version": "4.44.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.44.2.tgz", "integrity": "sha1-n7G+ztzcniJ9R0hXbri6L62NLik=", "cpu": ["x64"], "dev": true, "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-win32-arm64-msvc": {"version": "4.44.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.44.2.tgz", "integrity": "sha1-/PPmLt12xWAlK4GfaWJ2hfZYh9c=", "cpu": ["arm64"], "dev": true, "optional": true, "os": ["win32"]}, "node_modules/@rollup/rollup-win32-ia32-msvc": {"version": "4.44.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.44.2.tgz", "integrity": "sha1-RaUwRJHW2kZm9hWb5Pc51NQ6KD8=", "cpu": ["ia32"], "dev": true, "optional": true, "os": ["win32"]}, "node_modules/@rollup/rollup-win32-x64-msvc": {"version": "4.44.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.44.2.tgz", "integrity": "sha1-ZgAYyWlq1PSKvoxdVttTyBqtuiU=", "cpu": ["x64"], "dev": true, "optional": true, "os": ["win32"]}, "node_modules/@types/babel__core": {"version": "7.20.5", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/babel__core/-/babel__core-7.20.5.tgz", "integrity": "sha1-PfFfJ7qFMZyqB7oI0HIYibs5wBc=", "dev": true, "dependencies": {"@babel/parser": "^7.20.7", "@babel/types": "^7.20.7", "@types/babel__generator": "*", "@types/babel__template": "*", "@types/babel__traverse": "*"}}, "node_modules/@types/babel__generator": {"version": "7.27.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/babel__generator/-/babel__generator-7.27.0.tgz", "integrity": "sha1-tYGSlMUReZV6+uw0FEL5NB5BCKk=", "dev": true, "dependencies": {"@babel/types": "^7.0.0"}}, "node_modules/@types/babel__template": {"version": "7.4.4", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/babel__template/-/babel__template-7.4.4.tgz", "integrity": "sha1-VnJRNwHBshmbxtrWNqnXSRWGdm8=", "dev": true, "dependencies": {"@babel/parser": "^7.1.0", "@babel/types": "^7.0.0"}}, "node_modules/@types/babel__traverse": {"version": "7.20.7", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/babel__traverse/-/babel__traverse-7.20.7.tgz", "integrity": "sha1-lozcI2bsPaFZ9hFmQo7kDzcOVsI=", "dev": true, "dependencies": {"@babel/types": "^7.20.7"}}, "node_modules/@types/d3-array": {"version": "3.2.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/d3-array/-/d3-array-3.2.1.tgz", "integrity": "sha1-H2ZY49IAbE/OrFP95GQWaFn4uMU="}, "node_modules/@types/d3-color": {"version": "3.1.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/d3-color/-/d3-color-3.1.3.tgz", "integrity": "sha1-NoyWGhjech2oIA6AvzlD+1MTavI="}, "node_modules/@types/d3-ease": {"version": "3.0.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/d3-ease/-/d3-ease-3.0.2.tgz", "integrity": "sha1-4o2xv7+mFwdvd3DdHZpI6qO2xRs="}, "node_modules/@types/d3-interpolate": {"version": "3.0.4", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/d3-interpolate/-/d3-interpolate-3.0.4.tgz", "integrity": "sha1-QSuQ6EhwKF8v+KhGxutgNE8SpBw=", "dependencies": {"@types/d3-color": "*"}}, "node_modules/@types/d3-path": {"version": "3.1.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/d3-path/-/d3-path-3.1.1.tgz", "integrity": "sha1-9jKzgMOsoduo40qgSbzWpK8j34o="}, "node_modules/@types/d3-scale": {"version": "4.0.9", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/d3-scale/-/d3-scale-4.0.9.tgz", "integrity": "sha1-V6L3ByQub+Hega17/Myq9gYXmvs=", "dependencies": {"@types/d3-time": "*"}}, "node_modules/@types/d3-shape": {"version": "3.1.7", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/d3-shape/-/d3-shape-3.1.7.tgz", "integrity": "sha1-K3tCPcLf5pyMk1luZz43RDNIxVU=", "dependencies": {"@types/d3-path": "*"}}, "node_modules/@types/d3-time": {"version": "3.0.4", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/d3-time/-/d3-time-3.0.4.tgz", "integrity": "sha1-hHL+7NY5aRRQ3YAA6zPt1EThMj8="}, "node_modules/@types/d3-timer": {"version": "3.0.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/d3-timer/-/d3-timer-3.0.2.tgz", "integrity": "sha1-cLvad9wjqnJ0E+IuIUr6Pw6FL3A="}, "node_modules/@types/estree": {"version": "1.0.8", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/estree/-/estree-1.0.8.tgz", "integrity": "sha1-lYuRyZGxhnztMYvt6g4hXuBQcm4=", "dev": true}, "node_modules/@types/file-saver": {"version": "2.0.7", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/file-saver/-/file-saver-2.0.7.tgz", "integrity": "sha1-jbsvJL3HSGxUqoVOtBSUC70Fb30=", "dev": true}, "node_modules/@types/js-yaml": {"version": "4.0.9", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/js-yaml/-/js-yaml-4.0.9.tgz", "integrity": "sha1-zYI4LE+QL+2WkaLteexoxYmK9MI=", "dev": true}, "node_modules/@types/json-schema": {"version": "7.0.15", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/json-schema/-/json-schema-7.0.15.tgz", "integrity": "sha1-WWoXRyM2lNUPatinhp/Lb1bPWEE=", "dev": true}, "node_modules/@types/lodash": {"version": "4.17.20", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/lodash/-/lodash-4.17.20.tgz", "integrity": "sha1-HKdzYdc2NDLSn15VlQ2eweHG6pM=", "dev": true}, "node_modules/@types/node": {"version": "22.16.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/node/-/node-22.16.0.tgz", "integrity": "sha1-NSvElR/Qid8y8rZBKmHTObZ97Ys=", "dev": true, "dependencies": {"undici-types": "~6.21.0"}}, "node_modules/@types/parse-json": {"version": "4.0.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/parse-json/-/parse-json-4.0.2.tgz", "integrity": "sha1-WVDlCWB5MFWEXpVsQn/CsNcMUjk="}, "node_modules/@types/prop-types": {"version": "15.7.15", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/prop-types/-/prop-types-15.7.15.tgz", "integrity": "sha1-5uWobWAr6spxzlFj+t9fldcJMcc="}, "node_modules/@types/react": {"version": "19.1.8", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/react/-/react-19.1.8.tgz", "integrity": "sha1-/4OV8q+3ZFlyZc7RX43dsHIK4cM=", "dependencies": {"csstype": "^3.0.2"}}, "node_modules/@types/react-dom": {"version": "19.1.6", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/react-dom/-/react-dom-19.1.6.tgz", "integrity": "sha1-SvYp2g6fnA9Qb8TRyqYQOZxZXWQ=", "dev": true, "peerDependencies": {"@types/react": "^19.0.0"}}, "node_modules/@types/react-transition-group": {"version": "4.4.12", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/react-transition-group/-/react-transition-group-4.4.12.tgz", "integrity": "sha1-tddlaEhbAqMHI4Jwv+lstR7ioEQ=", "peerDependencies": {"@types/react": "*"}}, "node_modules/@types/ws": {"version": "8.18.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/ws/-/ws-8.18.1.tgz", "integrity": "sha1-SEZOS/Ld/RfbE9hFRn9gcP/qSqk=", "dev": true, "dependencies": {"@types/node": "*"}}, "node_modules/@typescript-eslint/eslint-plugin": {"version": "8.35.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@typescript-eslint/eslint-plugin/-/eslint-plugin-8.35.1.tgz", "integrity": "sha1-BrESn+JtZTKr1Y+ys/6YEL0BaTU=", "dev": true, "dependencies": {"@eslint-community/regexpp": "^4.10.0", "@typescript-eslint/scope-manager": "8.35.1", "@typescript-eslint/type-utils": "8.35.1", "@typescript-eslint/utils": "8.35.1", "@typescript-eslint/visitor-keys": "8.35.1", "graphemer": "^1.4.0", "ignore": "^7.0.0", "natural-compare": "^1.4.0", "ts-api-utils": "^2.1.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"@typescript-eslint/parser": "^8.35.1", "eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <5.9.0"}}, "node_modules/@typescript-eslint/eslint-plugin/node_modules/ignore": {"version": "7.0.5", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/ignore/-/ignore-7.0.5.tgz", "integrity": "sha1-TLX2zX1MerA2VzjHrqiIuqbX79k=", "dev": true, "engines": {"node": ">= 4"}}, "node_modules/@typescript-eslint/parser": {"version": "8.35.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@typescript-eslint/parser/-/parser-8.35.1.tgz", "integrity": "sha1-eHMS6A8PM32F9MKlaUEcRp6FLUQ=", "dev": true, "dependencies": {"@typescript-eslint/scope-manager": "8.35.1", "@typescript-eslint/types": "8.35.1", "@typescript-eslint/typescript-estree": "8.35.1", "@typescript-eslint/visitor-keys": "8.35.1", "debug": "^4.3.4"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <5.9.0"}}, "node_modules/@typescript-eslint/project-service": {"version": "8.35.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@typescript-eslint/project-service/-/project-service-8.35.1.tgz", "integrity": "sha1-gVu3cfL2yXeA5EKZQ07OPC5SYSc=", "dev": true, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.35.1", "@typescript-eslint/types": "^8.35.1", "debug": "^4.3.4"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}}, "node_modules/@typescript-eslint/scope-manager": {"version": "8.35.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@typescript-eslint/scope-manager/-/scope-manager-8.35.1.tgz", "integrity": "sha1-sZ+b5lyNEFnoijI6GmVn2/4KGk4=", "dev": true, "dependencies": {"@typescript-eslint/types": "8.35.1", "@typescript-eslint/visitor-keys": "8.35.1"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@typescript-eslint/tsconfig-utils": {"version": "8.35.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.35.1.tgz", "integrity": "sha1-wtuHFMGBzAcAIWwaLjz1VxnFgAY=", "dev": true, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}}, "node_modules/@typescript-eslint/type-utils": {"version": "8.35.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@typescript-eslint/type-utils/-/type-utils-8.35.1.tgz", "integrity": "sha1-T5oH1u+g5hemfhiQ0oEX5ozhVL0=", "dev": true, "dependencies": {"@typescript-eslint/typescript-estree": "8.35.1", "@typescript-eslint/utils": "8.35.1", "debug": "^4.3.4", "ts-api-utils": "^2.1.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <5.9.0"}}, "node_modules/@typescript-eslint/types": {"version": "8.35.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@typescript-eslint/types/-/types-8.35.1.tgz", "integrity": "sha1-Q0Tc+TRJW78lqfg6Bt2f4qzxV4A=", "dev": true, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@typescript-eslint/typescript-estree": {"version": "8.35.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@typescript-eslint/typescript-estree/-/typescript-estree-8.35.1.tgz", "integrity": "sha1-uA6F/La/vLrLMiSxNn9so/A+YYM=", "dev": true, "dependencies": {"@typescript-eslint/project-service": "8.35.1", "@typescript-eslint/tsconfig-utils": "8.35.1", "@typescript-eslint/types": "8.35.1", "@typescript-eslint/visitor-keys": "8.35.1", "debug": "^4.3.4", "fast-glob": "^3.3.2", "is-glob": "^4.0.3", "minimatch": "^9.0.4", "semver": "^7.6.0", "ts-api-utils": "^2.1.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}}, "node_modules/@typescript-eslint/typescript-estree/node_modules/brace-expansion": {"version": "2.0.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/brace-expansion/-/brace-expansion-2.0.2.tgz", "integrity": "sha1-VPxTI3phPYVMe9N0Y6rRffhyFOc=", "dev": true, "dependencies": {"balanced-match": "^1.0.0"}}, "node_modules/@typescript-eslint/typescript-estree/node_modules/minimatch": {"version": "9.0.5", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/minimatch/-/minimatch-9.0.5.tgz", "integrity": "sha1-10+d1rV9g9jpjPuCEzsDl4vJKeU=", "dev": true, "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/@typescript-eslint/typescript-estree/node_modules/semver": {"version": "7.7.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/semver/-/semver-7.7.2.tgz", "integrity": "sha1-Z9mf3NNc7CHm+Lh6f9UVoz+YK1g=", "dev": true, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/@typescript-eslint/utils": {"version": "8.35.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@typescript-eslint/utils/-/utils-8.35.1.tgz", "integrity": "sha1-qaDO64HJ0TLz91U3rSyn9somZSM=", "dev": true, "dependencies": {"@eslint-community/eslint-utils": "^4.7.0", "@typescript-eslint/scope-manager": "8.35.1", "@typescript-eslint/types": "8.35.1", "@typescript-eslint/typescript-estree": "8.35.1"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <5.9.0"}}, "node_modules/@typescript-eslint/visitor-keys": {"version": "8.35.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@typescript-eslint/visitor-keys/-/visitor-keys-8.35.1.tgz", "integrity": "sha1-qseKsiZd0RknvGrz+cWgIbvBZwo=", "dev": true, "dependencies": {"@typescript-eslint/types": "8.35.1", "eslint-visitor-keys": "^4.2.1"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@vitejs/plugin-react": {"version": "4.6.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@vitejs/plugin-react/-/plugin-react-4.6.0.tgz", "integrity": "sha1-Jwe0hfRIBtQtQcY5IYg8/5xU36o=", "dev": true, "dependencies": {"@babel/core": "^7.27.4", "@babel/plugin-transform-react-jsx-self": "^7.27.1", "@babel/plugin-transform-react-jsx-source": "^7.27.1", "@rolldown/pluginutils": "1.0.0-beta.19", "@types/babel__core": "^7.20.5", "react-refresh": "^0.17.0"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "peerDependencies": {"vite": "^4.2.0 || ^5.0.0 || ^6.0.0 || ^7.0.0-beta.0"}}, "node_modules/@whatwg-node/disposablestack": {"version": "0.0.6", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@whatwg-node/disposablestack/-/disposablestack-0.0.6.tgz", "integrity": "sha1-IGShQl6mYZTe9t8MehhRtpOcgrs=", "dev": true, "dependencies": {"@whatwg-node/promise-helpers": "^1.0.0", "tslib": "^2.6.3"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@whatwg-node/events": {"version": "0.0.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@whatwg-node/events/-/events-0.0.3.tgz", "integrity": "sha1-E6Zd1PWJP1UoD3ZuKa5IB0knrK0=", "dev": true}, "node_modules/@whatwg-node/fetch": {"version": "0.8.8", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@whatwg-node/fetch/-/fetch-0.8.8.tgz", "integrity": "sha1-SMatDGt5Uac+gS8J3SLXXp+hjK4=", "dev": true, "dependencies": {"@peculiar/webcrypto": "^1.4.0", "@whatwg-node/node-fetch": "^0.3.6", "busboy": "^1.6.0", "urlpattern-polyfill": "^8.0.0", "web-streams-polyfill": "^3.2.1"}}, "node_modules/@whatwg-node/node-fetch": {"version": "0.3.6", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@whatwg-node/node-fetch/-/node-fetch-0.3.6.tgz", "integrity": "sha1-4ogWlV81mRbi2DC2imRJMST6ptA=", "dev": true, "dependencies": {"@whatwg-node/events": "^0.0.3", "busboy": "^1.6.0", "fast-querystring": "^1.1.1", "fast-url-parser": "^1.1.3", "tslib": "^2.3.1"}}, "node_modules/@whatwg-node/promise-helpers": {"version": "1.3.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@whatwg-node/promise-helpers/-/promise-helpers-1.3.2.tgz", "integrity": "sha1-O1SYetZRfvbbWSDGam8NraYGWH0=", "dev": true, "dependencies": {"tslib": "^2.6.3"}, "engines": {"node": ">=16.0.0"}}, "node_modules/@wry/caches": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@wry/caches/-/caches-1.0.1.tgz", "integrity": "sha1-hkH9O24JIwuGzouTVY1Ezx7OflI=", "dependencies": {"tslib": "^2.3.0"}, "engines": {"node": ">=8"}}, "node_modules/@wry/context": {"version": "0.7.4", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@wry/context/-/context-0.7.4.tgz", "integrity": "sha1-4y11D6B1lVxKss+4xICV4dQtWZA=", "dependencies": {"tslib": "^2.3.0"}, "engines": {"node": ">=8"}}, "node_modules/@wry/equality": {"version": "0.5.7", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@wry/equality/-/equality-0.5.7.tgz", "integrity": "sha1-cuwac3YJQ9Q51Wt7HpmFrsXUl7s=", "dependencies": {"tslib": "^2.3.0"}, "engines": {"node": ">=8"}}, "node_modules/@wry/trie": {"version": "0.5.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@wry/trie/-/trie-0.5.0.tgz", "integrity": "sha1-EeeD86U/bkzR1C0tEyP1vD+pnJQ=", "dependencies": {"tslib": "^2.3.0"}, "engines": {"node": ">=8"}}, "node_modules/acorn": {"version": "8.15.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/acorn/-/acorn-8.15.0.tgz", "integrity": "sha1-o2CJi8QV7arEbIJB9jg5dbkwuBY=", "dev": true, "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/acorn-jsx": {"version": "5.3.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/acorn-jsx/-/acorn-jsx-5.3.2.tgz", "integrity": "sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=", "dev": true, "peerDependencies": {"acorn": "^6.0.0 || ^7.0.0 || ^8.0.0"}}, "node_modules/agent-base": {"version": "7.1.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/agent-base/-/agent-base-7.1.3.tgz", "integrity": "sha1-KUNeuCG8QZRjOluJ5bxHA7r8JaE=", "dev": true, "engines": {"node": ">= 14"}}, "node_modules/aggregate-error": {"version": "3.1.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/aggregate-error/-/aggregate-error-3.1.0.tgz", "integrity": "sha1-kmcP9Q9TWb23o+DUDQ7DDFc3aHo=", "dev": true, "dependencies": {"clean-stack": "^2.0.0", "indent-string": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/ajv": {"version": "6.12.6", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/ajv/-/ajv-6.12.6.tgz", "integrity": "sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=", "dev": true, "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/ansi-escapes": {"version": "4.3.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/ansi-escapes/-/ansi-escapes-4.3.2.tgz", "integrity": "sha1-ayKR0dt9mLZSHV8e+kLQ86n+tl4=", "dev": true, "dependencies": {"type-fest": "^0.21.3"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/ansi-regex": {"version": "5.0.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/ansi-regex/-/ansi-regex-5.0.1.tgz", "integrity": "sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=", "dev": true, "engines": {"node": ">=8"}}, "node_modules/ansi-styles": {"version": "4.3.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/ansi-styles/-/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "dev": true, "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/argparse": {"version": "2.0.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/argparse/-/argparse-2.0.1.tgz", "integrity": "sha1-JG9Q88p4oyQPbJl+ipvR6sSeSzg=", "dev": true}, "node_modules/array-union": {"version": "2.1.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/array-union/-/array-union-2.1.0.tgz", "integrity": "sha1-t5hCCtvrHego2ErNii4j0+/oXo0=", "dev": true, "engines": {"node": ">=8"}}, "node_modules/asap": {"version": "2.0.6", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/asap/-/asap-2.0.6.tgz", "integrity": "sha1-5QNHYR1+aQlDIIu9r+vLwvuGbUY=", "dev": true}, "node_modules/asn1js": {"version": "3.0.6", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/asn1js/-/asn1js-3.0.6.tgz", "integrity": "sha1-U+AC6+AMX3/XfBwEfDVX18BNziU=", "dev": true, "dependencies": {"pvtsutils": "^1.3.6", "pvutils": "^1.1.3", "tslib": "^2.8.1"}, "engines": {"node": ">=12.0.0"}}, "node_modules/astral-regex": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/astral-regex/-/astral-regex-2.0.0.tgz", "integrity": "sha1-SDFDxWeu7UeFdZwIZXhtx319LjE=", "dev": true, "engines": {"node": ">=8"}}, "node_modules/auto-bind": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/auto-bind/-/auto-bind-4.0.0.tgz", "integrity": "sha1-41ifxsLaj3ykO6n4T6UqdE/Jl/s=", "dev": true, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/babel-plugin-macros": {"version": "3.1.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/babel-plugin-macros/-/babel-plugin-macros-3.1.0.tgz", "integrity": "sha1-nvbcdN65NLTbNE3Jc+6FHRSMUME=", "dependencies": {"@babel/runtime": "^7.12.5", "cosmiconfig": "^7.0.0", "resolve": "^1.19.0"}, "engines": {"node": ">=10", "npm": ">=6"}}, "node_modules/babel-plugin-macros/node_modules/cosmiconfig": {"version": "7.1.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/cosmiconfig/-/cosmiconfig-7.1.0.tgz", "integrity": "sha1-FEO5r6WWtnAILqRsvY9qYrhGNfY=", "dependencies": {"@types/parse-json": "^4.0.0", "import-fresh": "^3.2.1", "parse-json": "^5.0.0", "path-type": "^4.0.0", "yaml": "^1.10.0"}, "engines": {"node": ">=10"}}, "node_modules/babel-plugin-macros/node_modules/yaml": {"version": "1.10.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/yaml/-/yaml-1.10.2.tgz", "integrity": "sha1-IwHF/78StGfejaIzOkWeKeeSDks=", "engines": {"node": ">= 6"}}, "node_modules/balanced-match": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/balanced-match/-/balanced-match-1.0.2.tgz", "integrity": "sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=", "dev": true}, "node_modules/base64-js": {"version": "1.5.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/base64-js/-/base64-js-1.5.1.tgz", "integrity": "sha1-GxtEAWClv3rUC2UPCVljSBkDkwo=", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}, "node_modules/bl": {"version": "4.1.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/bl/-/bl-4.1.0.tgz", "integrity": "sha1-RRU1JkGCvsL7vIOmKrmM8R2fezo=", "dev": true, "dependencies": {"buffer": "^5.5.0", "inherits": "^2.0.4", "readable-stream": "^3.4.0"}}, "node_modules/brace-expansion": {"version": "1.1.12", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/brace-expansion/-/brace-expansion-1.1.12.tgz", "integrity": "sha1-q5tFRGblqMw6GHvqrVgEEqnFuEM=", "dev": true, "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/braces": {"version": "3.0.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/braces/-/braces-3.0.3.tgz", "integrity": "sha1-SQMy9AkZRSJy1VqEgK3AxEE1h4k=", "dev": true, "dependencies": {"fill-range": "^7.1.1"}, "engines": {"node": ">=8"}}, "node_modules/browserslist": {"version": "4.25.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/browserslist/-/browserslist-4.25.1.tgz", "integrity": "sha1-up6ObymKHYb4Kcm5deB5SJZ7sRE=", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "dependencies": {"caniuse-lite": "^1.0.30001726", "electron-to-chromium": "^1.5.173", "node-releases": "^2.0.19", "update-browserslist-db": "^1.1.3"}, "bin": {"browserslist": "cli.js"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}}, "node_modules/bser": {"version": "2.1.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/bser/-/bser-2.1.1.tgz", "integrity": "sha1-5nh9og7OnQeZhTPP2d5vXDj0vAU=", "dev": true, "dependencies": {"node-int64": "^0.4.0"}}, "node_modules/buffer": {"version": "5.7.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/buffer/-/buffer-5.7.1.tgz", "integrity": "sha1-umLnwTEzBTWCGXFghRqPZI6Z7tA=", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.1.13"}}, "node_modules/busboy": {"version": "1.6.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/busboy/-/busboy-1.6.0.tgz", "integrity": "sha1-lm6japUC5DzbkUaWJSO5L1MfaJM=", "dev": true, "dependencies": {"streamsearch": "^1.1.0"}, "engines": {"node": ">=10.16.0"}}, "node_modules/callsites": {"version": "3.1.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/callsites/-/callsites-3.1.0.tgz", "integrity": "sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=", "engines": {"node": ">=6"}}, "node_modules/camel-case": {"version": "4.1.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/camel-case/-/camel-case-4.1.2.tgz", "integrity": "sha1-lygHKpVPgFIoIlpt7qazhGHhvVo=", "dev": true, "dependencies": {"pascal-case": "^3.1.2", "tslib": "^2.0.3"}}, "node_modules/caniuse-lite": {"version": "1.0.30001727", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/caniuse-lite/-/caniuse-lite-1.0.30001727.tgz", "integrity": "sha1-IulwZCKtN6pQVWr4wQ5A4tk6i4U=", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/caniuse-lite"}, {"type": "github", "url": "https://github.com/sponsors/ai"}]}, "node_modules/capital-case": {"version": "1.0.4", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/capital-case/-/capital-case-1.0.4.tgz", "integrity": "sha1-nRMCkjU8kkn2sA+lhSvuOKcX5mk=", "dev": true, "dependencies": {"no-case": "^3.0.4", "tslib": "^2.0.3", "upper-case-first": "^2.0.2"}}, "node_modules/chalk": {"version": "4.1.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/chalk/-/chalk-4.1.2.tgz", "integrity": "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=", "dev": true, "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/change-case": {"version": "4.1.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/change-case/-/change-case-4.1.2.tgz", "integrity": "sha1-/t/F8TYEXiOYwEEO5EH5VwRkHhI=", "dev": true, "dependencies": {"camel-case": "^4.1.2", "capital-case": "^1.0.4", "constant-case": "^3.0.4", "dot-case": "^3.0.4", "header-case": "^2.0.4", "no-case": "^3.0.4", "param-case": "^3.0.4", "pascal-case": "^3.1.2", "path-case": "^3.0.4", "sentence-case": "^3.0.4", "snake-case": "^3.0.4", "tslib": "^2.0.3"}}, "node_modules/change-case-all": {"version": "1.0.15", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/change-case-all/-/change-case-all-1.0.15.tgz", "integrity": "sha1-3ik5MWf8EB1kbNdrDvI+J9CXVq0=", "dev": true, "dependencies": {"change-case": "^4.1.2", "is-lower-case": "^2.0.2", "is-upper-case": "^2.0.2", "lower-case": "^2.0.2", "lower-case-first": "^2.0.2", "sponge-case": "^1.0.1", "swap-case": "^2.0.2", "title-case": "^3.0.3", "upper-case": "^2.0.2", "upper-case-first": "^2.0.2"}}, "node_modules/chardet": {"version": "0.7.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/chardet/-/chardet-0.7.0.tgz", "integrity": "sha1-kAlISfCTfy7twkJdDSip5fDLrZ4=", "dev": true}, "node_modules/clean-stack": {"version": "2.2.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/clean-stack/-/clean-stack-2.2.0.tgz", "integrity": "sha1-7oRy27Ep5yezHooQpCfe6d/kAIs=", "dev": true, "engines": {"node": ">=6"}}, "node_modules/cli-cursor": {"version": "3.1.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/cli-cursor/-/cli-cursor-3.1.0.tgz", "integrity": "sha1-JkMFp65JDR0Dvwybp8kl0XU68wc=", "dev": true, "dependencies": {"restore-cursor": "^3.1.0"}, "engines": {"node": ">=8"}}, "node_modules/cli-spinners": {"version": "2.9.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/cli-spinners/-/cli-spinners-2.9.2.tgz", "integrity": "sha1-F3Oo9LnE1qwxVj31Oz/B15Ri/kE=", "dev": true, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/cli-truncate": {"version": "2.1.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/cli-truncate/-/cli-truncate-2.1.0.tgz", "integrity": "sha1-w54ovwXtzeW+O5iZKiLe7Vork8c=", "dev": true, "dependencies": {"slice-ansi": "^3.0.0", "string-width": "^4.2.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/cli-width": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/cli-width/-/cli-width-3.0.0.tgz", "integrity": "sha1-ovSEN6LKqaIkNueUvwceyeYc7fY=", "dev": true, "engines": {"node": ">= 10"}}, "node_modules/cliui": {"version": "8.0.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/cliui/-/cliui-8.0.1.tgz", "integrity": "sha1-DASwddsCy/5g3I5s8vVIaxo2CKo=", "dev": true, "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.1", "wrap-ansi": "^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/cliui/node_modules/wrap-ansi": {"version": "7.0.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "integrity": "sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=", "dev": true, "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/clone": {"version": "1.0.4", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/clone/-/clone-1.0.4.tgz", "integrity": "sha1-2jCcwmPfFZlMaIypAheco8fNfH4=", "dev": true, "engines": {"node": ">=0.8"}}, "node_modules/clsx": {"version": "2.1.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/clsx/-/clsx-2.1.1.tgz", "integrity": "sha1-7tOXyf2L2IK/sY3qtxAgSaLzKZk=", "engines": {"node": ">=6"}}, "node_modules/color-convert": {"version": "2.0.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/color-convert/-/color-convert-2.0.1.tgz", "integrity": "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=", "dev": true, "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/color-name/-/color-name-1.1.4.tgz", "integrity": "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=", "dev": true}, "node_modules/colorette": {"version": "2.0.20", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/colorette/-/colorette-2.0.20.tgz", "integrity": "sha1-nreT5oMwZ/cjWQL807CZF6AAqVo=", "dev": true}, "node_modules/common-tags": {"version": "1.8.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/common-tags/-/common-tags-1.8.2.tgz", "integrity": "sha1-lOuzwHbSYDJ0X9VPrOf2iO9aycY=", "dev": true, "engines": {"node": ">=4.0.0"}}, "node_modules/concat-map": {"version": "0.0.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/concat-map/-/concat-map-0.0.1.tgz", "integrity": "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=", "dev": true}, "node_modules/constant-case": {"version": "3.0.4", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/constant-case/-/constant-case-3.0.4.tgz", "integrity": "sha1-O4Sprq9M8x7EXmv13pG9+wWJ+vE=", "dev": true, "dependencies": {"no-case": "^3.0.4", "tslib": "^2.0.3", "upper-case": "^2.0.2"}}, "node_modules/convert-source-map": {"version": "1.9.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/convert-source-map/-/convert-source-map-1.9.0.tgz", "integrity": "sha1-f6rmI1P7QhM2bQypg1jSLoNosF8="}, "node_modules/cookie": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/cookie/-/cookie-1.0.2.tgz", "integrity": "sha1-JzYHAVMhFr0/H5QWkp0Xav4eRhA=", "engines": {"node": ">=18"}}, "node_modules/cosmiconfig": {"version": "8.3.6", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/cosmiconfig/-/cosmiconfig-8.3.6.tgz", "integrity": "sha1-Bgorhx1m26bIU46hEYuhrBb1+uM=", "dev": true, "dependencies": {"import-fresh": "^3.3.0", "js-yaml": "^4.1.0", "parse-json": "^5.2.0", "path-type": "^4.0.0"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/d-fischer"}, "peerDependencies": {"typescript": ">=4.9.5"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/cross-fetch": {"version": "3.2.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/cross-fetch/-/cross-fetch-3.2.0.tgz", "integrity": "sha1-NOkZL1O8dX1mFDBNnl5vtO23guM=", "dev": true, "dependencies": {"node-fetch": "^2.7.0"}}, "node_modules/cross-inspect": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/cross-inspect/-/cross-inspect-1.0.1.tgz", "integrity": "sha1-Ffb2XkypY89MwaK1/vGPbKMocSs=", "dev": true, "dependencies": {"tslib": "^2.4.0"}, "engines": {"node": ">=16.0.0"}}, "node_modules/cross-spawn": {"version": "7.0.6", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/cross-spawn/-/cross-spawn-7.0.6.tgz", "integrity": "sha1-ilj+ePANzXDDcEUXWd+/rwPo7p8=", "dev": true, "dependencies": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "engines": {"node": ">= 8"}}, "node_modules/csstype": {"version": "3.1.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/csstype/-/csstype-3.1.3.tgz", "integrity": "sha1-2A/ylNEU+w5qxQD7+FtgE31+/4E="}, "node_modules/d3-array": {"version": "3.2.4", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/d3-array/-/d3-array-3.2.4.tgz", "integrity": "sha1-Ff7DOyN/l6xdfJhtx32ic6jtC7U=", "dependencies": {"internmap": "1 - 2"}, "engines": {"node": ">=12"}}, "node_modules/d3-color": {"version": "3.1.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/d3-color/-/d3-color-3.1.0.tgz", "integrity": "sha1-OVsoM9+scVB/EqwvevI7+BneJOI=", "engines": {"node": ">=12"}}, "node_modules/d3-ease": {"version": "3.0.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/d3-ease/-/d3-ease-3.0.1.tgz", "integrity": "sha1-llisOKIUDVnTRhYPH2ww/aC9EvQ=", "engines": {"node": ">=12"}}, "node_modules/d3-format": {"version": "3.1.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/d3-format/-/d3-format-3.1.0.tgz", "integrity": "sha1-kmDiOijqXLEJ6TshoG4k4uvVVkE=", "engines": {"node": ">=12"}}, "node_modules/d3-interpolate": {"version": "3.0.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/d3-interpolate/-/d3-interpolate-3.0.1.tgz", "integrity": "sha1-PEeqWzLFs9+1bvP9Q0IHimMrQA0=", "dependencies": {"d3-color": "1 - 3"}, "engines": {"node": ">=12"}}, "node_modules/d3-path": {"version": "3.1.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/d3-path/-/d3-path-3.1.0.tgz", "integrity": "sha1-It+TkDL7WnGuixgA1h3beFHEJSY=", "engines": {"node": ">=12"}}, "node_modules/d3-scale": {"version": "4.0.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/d3-scale/-/d3-scale-4.0.2.tgz", "integrity": "sha1-grOOjo/3CAdk+Nzsd71L45Nok5Y=", "dependencies": {"d3-array": "2.10.0 - 3", "d3-format": "1 - 3", "d3-interpolate": "1.2.0 - 3", "d3-time": "2.1.1 - 3", "d3-time-format": "2 - 4"}, "engines": {"node": ">=12"}}, "node_modules/d3-shape": {"version": "3.2.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/d3-shape/-/d3-shape-3.2.0.tgz", "integrity": "sha1-oag5y9m6RfKGdMadf4Vbz5HfxqU=", "dependencies": {"d3-path": "^3.1.0"}, "engines": {"node": ">=12"}}, "node_modules/d3-time": {"version": "3.1.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/d3-time/-/d3-time-3.1.0.tgz", "integrity": "sha1-kxDbVumS48AXXh7zheVF5Iqbtcc=", "dependencies": {"d3-array": "2 - 3"}, "engines": {"node": ">=12"}}, "node_modules/d3-time-format": {"version": "4.1.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/d3-time-format/-/d3-time-format-4.1.0.tgz", "integrity": "sha1-erUlelBB0R7LT+cKXH0WoZW7QIo=", "dependencies": {"d3-time": "1 - 3"}, "engines": {"node": ">=12"}}, "node_modules/d3-timer": {"version": "3.0.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/d3-timer/-/d3-timer-3.0.1.tgz", "integrity": "sha1-YoTSonCChbGrt+IB7aQ4CvNeY7A=", "engines": {"node": ">=12"}}, "node_modules/data-uri-to-buffer": {"version": "4.0.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/data-uri-to-buffer/-/data-uri-to-buffer-4.0.1.tgz", "integrity": "sha1-2P6ysogeak9YwuCKz9Dig04mIi4=", "dev": true, "engines": {"node": ">= 12"}}, "node_modules/dataloader": {"version": "2.2.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/dataloader/-/dataloader-2.2.3.tgz", "integrity": "sha1-QtELSRNRX1s3xqzty0lg1q4bFRc=", "dev": true}, "node_modules/dayjs": {"version": "1.11.13", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/dayjs/-/dayjs-1.11.13.tgz", "integrity": "sha1-kkMLATkFXD67YBUKoT6GCktaNmw="}, "node_modules/debounce": {"version": "1.2.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/debounce/-/debounce-1.2.1.tgz", "integrity": "sha1-OIgdj0FmpcWEgCDBGCe4NLyz4KU=", "dev": true}, "node_modules/debug": {"version": "4.4.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/debug/-/debug-4.4.1.tgz", "integrity": "sha1-5ai8bLxMbNPmQwiwaTo9T6VQGJs=", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/decimal.js-light": {"version": "2.5.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/decimal.js-light/-/decimal.js-light-2.5.1.tgz", "integrity": "sha1-E0/TJQjxniCPT7L42sDSYmqGeTQ="}, "node_modules/deep-is": {"version": "0.1.4", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/deep-is/-/deep-is-0.1.4.tgz", "integrity": "sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=", "dev": true}, "node_modules/defaults": {"version": "1.0.4", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/defaults/-/defaults-1.0.4.tgz", "integrity": "sha1-sLAgYsHiqmL/XZUo8PmLqpCXjXo=", "dev": true, "dependencies": {"clone": "^1.0.2"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/dependency-graph": {"version": "0.11.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/dependency-graph/-/dependency-graph-0.11.0.tgz", "integrity": "sha1-rAzn7WilTaIhZahel6AdU/XrLic=", "dev": true, "engines": {"node": ">= 0.6.0"}}, "node_modules/detect-indent": {"version": "6.1.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/detect-indent/-/detect-indent-6.1.0.tgz", "integrity": "sha1-WSSF67v2s7GrK+F1yDk9BMoNV+Y=", "dev": true, "engines": {"node": ">=8"}}, "node_modules/dir-glob": {"version": "3.0.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/dir-glob/-/dir-glob-3.0.1.tgz", "integrity": "sha1-Vtv3PZkqSpO6FYT0U0Bj/S5BcX8=", "dev": true, "dependencies": {"path-type": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/dom-helpers": {"version": "5.2.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/dom-helpers/-/dom-helpers-5.2.1.tgz", "integrity": "sha1-2UAFNrK/giWtmP4FLgKUUaxA6QI=", "dependencies": {"@babel/runtime": "^7.8.7", "csstype": "^3.0.2"}}, "node_modules/dot-case": {"version": "3.0.4", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/dot-case/-/dot-case-3.0.4.tgz", "integrity": "sha1-mytnDQCkMWZ6inW6Kc0bmICc51E=", "dev": true, "dependencies": {"no-case": "^3.0.4", "tslib": "^2.0.3"}}, "node_modules/dotenv": {"version": "16.6.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/dotenv/-/dotenv-16.6.1.tgz", "integrity": "sha1-dz8OaVJ6gxXHKF1e5zxEWdIKgCA=", "dev": true, "engines": {"node": ">=12"}, "funding": {"url": "https://dotenvx.com"}}, "node_modules/dset": {"version": "3.1.4", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/dset/-/dset-3.1.4.tgz", "integrity": "sha1-+Or18CPwaKA20IzQfcn/t9AGUkg=", "dev": true, "engines": {"node": ">=4"}}, "node_modules/electron-to-chromium": {"version": "1.5.179", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/electron-to-chromium/-/electron-to-chromium-1.5.179.tgz", "integrity": "sha1-RT1T82ABSiYE1AzNQcTqCm4xuZo=", "dev": true}, "node_modules/emoji-regex": {"version": "8.0.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/emoji-regex/-/emoji-regex-8.0.0.tgz", "integrity": "sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=", "dev": true}, "node_modules/error-ex": {"version": "1.3.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/error-ex/-/error-ex-1.3.2.tgz", "integrity": "sha1-tKxAZIEH/c3PriQvQovqihTU8b8=", "dependencies": {"is-arrayish": "^0.2.1"}}, "node_modules/esbuild": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/esbuild/-/esbuild-0.25.5.tgz", "integrity": "sha1-cQdQVJk/3652xmWG+bnB+Nft1DA=", "dev": true, "hasInstallScript": true, "bin": {"esbuild": "bin/esbuild"}, "engines": {"node": ">=18"}, "optionalDependencies": {"@esbuild/aix-ppc64": "0.25.5", "@esbuild/android-arm": "0.25.5", "@esbuild/android-arm64": "0.25.5", "@esbuild/android-x64": "0.25.5", "@esbuild/darwin-arm64": "0.25.5", "@esbuild/darwin-x64": "0.25.5", "@esbuild/freebsd-arm64": "0.25.5", "@esbuild/freebsd-x64": "0.25.5", "@esbuild/linux-arm": "0.25.5", "@esbuild/linux-arm64": "0.25.5", "@esbuild/linux-ia32": "0.25.5", "@esbuild/linux-loong64": "0.25.5", "@esbuild/linux-mips64el": "0.25.5", "@esbuild/linux-ppc64": "0.25.5", "@esbuild/linux-riscv64": "0.25.5", "@esbuild/linux-s390x": "0.25.5", "@esbuild/linux-x64": "0.25.5", "@esbuild/netbsd-arm64": "0.25.5", "@esbuild/netbsd-x64": "0.25.5", "@esbuild/openbsd-arm64": "0.25.5", "@esbuild/openbsd-x64": "0.25.5", "@esbuild/sunos-x64": "0.25.5", "@esbuild/win32-arm64": "0.25.5", "@esbuild/win32-ia32": "0.25.5", "@esbuild/win32-x64": "0.25.5"}}, "node_modules/escalade": {"version": "3.2.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/escalade/-/escalade-3.2.0.tgz", "integrity": "sha1-ARo/aYVroYnf+n3I/M6Z0qh5A+U=", "dev": true, "engines": {"node": ">=6"}}, "node_modules/escape-string-regexp": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz", "integrity": "sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ=", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/eslint": {"version": "9.30.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/eslint/-/eslint-9.30.1.tgz", "integrity": "sha1-1BB7OZZEEqzZtcB0Txxt9RT6EhE=", "dev": true, "dependencies": {"@eslint-community/eslint-utils": "^4.2.0", "@eslint-community/regexpp": "^4.12.1", "@eslint/config-array": "^0.21.0", "@eslint/config-helpers": "^0.3.0", "@eslint/core": "^0.14.0", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "9.30.1", "@eslint/plugin-kit": "^0.3.1", "@humanfs/node": "^0.16.6", "@humanwhocodes/module-importer": "^1.0.1", "@humanwhocodes/retry": "^0.4.2", "@types/estree": "^1.0.6", "@types/json-schema": "^7.0.15", "ajv": "^6.12.4", "chalk": "^4.0.0", "cross-spawn": "^7.0.6", "debug": "^4.3.2", "escape-string-regexp": "^4.0.0", "eslint-scope": "^8.4.0", "eslint-visitor-keys": "^4.2.1", "espree": "^10.4.0", "esquery": "^1.5.0", "esutils": "^2.0.2", "fast-deep-equal": "^3.1.3", "file-entry-cache": "^8.0.0", "find-up": "^5.0.0", "glob-parent": "^6.0.2", "ignore": "^5.2.0", "imurmurhash": "^0.1.4", "is-glob": "^4.0.0", "json-stable-stringify-without-jsonify": "^1.0.1", "lodash.merge": "^4.6.2", "minimatch": "^3.1.2", "natural-compare": "^1.4.0", "optionator": "^0.9.3"}, "bin": {"eslint": "bin/eslint.js"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://eslint.org/donate"}, "peerDependencies": {"jiti": "*"}, "peerDependenciesMeta": {"jiti": {"optional": true}}}, "node_modules/eslint-config-prettier": {"version": "10.1.5", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/eslint-config-prettier/-/eslint-config-prettier-10.1.5.tgz", "integrity": "sha1-AMGNciUEO2+85qZlaXN3mY1FN4I=", "dev": true, "bin": {"eslint-config-prettier": "bin/cli.js"}, "funding": {"url": "https://opencollective.com/eslint-config-prettier"}, "peerDependencies": {"eslint": ">=7.0.0"}}, "node_modules/eslint-plugin-prettier": {"version": "5.5.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/eslint-plugin-prettier/-/eslint-plugin-prettier-5.5.1.tgz", "integrity": "sha1-Rwgglk3prts36c5iwyZtLSbQjRU=", "dev": true, "dependencies": {"prettier-linter-helpers": "^1.0.0", "synckit": "^0.11.7"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint-plugin-prettier"}, "peerDependencies": {"@types/eslint": ">=8.0.0", "eslint": ">=8.0.0", "eslint-config-prettier": ">= 7.0.0 <10.0.0 || >=10.1.0", "prettier": ">=3.0.0"}, "peerDependenciesMeta": {"@types/eslint": {"optional": true}, "eslint-config-prettier": {"optional": true}}}, "node_modules/eslint-plugin-react-hooks": {"version": "5.2.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/eslint-plugin-react-hooks/-/eslint-plugin-react-hooks-5.2.0.tgz", "integrity": "sha1-G+AICQHmrDHOeXG+7T0+wKQj2eM=", "dev": true, "engines": {"node": ">=10"}, "peerDependencies": {"eslint": "^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0 || ^9.0.0"}}, "node_modules/eslint-plugin-react-refresh": {"version": "0.4.20", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/eslint-plugin-react-refresh/-/eslint-plugin-react-refresh-0.4.20.tgz", "integrity": "sha1-O7+1yGN+KNGc40Q2hkReUC7NGLo=", "dev": true, "peerDependencies": {"eslint": ">=8.40"}}, "node_modules/eslint-scope": {"version": "8.4.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/eslint-scope/-/eslint-scope-8.4.0.tgz", "integrity": "sha1-iOZGogf61hQ2/6OetQUUcgBlXII=", "dev": true, "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint-visitor-keys": {"version": "4.2.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/eslint-visitor-keys/-/eslint-visitor-keys-4.2.1.tgz", "integrity": "sha1-TP6mD+fdCtjoFuHtAmwdUlG1EsE=", "dev": true, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/espree": {"version": "10.4.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/espree/-/espree-10.4.0.tgz", "integrity": "sha1-1U9JSdRikAWh+haNk3w/8ffiqDc=", "dev": true, "dependencies": {"acorn": "^8.15.0", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^4.2.1"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/esquery": {"version": "1.6.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/esquery/-/esquery-1.6.0.tgz", "integrity": "sha1-kUGSNPgE2FKoLc7sPhbNwiz52uc=", "dev": true, "dependencies": {"estraverse": "^5.1.0"}, "engines": {"node": ">=0.10"}}, "node_modules/esrecurse": {"version": "4.3.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/esrecurse/-/esrecurse-4.3.0.tgz", "integrity": "sha1-eteWTWeauyi+5yzsY3WLHF0smSE=", "dev": true, "dependencies": {"estraverse": "^5.2.0"}, "engines": {"node": ">=4.0"}}, "node_modules/estraverse": {"version": "5.3.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/estraverse/-/estraverse-5.3.0.tgz", "integrity": "sha1-LupSkHAvJquP5TcDcP+GyWXSESM=", "dev": true, "engines": {"node": ">=4.0"}}, "node_modules/esutils": {"version": "2.0.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/esutils/-/esutils-2.0.3.tgz", "integrity": "sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/eventemitter3": {"version": "4.0.7", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/eventemitter3/-/eventemitter3-4.0.7.tgz", "integrity": "sha1-Lem2j2Uo1WRO9cWVJqG0oHMGFp8="}, "node_modules/external-editor": {"version": "3.1.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/external-editor/-/external-editor-3.1.0.tgz", "integrity": "sha1-ywP3QL764D6k0oPK7SdBqD8zVJU=", "dev": true, "dependencies": {"chardet": "^0.7.0", "iconv-lite": "^0.4.24", "tmp": "^0.0.33"}, "engines": {"node": ">=4"}}, "node_modules/fast-decode-uri-component": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/fast-decode-uri-component/-/fast-decode-uri-component-1.0.1.tgz", "integrity": "sha1-Rvi2wisw/3qBNX1PWav66TggJUM=", "dev": true}, "node_modules/fast-deep-equal": {"version": "3.1.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "integrity": "sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=", "dev": true}, "node_modules/fast-diff": {"version": "1.3.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/fast-diff/-/fast-diff-1.3.0.tgz", "integrity": "sha1-7OQH+lUKZNY4U2zXJ+EpxhYW4PA=", "dev": true}, "node_modules/fast-equals": {"version": "5.2.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/fast-equals/-/fast-equals-5.2.2.tgz", "integrity": "sha1-iF17+wefrAzg6EUDdLzinpt0JIQ=", "engines": {"node": ">=6.0.0"}}, "node_modules/fast-glob": {"version": "3.3.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/fast-glob/-/fast-glob-3.3.3.tgz", "integrity": "sha1-0G1YXOjbqQoWsFBcVDw8z7OuuBg=", "dev": true, "dependencies": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.8"}, "engines": {"node": ">=8.6.0"}}, "node_modules/fast-glob/node_modules/glob-parent": {"version": "5.1.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/glob-parent/-/glob-parent-5.1.2.tgz", "integrity": "sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=", "dev": true, "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/fast-json-stable-stringify": {"version": "2.1.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "integrity": "sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=", "dev": true}, "node_modules/fast-levenshtein": {"version": "2.0.6", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz", "integrity": "sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=", "dev": true}, "node_modules/fast-querystring": {"version": "1.1.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/fast-querystring/-/fast-querystring-1.1.2.tgz", "integrity": "sha1-ptJJN7T8b3kbTuMdy29Trq+4n1M=", "dev": true, "dependencies": {"fast-decode-uri-component": "^1.0.1"}}, "node_modules/fast-url-parser": {"version": "1.1.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/fast-url-parser/-/fast-url-parser-1.1.3.tgz", "integrity": "sha1-9K8+qfNNiicc9YrSs3WfQx8LMY0=", "dev": true, "dependencies": {"punycode": "^1.3.2"}}, "node_modules/fastq": {"version": "1.19.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/fastq/-/fastq-1.19.1.tgz", "integrity": "sha1-1Q6rqAPIhGqIPBZJKCHrzSzaVfU=", "dev": true, "dependencies": {"reusify": "^1.0.4"}}, "node_modules/fb-watchman": {"version": "2.0.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/fb-watchman/-/fb-watchman-2.0.2.tgz", "integrity": "sha1-6VJO5rXHfp5QAa8PhfOtu4YjJVw=", "dev": true, "dependencies": {"bser": "2.1.1"}}, "node_modules/fbjs": {"version": "3.0.5", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/fbjs/-/fbjs-3.0.5.tgz", "integrity": "sha1-qg7bfVyqY0ABF5C9kknb74qBEo0=", "dev": true, "dependencies": {"cross-fetch": "^3.1.5", "fbjs-css-vars": "^1.0.0", "loose-envify": "^1.0.0", "object-assign": "^4.1.0", "promise": "^7.1.1", "setimmediate": "^1.0.5", "ua-parser-js": "^1.0.35"}}, "node_modules/fbjs-css-vars": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/fbjs-css-vars/-/fbjs-css-vars-1.0.2.tgz", "integrity": "sha1-IWVRE2rgL+JVkyw+yHdfGOLAeLg=", "dev": true}, "node_modules/fetch-blob": {"version": "3.2.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/fetch-blob/-/fetch-blob-3.2.0.tgz", "integrity": "sha1-8JuNS71Frcbwwgt+eH55PjCdzOk=", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/jimmywarting"}, {"type": "paypal", "url": "https://paypal.me/jimmywarting"}], "dependencies": {"node-domexception": "^1.0.0", "web-streams-polyfill": "^3.0.3"}, "engines": {"node": "^12.20 || >= 14.13"}}, "node_modules/figures": {"version": "3.2.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/figures/-/figures-3.2.0.tgz", "integrity": "sha1-YlwYvSk8YE3EqN2y/r8MiDQXRq8=", "dev": true, "dependencies": {"escape-string-regexp": "^1.0.5"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/figures/node_modules/escape-string-regexp": {"version": "1.0.5", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "integrity": "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=", "dev": true, "engines": {"node": ">=0.8.0"}}, "node_modules/file-entry-cache": {"version": "8.0.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/file-entry-cache/-/file-entry-cache-8.0.0.tgz", "integrity": "sha1-d4e93PETG/+5JjbGlFe7wO3W2B8=", "dev": true, "dependencies": {"flat-cache": "^4.0.0"}, "engines": {"node": ">=16.0.0"}}, "node_modules/file-saver": {"version": "2.0.5", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/file-saver/-/file-saver-2.0.5.tgz", "integrity": "sha1-1hz+LOBZ9BTYmendbUEH7iVnDDg="}, "node_modules/fill-range": {"version": "7.1.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/fill-range/-/fill-range-7.1.1.tgz", "integrity": "sha1-RCZdPKwH4+p9wkdRY4BkN1SgUpI=", "dev": true, "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/find-root": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/find-root/-/find-root-1.1.0.tgz", "integrity": "sha1-q8/Iunb3CMQql7PWhbfpRQv7nOQ="}, "node_modules/find-up": {"version": "5.0.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/find-up/-/find-up-5.0.0.tgz", "integrity": "sha1-TJKBnstwg1YeT0okCoa+UZj1Nvw=", "dev": true, "dependencies": {"locate-path": "^6.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/flat-cache": {"version": "4.0.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/flat-cache/-/flat-cache-4.0.1.tgz", "integrity": "sha1-Ds45/LFO4BL0sEEL0z3ZwfAREnw=", "dev": true, "dependencies": {"flatted": "^3.2.9", "keyv": "^4.5.4"}, "engines": {"node": ">=16"}}, "node_modules/flatted": {"version": "3.3.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/flatted/-/flatted-3.3.3.tgz", "integrity": "sha1-Z8j62VRUp8er6/dLt47nSkQCM1g=", "dev": true}, "node_modules/formdata-polyfill": {"version": "4.0.10", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/formdata-polyfill/-/formdata-polyfill-4.0.10.tgz", "integrity": "sha1-JIB8McnUAuACqz2McgFEzriEhCM=", "dev": true, "dependencies": {"fetch-blob": "^3.1.2"}, "engines": {"node": ">=12.20.0"}}, "node_modules/fsevents": {"version": "2.3.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/fsevents/-/fsevents-2.3.3.tgz", "integrity": "sha1-ysZAd4XQNnWipeGlMFxpezR9kNY=", "dev": true, "optional": true, "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}}, "node_modules/function-bind": {"version": "1.1.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/function-bind/-/function-bind-1.1.2.tgz", "integrity": "sha1-LALYZNl/PqbIgwxGTL0Rq26rehw=", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/gensync": {"version": "1.0.0-beta.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/gensync/-/gensync-1.0.0-beta.2.tgz", "integrity": "sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=", "dev": true, "engines": {"node": ">=6.9.0"}}, "node_modules/get-caller-file": {"version": "2.0.5", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/get-caller-file/-/get-caller-file-2.0.5.tgz", "integrity": "sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=", "dev": true, "engines": {"node": "6.* || 8.* || >= 10.*"}}, "node_modules/glob-parent": {"version": "6.0.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/glob-parent/-/glob-parent-6.0.2.tgz", "integrity": "sha1-bSN9mQg5UMeSkPJMdkKj3poo+eM=", "dev": true, "dependencies": {"is-glob": "^4.0.3"}, "engines": {"node": ">=10.13.0"}}, "node_modules/globals": {"version": "15.15.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/globals/-/globals-15.15.0.tgz", "integrity": "sha1-fEdhKZ1BwysHVxWkzh7eeJf/cqg=", "dev": true, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/globby": {"version": "11.1.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/globby/-/globby-11.1.0.tgz", "integrity": "sha1-vUvpi7BC+D15b344EZkfvoKg00s=", "dev": true, "dependencies": {"array-union": "^2.1.0", "dir-glob": "^3.0.1", "fast-glob": "^3.2.9", "ignore": "^5.2.0", "merge2": "^1.4.1", "slash": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/graphemer": {"version": "1.4.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/graphemer/-/graphemer-1.4.0.tgz", "integrity": "sha1-+y8dVeDjoYSa7/yQxPoN1ToOZsY=", "dev": true}, "node_modules/graphql": {"version": "16.11.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/graphql/-/graphql-16.11.0.tgz", "integrity": "sha1-ltF/ZjcGeAJ/31my1MILTvqopjM=", "engines": {"node": "^12.22.0 || ^14.16.0 || ^16.0.0 || >=17.0.0"}}, "node_modules/graphql-config": {"version": "5.1.5", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/graphql-config/-/graphql-config-5.1.5.tgz", "integrity": "sha1-NOC/uojnS27v2IlxapN4CG9ZX38=", "dev": true, "dependencies": {"@graphql-tools/graphql-file-loader": "^8.0.0", "@graphql-tools/json-file-loader": "^8.0.0", "@graphql-tools/load": "^8.1.0", "@graphql-tools/merge": "^9.0.0", "@graphql-tools/url-loader": "^8.0.0", "@graphql-tools/utils": "^10.0.0", "cosmiconfig": "^8.1.0", "jiti": "^2.0.0", "minimatch": "^9.0.5", "string-env-interpolation": "^1.0.1", "tslib": "^2.4.0"}, "engines": {"node": ">= 16.0.0"}, "peerDependencies": {"cosmiconfig-toml-loader": "^1.0.0", "graphql": "^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0"}, "peerDependenciesMeta": {"cosmiconfig-toml-loader": {"optional": true}}}, "node_modules/graphql-config/node_modules/brace-expansion": {"version": "2.0.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/brace-expansion/-/brace-expansion-2.0.2.tgz", "integrity": "sha1-VPxTI3phPYVMe9N0Y6rRffhyFOc=", "dev": true, "dependencies": {"balanced-match": "^1.0.0"}}, "node_modules/graphql-config/node_modules/jiti": {"version": "2.4.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/jiti/-/jiti-2.4.2.tgz", "integrity": "sha1-0Zt3Muu2EWsG4gONp0pVNm+u9WA=", "dev": true, "bin": {"jiti": "lib/jiti-cli.mjs"}}, "node_modules/graphql-config/node_modules/minimatch": {"version": "9.0.5", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/minimatch/-/minimatch-9.0.5.tgz", "integrity": "sha1-10+d1rV9g9jpjPuCEzsDl4vJKeU=", "dev": true, "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/graphql-request": {"version": "6.1.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/graphql-request/-/graphql-request-6.1.0.tgz", "integrity": "sha1-9OshB5Z688elkH6zExxnHqyJvk8=", "dev": true, "dependencies": {"@graphql-typed-document-node/core": "^3.2.0", "cross-fetch": "^3.1.5"}, "peerDependencies": {"graphql": "14 - 16"}}, "node_modules/graphql-tag": {"version": "2.12.6", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/graphql-tag/-/graphql-tag-2.12.6.tgz", "integrity": "sha1-1EGlacHSU37xDKPRYztIclMptfE=", "dependencies": {"tslib": "^2.1.0"}, "engines": {"node": ">=10"}, "peerDependencies": {"graphql": "^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/graphql-ws": {"version": "6.0.5", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/graphql-ws/-/graphql-ws-6.0.5.tgz", "integrity": "sha1-JK3PREYC34NHe54HzUtX9BGtoCQ=", "devOptional": true, "engines": {"node": ">=20"}, "peerDependencies": {"@fastify/websocket": "^10 || ^11", "crossws": "~0.3", "graphql": "^15.10.1 || ^16", "uWebSockets.js": "^20", "ws": "^8"}, "peerDependenciesMeta": {"@fastify/websocket": {"optional": true}, "crossws": {"optional": true}, "uWebSockets.js": {"optional": true}, "ws": {"optional": true}}}, "node_modules/has-flag": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/has-flag/-/has-flag-4.0.0.tgz", "integrity": "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=", "dev": true, "engines": {"node": ">=8"}}, "node_modules/hasown": {"version": "2.0.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/hasown/-/hasown-2.0.2.tgz", "integrity": "sha1-AD6vkb563DcuhOxZ3DclLO24AAM=", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/header-case": {"version": "2.0.4", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/header-case/-/header-case-2.0.4.tgz", "integrity": "sha1-WkLmO1UXc0nPQFvrjXdayruSwGM=", "dev": true, "dependencies": {"capital-case": "^1.0.4", "tslib": "^2.0.3"}}, "node_modules/hoist-non-react-statics": {"version": "3.3.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/hoist-non-react-statics/-/hoist-non-react-statics-3.3.2.tgz", "integrity": "sha1-7OCsr3HWLClpwuxZ/v9CpLGoW0U=", "dependencies": {"react-is": "^16.7.0"}}, "node_modules/hoist-non-react-statics/node_modules/react-is": {"version": "16.13.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/react-is/-/react-is-16.13.1.tgz", "integrity": "sha1-eJcppNw23imZ3BVt1sHZwYzqVqQ="}, "node_modules/html-parse-stringify": {"version": "3.0.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/html-parse-stringify/-/html-parse-stringify-3.0.1.tgz", "integrity": "sha1-38EBc0fOn3fIFBpQfyMwQMWcVdI=", "dependencies": {"void-elements": "3.1.0"}}, "node_modules/http-proxy-agent": {"version": "7.0.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/http-proxy-agent/-/http-proxy-agent-7.0.2.tgz", "integrity": "sha1-mosfJGhmwChQlIZYX2K48sGMJw4=", "dev": true, "dependencies": {"agent-base": "^7.1.0", "debug": "^4.3.4"}, "engines": {"node": ">= 14"}}, "node_modules/https-proxy-agent": {"version": "7.0.6", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/https-proxy-agent/-/https-proxy-agent-7.0.6.tgz", "integrity": "sha1-2o3+rH2hMLBcK6S1nJts1mYRprk=", "dev": true, "dependencies": {"agent-base": "^7.1.2", "debug": "4"}, "engines": {"node": ">= 14"}}, "node_modules/i18next": {"version": "24.2.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/i18next/-/i18next-24.2.3.tgz", "integrity": "sha1-OgX3JhXL18ANfjSGZ+KqvvHfdTs=", "funding": [{"type": "individual", "url": "https://locize.com"}, {"type": "individual", "url": "https://locize.com/i18next.html"}, {"type": "individual", "url": "https://www.i18next.com/how-to/faq#i18next-is-awesome.-how-can-i-support-the-project"}], "dependencies": {"@babel/runtime": "^7.26.10"}, "peerDependencies": {"typescript": "^5"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/iconv-lite": {"version": "0.4.24", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/iconv-lite/-/iconv-lite-0.4.24.tgz", "integrity": "sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=", "dev": true, "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/ieee754": {"version": "1.2.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/ieee754/-/ieee754-1.2.1.tgz", "integrity": "sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I=", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}, "node_modules/ignore": {"version": "5.3.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/ignore/-/ignore-5.3.2.tgz", "integrity": "sha1-PNQOcp82Q/2HywTlC/DrcivFlvU=", "dev": true, "engines": {"node": ">= 4"}}, "node_modules/immutable": {"version": "3.7.6", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/immutable/-/immutable-3.7.6.tgz", "integrity": "sha1-E7TTyxK++hVIKib+Gy665kAHHks=", "dev": true, "engines": {"node": ">=0.8.0"}}, "node_modules/import-fresh": {"version": "3.3.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/import-fresh/-/import-fresh-3.3.1.tgz", "integrity": "sha1-nOy1ZQPAraHydB271lRuSxO1fM8=", "dependencies": {"parent-module": "^1.0.0", "resolve-from": "^4.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/import-fresh/node_modules/resolve-from": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/resolve-from/-/resolve-from-4.0.0.tgz", "integrity": "sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=", "engines": {"node": ">=4"}}, "node_modules/import-from": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/import-from/-/import-from-4.0.0.tgz", "integrity": "sha1-JxC41mgX0jLhb0Fm4xkkjT1UkuI=", "dev": true, "engines": {"node": ">=12.2"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/imurmurhash": {"version": "0.1.4", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/imurmurhash/-/imurmurhash-0.1.4.tgz", "integrity": "sha1-khi5srkoojixPcT7a21XbyMUU+o=", "dev": true, "engines": {"node": ">=0.8.19"}}, "node_modules/indent-string": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/indent-string/-/indent-string-4.0.0.tgz", "integrity": "sha1-Yk+PRJfWGbLZdoUx1Y9BIoVNclE=", "dev": true, "engines": {"node": ">=8"}}, "node_modules/inherits": {"version": "2.0.4", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/inherits/-/inherits-2.0.4.tgz", "integrity": "sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=", "dev": true}, "node_modules/inquirer": {"version": "8.2.6", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/inquirer/-/inquirer-8.2.6.tgz", "integrity": "sha1-czt0iIGV2NQApnrDMgEbX65epWI=", "dev": true, "dependencies": {"ansi-escapes": "^4.2.1", "chalk": "^4.1.1", "cli-cursor": "^3.1.0", "cli-width": "^3.0.0", "external-editor": "^3.0.3", "figures": "^3.0.0", "lodash": "^4.17.21", "mute-stream": "0.0.8", "ora": "^5.4.1", "run-async": "^2.4.0", "rxjs": "^7.5.5", "string-width": "^4.1.0", "strip-ansi": "^6.0.0", "through": "^2.3.6", "wrap-ansi": "^6.0.1"}, "engines": {"node": ">=12.0.0"}}, "node_modules/internmap": {"version": "2.0.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/internmap/-/internmap-2.0.3.tgz", "integrity": "sha1-ZoXyN1XkPFJOJR0py8lySOMGEAk=", "engines": {"node": ">=12"}}, "node_modules/invariant": {"version": "2.2.4", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/invariant/-/invariant-2.2.4.tgz", "integrity": "sha1-YQ88ksk1nOHbYW5TgAjSP/NRWOY=", "dev": true, "dependencies": {"loose-envify": "^1.0.0"}}, "node_modules/is-absolute": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/is-absolute/-/is-absolute-1.0.0.tgz", "integrity": "sha1-OV4a6EsR8mrReV5zwXN45IowFXY=", "dev": true, "dependencies": {"is-relative": "^1.0.0", "is-windows": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-arrayish": {"version": "0.2.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/is-arrayish/-/is-arrayish-0.2.1.tgz", "integrity": "sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0="}, "node_modules/is-core-module": {"version": "2.16.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/is-core-module/-/is-core-module-2.16.1.tgz", "integrity": "sha1-KpiAGoSfQ+Kt1kT7trxiKbGaTvQ=", "dependencies": {"hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-extglob": {"version": "2.1.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/is-extglob/-/is-extglob-2.1.1.tgz", "integrity": "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/is-fullwidth-code-point": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=", "dev": true, "engines": {"node": ">=8"}}, "node_modules/is-glob": {"version": "4.0.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/is-glob/-/is-glob-4.0.3.tgz", "integrity": "sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=", "dev": true, "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-interactive": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/is-interactive/-/is-interactive-1.0.0.tgz", "integrity": "sha1-zqbmrlyHCnsKAAQHC3tYfgJSkS4=", "dev": true, "engines": {"node": ">=8"}}, "node_modules/is-lower-case": {"version": "2.0.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/is-lower-case/-/is-lower-case-2.0.2.tgz", "integrity": "sha1-HAiE0wEshBVWJDSDql1SL0c5bSo=", "dev": true, "dependencies": {"tslib": "^2.0.3"}}, "node_modules/is-number": {"version": "7.0.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/is-number/-/is-number-7.0.0.tgz", "integrity": "sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=", "dev": true, "engines": {"node": ">=0.12.0"}}, "node_modules/is-relative": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/is-relative/-/is-relative-1.0.0.tgz", "integrity": "sha1-obtpNc6MXboei5dUubLcwCDiJg0=", "dev": true, "dependencies": {"is-unc-path": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-unc-path": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/is-unc-path/-/is-unc-path-1.0.0.tgz", "integrity": "sha1-1zHoiY7QkKEsNSrS6u1Qla0yLJ0=", "dev": true, "dependencies": {"unc-path-regex": "^0.1.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-unicode-supported": {"version": "0.1.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/is-unicode-supported/-/is-unicode-supported-0.1.0.tgz", "integrity": "sha1-PybHaoCVk7Ur+i7LVxDtJ3m1Iqc=", "dev": true, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-upper-case": {"version": "2.0.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/is-upper-case/-/is-upper-case-2.0.2.tgz", "integrity": "sha1-8RBc7R/k3pBqXzlVPn04A/2ARkk=", "dev": true, "dependencies": {"tslib": "^2.0.3"}}, "node_modules/is-windows": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/is-windows/-/is-windows-1.0.2.tgz", "integrity": "sha1-0YUOuXkezRjmGCzhKjDzlmNLsZ0=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/isexe": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/isexe/-/isexe-2.0.0.tgz", "integrity": "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=", "dev": true}, "node_modules/isomorphic-ws": {"version": "5.0.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/isomorphic-ws/-/isomorphic-ws-5.0.0.tgz", "integrity": "sha1-5VKRSJEuy5tFG0btRNU9rhzgS78=", "dev": true, "peerDependencies": {"ws": "*"}}, "node_modules/jiti": {"version": "1.21.7", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/jiti/-/jiti-1.21.7.tgz", "integrity": "sha1-ndgQQ0JKPShFixk9ll8NGKIwC6k=", "dev": true, "bin": {"jiti": "bin/jiti.js"}}, "node_modules/jose": {"version": "5.10.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/jose/-/jose-5.10.0.tgz", "integrity": "sha1-w3NGoJnWRnxAE1GpoMIWHg9SxL4=", "dev": true, "funding": {"url": "https://github.com/sponsors/panva"}}, "node_modules/js-tokens": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/js-tokens/-/js-tokens-4.0.0.tgz", "integrity": "sha1-GSA/tZmR35jjoocFDUZHzerzJJk="}, "node_modules/js-yaml": {"version": "4.1.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/js-yaml/-/js-yaml-4.1.0.tgz", "integrity": "sha1-wftl+PUBeQHN0slRhkuhhFihBgI=", "dev": true, "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/jsesc": {"version": "3.1.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/jsesc/-/jsesc-3.1.0.tgz", "integrity": "sha1-dNM1ojT2ftGZB/2t+sfM+dQJgl0=", "bin": {"jsesc": "bin/jsesc"}, "engines": {"node": ">=6"}}, "node_modules/json-buffer": {"version": "3.0.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/json-buffer/-/json-buffer-3.0.1.tgz", "integrity": "sha1-kziAKjDTtmBfvgYT4JQAjKjAWhM=", "dev": true}, "node_modules/json-parse-even-better-errors": {"version": "2.3.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz", "integrity": "sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0="}, "node_modules/json-schema-traverse": {"version": "0.4.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz", "integrity": "sha1-afaofZUTq4u4/mO9sJecRI5oRmA=", "dev": true}, "node_modules/json-stable-stringify-without-jsonify": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz", "integrity": "sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=", "dev": true}, "node_modules/json-to-pretty-yaml": {"version": "1.2.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/json-to-pretty-yaml/-/json-to-pretty-yaml-1.2.2.tgz", "integrity": "sha1-9M0L0KXo/h3yWq9boRiwmf2ZLVs=", "dev": true, "dependencies": {"remedial": "^1.0.7", "remove-trailing-spaces": "^1.0.6"}, "engines": {"node": ">= 0.2.0"}}, "node_modules/json5": {"version": "2.2.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/json5/-/json5-2.2.3.tgz", "integrity": "sha1-eM1vGhm9wStz21rQxh79ZsHikoM=", "dev": true, "bin": {"json5": "lib/cli.js"}, "engines": {"node": ">=6"}}, "node_modules/keyv": {"version": "4.5.4", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/keyv/-/keyv-4.5.4.tgz", "integrity": "sha1-qHmpnilFL5QkOfKkBeOvizHU3pM=", "dev": true, "dependencies": {"json-buffer": "3.0.1"}}, "node_modules/levn": {"version": "0.4.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/levn/-/levn-0.4.1.tgz", "integrity": "sha1-rkViwAdHO5MqYgDUAyaN0v/8at4=", "dev": true, "dependencies": {"prelude-ls": "^1.2.1", "type-check": "~0.4.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/lines-and-columns": {"version": "1.2.4", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/lines-and-columns/-/lines-and-columns-1.2.4.tgz", "integrity": "sha1-7KKE910pZQeTCdwK2SVauy68FjI="}, "node_modules/listr2": {"version": "4.0.5", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/listr2/-/listr2-4.0.5.tgz", "integrity": "sha1-ncxQIhWD6LTHHEP5x9/Q71RrddU=", "dev": true, "dependencies": {"cli-truncate": "^2.1.0", "colorette": "^2.0.16", "log-update": "^4.0.0", "p-map": "^4.0.0", "rfdc": "^1.3.0", "rxjs": "^7.5.5", "through": "^2.3.8", "wrap-ansi": "^7.0.0"}, "engines": {"node": ">=12"}, "peerDependencies": {"enquirer": ">= 2.3.0 < 3"}, "peerDependenciesMeta": {"enquirer": {"optional": true}}}, "node_modules/listr2/node_modules/wrap-ansi": {"version": "7.0.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "integrity": "sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=", "dev": true, "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/locate-path": {"version": "6.0.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/locate-path/-/locate-path-6.0.0.tgz", "integrity": "sha1-VTIeswn+u8WcSAHZMackUqaB0oY=", "dev": true, "dependencies": {"p-locate": "^5.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/lodash": {"version": "4.17.21", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/lodash/-/lodash-4.17.21.tgz", "integrity": "sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw="}, "node_modules/lodash.debounce": {"version": "4.0.8", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/lodash.debounce/-/lodash.debounce-4.0.8.tgz", "integrity": "sha1-gteb/zCmfEAF/9XiUVMArZyk168="}, "node_modules/lodash.merge": {"version": "4.6.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/lodash.merge/-/lodash.merge-4.6.2.tgz", "integrity": "sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo=", "dev": true}, "node_modules/lodash.sortby": {"version": "4.7.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/lodash.sortby/-/lodash.sortby-4.7.0.tgz", "integrity": "sha1-7dFMgk4sycHgsKG0K7UhBRakJDg=", "dev": true}, "node_modules/log-symbols": {"version": "4.1.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/log-symbols/-/log-symbols-4.1.0.tgz", "integrity": "sha1-P727lbRoOsn8eFER55LlWNSr1QM=", "dev": true, "dependencies": {"chalk": "^4.1.0", "is-unicode-supported": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/log-update": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/log-update/-/log-update-4.0.0.tgz", "integrity": "sha1-WJ7NNSRx8qHAxXAodUOmTf0g4KE=", "dev": true, "dependencies": {"ansi-escapes": "^4.3.0", "cli-cursor": "^3.1.0", "slice-ansi": "^4.0.0", "wrap-ansi": "^6.2.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/log-update/node_modules/slice-ansi": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/slice-ansi/-/slice-ansi-4.0.0.tgz", "integrity": "sha1-UA6N0P1VsFgVCGJVsxla3ypF/ms=", "dev": true, "dependencies": {"ansi-styles": "^4.0.0", "astral-regex": "^2.0.0", "is-fullwidth-code-point": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/slice-ansi?sponsor=1"}}, "node_modules/loose-envify": {"version": "1.4.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/loose-envify/-/loose-envify-1.4.0.tgz", "integrity": "sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8=", "dependencies": {"js-tokens": "^3.0.0 || ^4.0.0"}, "bin": {"loose-envify": "cli.js"}}, "node_modules/lower-case": {"version": "2.0.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/lower-case/-/lower-case-2.0.2.tgz", "integrity": "sha1-b6I3xj29xKgsoP2ILkci3F5jTig=", "dev": true, "dependencies": {"tslib": "^2.0.3"}}, "node_modules/lower-case-first": {"version": "2.0.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/lower-case-first/-/lower-case-first-2.0.2.tgz", "integrity": "sha1-ZMIySiJQv3w3xZAedqW1MJMBFgs=", "dev": true, "dependencies": {"tslib": "^2.0.3"}}, "node_modules/lru-cache": {"version": "5.1.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/lru-cache/-/lru-cache-5.1.1.tgz", "integrity": "sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=", "dev": true, "dependencies": {"yallist": "^3.0.2"}}, "node_modules/map-cache": {"version": "0.2.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/map-cache/-/map-cache-0.2.2.tgz", "integrity": "sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/merge2": {"version": "1.4.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/merge2/-/merge2-1.4.1.tgz", "integrity": "sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=", "dev": true, "engines": {"node": ">= 8"}}, "node_modules/meros": {"version": "1.3.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/meros/-/meros-1.3.1.tgz", "integrity": "sha1-IaCQ1ZwCr7HkGOQGMbeKo3ohrGk=", "dev": true, "engines": {"node": ">=13"}, "peerDependencies": {"@types/node": ">=13"}, "peerDependenciesMeta": {"@types/node": {"optional": true}}}, "node_modules/micromatch": {"version": "4.0.8", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/micromatch/-/micromatch-4.0.8.tgz", "integrity": "sha1-1m+hjzpHB2eJMgubGvMr2G2fogI=", "dev": true, "dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "node_modules/mimic-fn": {"version": "2.1.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/mimic-fn/-/mimic-fn-2.1.0.tgz", "integrity": "sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=", "dev": true, "engines": {"node": ">=6"}}, "node_modules/minimatch": {"version": "3.1.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=", "dev": true, "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/ms": {"version": "2.1.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/ms/-/ms-2.1.3.tgz", "integrity": "sha1-V0yBOM4dK1hh8LRFedut1gxmFbI="}, "node_modules/mute-stream": {"version": "0.0.8", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/mute-stream/-/mute-stream-0.0.8.tgz", "integrity": "sha1-FjDEKyJR/4HiooPelqVJfqkuXg0=", "dev": true}, "node_modules/nanoid": {"version": "3.3.11", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/nanoid/-/nanoid-3.3.11.tgz", "integrity": "sha1-T08RLO++MDIC8hmYOBKJNiZtGFs=", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "node_modules/natural-compare": {"version": "1.4.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/natural-compare/-/natural-compare-1.4.0.tgz", "integrity": "sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=", "dev": true}, "node_modules/no-case": {"version": "3.0.4", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/no-case/-/no-case-3.0.4.tgz", "integrity": "sha1-02H9XJgA9VhVGoNp/A3NRmK2Ek0=", "dev": true, "dependencies": {"lower-case": "^2.0.2", "tslib": "^2.0.3"}}, "node_modules/node-domexception": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/node-domexception/-/node-domexception-1.0.0.tgz", "integrity": "sha1-aIjbRqH3HAt2s/dVUBa2P+ZHZuU=", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/jimmywarting"}, {"type": "github", "url": "https://paypal.me/jimmywarting"}], "engines": {"node": ">=10.5.0"}}, "node_modules/node-fetch": {"version": "2.7.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/node-fetch/-/node-fetch-2.7.0.tgz", "integrity": "sha1-0PD6bj4twdJ+/NitmdVQvalNGH0=", "dev": true, "dependencies": {"whatwg-url": "^5.0.0"}, "engines": {"node": "4.x || >=6.0.0"}, "peerDependencies": {"encoding": "^0.1.0"}, "peerDependenciesMeta": {"encoding": {"optional": true}}}, "node_modules/node-int64": {"version": "0.4.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/node-int64/-/node-int64-0.4.0.tgz", "integrity": "sha1-h6kGXNs1XTGC2PlM4RGIuCXGijs=", "dev": true}, "node_modules/node-releases": {"version": "2.0.19", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/node-releases/-/node-releases-2.0.19.tgz", "integrity": "sha1-nkRaUpUJUexNF32EOvNwtBHK8xQ=", "dev": true}, "node_modules/normalize-path": {"version": "2.1.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/normalize-path/-/normalize-path-2.1.1.tgz", "integrity": "sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=", "dev": true, "dependencies": {"remove-trailing-separator": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/nullthrows": {"version": "1.1.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/nullthrows/-/nullthrows-1.1.1.tgz", "integrity": "sha1-eBgliEOFaulx6uQgitfX6xmkMbE=", "dev": true}, "node_modules/object-assign": {"version": "4.1.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=", "engines": {"node": ">=0.10.0"}}, "node_modules/onetime": {"version": "5.1.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/onetime/-/onetime-5.1.2.tgz", "integrity": "sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=", "dev": true, "dependencies": {"mimic-fn": "^2.1.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/optimism": {"version": "0.18.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/optimism/-/optimism-0.18.1.tgz", "integrity": "sha1-XPFoR5IUE9uwrICZBzcDiLnGM18=", "dependencies": {"@wry/caches": "^1.0.0", "@wry/context": "^0.7.0", "@wry/trie": "^0.5.0", "tslib": "^2.3.0"}}, "node_modules/optionator": {"version": "0.9.4", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/optionator/-/optionator-0.9.4.tgz", "integrity": "sha1-fqHBpdkddk+yghOciP4R4YKjpzQ=", "dev": true, "dependencies": {"deep-is": "^0.1.3", "fast-levenshtein": "^2.0.6", "levn": "^0.4.1", "prelude-ls": "^1.2.1", "type-check": "^0.4.0", "word-wrap": "^1.2.5"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/ora": {"version": "5.4.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/ora/-/ora-5.4.1.tgz", "integrity": "sha1-GyZ4Qmr0rEpQkAjl5KyemVnbnhg=", "dev": true, "dependencies": {"bl": "^4.1.0", "chalk": "^4.1.0", "cli-cursor": "^3.1.0", "cli-spinners": "^2.5.0", "is-interactive": "^1.0.0", "is-unicode-supported": "^0.1.0", "log-symbols": "^4.1.0", "strip-ansi": "^6.0.0", "wcwidth": "^1.0.1"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/os-tmpdir": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/os-tmpdir/-/os-tmpdir-1.0.2.tgz", "integrity": "sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/p-limit": {"version": "3.1.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/p-limit/-/p-limit-3.1.0.tgz", "integrity": "sha1-4drMvnjQ0TiMoYxk/qOOPlfjcGs=", "dev": true, "dependencies": {"yocto-queue": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-locate": {"version": "5.0.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/p-locate/-/p-locate-5.0.0.tgz", "integrity": "sha1-g8gxXGeFAF470CGDlBHJ4RDm2DQ=", "dev": true, "dependencies": {"p-limit": "^3.0.2"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-map": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/p-map/-/p-map-4.0.0.tgz", "integrity": "sha1-uy+Vpe2i7BaOySdOBqdHw+KQTSs=", "dev": true, "dependencies": {"aggregate-error": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/param-case": {"version": "3.0.4", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/param-case/-/param-case-3.0.4.tgz", "integrity": "sha1-fRf+SqEr3jTUp32RrPtiGcqtAcU=", "dev": true, "dependencies": {"dot-case": "^3.0.4", "tslib": "^2.0.3"}}, "node_modules/parent-module": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/parent-module/-/parent-module-1.0.1.tgz", "integrity": "sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=", "dependencies": {"callsites": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/parse-filepath": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/parse-filepath/-/parse-filepath-1.0.2.tgz", "integrity": "sha1-pjISf1Oq89FYdvWHLz/6x2PWyJE=", "dev": true, "dependencies": {"is-absolute": "^1.0.0", "map-cache": "^0.2.0", "path-root": "^0.1.1"}, "engines": {"node": ">=0.8"}}, "node_modules/parse-json": {"version": "5.2.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/parse-json/-/parse-json-5.2.0.tgz", "integrity": "sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80=", "dependencies": {"@babel/code-frame": "^7.0.0", "error-ex": "^1.3.1", "json-parse-even-better-errors": "^2.3.0", "lines-and-columns": "^1.1.6"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/pascal-case": {"version": "3.1.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/pascal-case/-/pascal-case-3.1.2.tgz", "integrity": "sha1-tI4O8rmOIF58Ha50fQsVCCN2YOs=", "dev": true, "dependencies": {"no-case": "^3.0.4", "tslib": "^2.0.3"}}, "node_modules/path-case": {"version": "3.0.4", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/path-case/-/path-case-3.0.4.tgz", "integrity": "sha1-kWhkUzTrlCZYN1xW+AtMDLX4LG8=", "dev": true, "dependencies": {"dot-case": "^3.0.4", "tslib": "^2.0.3"}}, "node_modules/path-exists": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/path-exists/-/path-exists-4.0.0.tgz", "integrity": "sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=", "dev": true, "engines": {"node": ">=8"}}, "node_modules/path-key": {"version": "3.1.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/path-key/-/path-key-3.1.1.tgz", "integrity": "sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=", "dev": true, "engines": {"node": ">=8"}}, "node_modules/path-parse": {"version": "1.0.7", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/path-parse/-/path-parse-1.0.7.tgz", "integrity": "sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU="}, "node_modules/path-root": {"version": "0.1.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/path-root/-/path-root-0.1.1.tgz", "integrity": "sha1-mkpoFMrBwM1zNgqV8yCDyOpHRbc=", "dev": true, "dependencies": {"path-root-regex": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/path-root-regex": {"version": "0.1.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/path-root-regex/-/path-root-regex-0.1.2.tgz", "integrity": "sha1-v8zcjfWxLcUsi0PsONGNcsBLqW0=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/path-type": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/path-type/-/path-type-4.0.0.tgz", "integrity": "sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs=", "engines": {"node": ">=8"}}, "node_modules/picocolors": {"version": "1.1.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/picocolors/-/picocolors-1.1.1.tgz", "integrity": "sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s="}, "node_modules/picomatch": {"version": "2.3.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/picomatch/-/picomatch-2.3.1.tgz", "integrity": "sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=", "dev": true, "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/postcss": {"version": "8.5.6", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/postcss/-/postcss-8.5.6.tgz", "integrity": "sha1-KCUAZhWmGbT2Kp50JswSCzSajzw=", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/postcss"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "dependencies": {"nanoid": "^3.3.11", "picocolors": "^1.1.1", "source-map-js": "^1.2.1"}, "engines": {"node": "^10 || ^12 || >=14"}}, "node_modules/prelude-ls": {"version": "1.2.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/prelude-ls/-/prelude-ls-1.2.1.tgz", "integrity": "sha1-3rxkidem5rDnYRiIzsiAM30xY5Y=", "dev": true, "engines": {"node": ">= 0.8.0"}}, "node_modules/prettier": {"version": "3.6.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/prettier/-/prettier-3.6.2.tgz", "integrity": "sha1-zNoCoQA+u7K/2m+DoHSXj2CLk5M=", "dev": true, "bin": {"prettier": "bin/prettier.cjs"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/prettier/prettier?sponsor=1"}}, "node_modules/prettier-linter-helpers": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/prettier-linter-helpers/-/prettier-linter-helpers-1.0.0.tgz", "integrity": "sha1-0j1B/hN1ZG3i0BBNNFSjAIgCz3s=", "dev": true, "dependencies": {"fast-diff": "^1.1.2"}, "engines": {"node": ">=6.0.0"}}, "node_modules/promise": {"version": "7.3.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/promise/-/promise-7.3.1.tgz", "integrity": "sha1-BktyYCsY+Q8pGSuLG8QY/9Hr078=", "dev": true, "dependencies": {"asap": "~2.0.3"}}, "node_modules/prop-types": {"version": "15.8.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/prop-types/-/prop-types-15.8.1.tgz", "integrity": "sha1-Z9h78aaU9IQ1zzMsJK8QIUoxQLU=", "dependencies": {"loose-envify": "^1.4.0", "object-assign": "^4.1.1", "react-is": "^16.13.1"}}, "node_modules/prop-types/node_modules/react-is": {"version": "16.13.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/react-is/-/react-is-16.13.1.tgz", "integrity": "sha1-eJcppNw23imZ3BVt1sHZwYzqVqQ="}, "node_modules/punycode": {"version": "1.4.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/punycode/-/punycode-1.4.1.tgz", "integrity": "sha1-wNWmOycYgArY4esPpSachN1BhF4=", "dev": true}, "node_modules/pvtsutils": {"version": "1.3.6", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/pvtsutils/-/pvtsutils-1.3.6.tgz", "integrity": "sha1-7EbjTbdCK55P3FSQV4wYg2V9YAE=", "dev": true, "dependencies": {"tslib": "^2.8.1"}}, "node_modules/pvutils": {"version": "1.1.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/pvutils/-/pvutils-1.1.3.tgz", "integrity": "sha1-81/B0n5809+9OcCCbRc+gGoD9aM=", "dev": true, "engines": {"node": ">=6.0.0"}}, "node_modules/queue-microtask": {"version": "1.2.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/queue-microtask/-/queue-microtask-1.2.3.tgz", "integrity": "sha1-SSkii7xyTfrEPg77BYyve2z7YkM=", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}, "node_modules/react": {"version": "19.1.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/react/-/react-19.1.0.tgz", "integrity": "sha1-kmhktsSNp2J/AEeV1szlDpB5O3U=", "engines": {"node": ">=0.10.0"}}, "node_modules/react-dom": {"version": "19.1.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/react-dom/-/react-dom-19.1.0.tgz", "integrity": "sha1-EzVY3so3+h1oJwjfiQSyUYZ5NiM=", "dependencies": {"scheduler": "^0.26.0"}, "peerDependencies": {"react": "^19.1.0"}}, "node_modules/react-i18next": {"version": "15.6.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/react-i18next/-/react-i18next-15.6.0.tgz", "integrity": "sha1-8XBVh2Ib08wckq8BRZSc2tj9LhU=", "dependencies": {"@babel/runtime": "^7.27.6", "html-parse-stringify": "^3.0.1"}, "peerDependencies": {"i18next": ">= 23.2.3", "react": ">= 16.8.0", "typescript": "^5"}, "peerDependenciesMeta": {"react-dom": {"optional": true}, "react-native": {"optional": true}, "typescript": {"optional": true}}}, "node_modules/react-is": {"version": "19.1.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/react-is/-/react-is-19.1.0.tgz", "integrity": "sha1-gFvOMhVGt+FMCEmJx3AiNRu90Rs="}, "node_modules/react-refresh": {"version": "0.17.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/react-refresh/-/react-refresh-0.17.0.tgz", "integrity": "sha1-t+V5w2V/I9BOzL5K0uWKjtUeflM=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/react-router": {"version": "7.6.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/react-router/-/react-router-7.6.3.tgz", "integrity": "sha1-ek6ltHm2bSxJqPAAgSsjGbTQpto=", "dependencies": {"cookie": "^1.0.1", "set-cookie-parser": "^2.6.0"}, "engines": {"node": ">=20.0.0"}, "peerDependencies": {"react": ">=18", "react-dom": ">=18"}, "peerDependenciesMeta": {"react-dom": {"optional": true}}}, "node_modules/react-router-dom": {"version": "7.6.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/react-router-dom/-/react-router-dom-7.6.3.tgz", "integrity": "sha1-RWhucbuVjPgN2Tyjq/QRH+tO3TU=", "dependencies": {"react-router": "7.6.3"}, "engines": {"node": ">=20.0.0"}, "peerDependencies": {"react": ">=18", "react-dom": ">=18"}}, "node_modules/react-smooth": {"version": "4.0.4", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/react-smooth/-/react-smooth-4.0.4.tgz", "integrity": "sha1-pYdfi7YZY8phuBnO3FadwkU4lLQ=", "dependencies": {"fast-equals": "^5.0.1", "prop-types": "^15.8.1", "react-transition-group": "^4.4.5"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}}, "node_modules/react-toastify": {"version": "11.0.5", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/react-toastify/-/react-toastify-11.0.5.tgz", "integrity": "sha1-zkxC0Q7rQzmIqyJk0+RFxOnRMxM=", "dependencies": {"clsx": "^2.1.1"}, "peerDependencies": {"react": "^18 || ^19", "react-dom": "^18 || ^19"}}, "node_modules/react-transition-group": {"version": "4.4.5", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/react-transition-group/-/react-transition-group-4.4.5.tgz", "integrity": "sha1-5T1OPzNE2oUhSJ+++PJYHUK+zdE=", "dependencies": {"@babel/runtime": "^7.5.5", "dom-helpers": "^5.0.1", "loose-envify": "^1.4.0", "prop-types": "^15.6.2"}, "peerDependencies": {"react": ">=16.6.0", "react-dom": ">=16.6.0"}}, "node_modules/readable-stream": {"version": "3.6.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/readable-stream/-/readable-stream-3.6.2.tgz", "integrity": "sha1-VqmzbqllwAxak+8x6xEaDxEFaWc=", "dev": true, "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/recharts": {"version": "2.15.4", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/recharts/-/recharts-2.15.4.tgz", "integrity": "sha1-DtPmbAhDvPLZ+aFyyvl7HQUSel8=", "dependencies": {"clsx": "^2.0.0", "eventemitter3": "^4.0.1", "lodash": "^4.17.21", "react-is": "^18.3.1", "react-smooth": "^4.0.4", "recharts-scale": "^0.4.4", "tiny-invariant": "^1.3.1", "victory-vendor": "^36.6.8"}, "engines": {"node": ">=14"}, "peerDependencies": {"react": "^16.0.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^16.0.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}}, "node_modules/recharts-scale": {"version": "0.4.5", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/recharts-scale/-/recharts-scale-0.4.5.tgz", "integrity": "sha1-CWknHxTnMuZC/MW9SrJw1uh90dk=", "dependencies": {"decimal.js-light": "^2.4.1"}}, "node_modules/recharts/node_modules/react-is": {"version": "18.3.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/react-is/-/react-is-18.3.1.tgz", "integrity": "sha1-6DVX3BLq5jqZ4AOkY4ix3LtE234="}, "node_modules/rehackt": {"version": "0.1.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/rehackt/-/rehackt-0.1.0.tgz", "integrity": "sha1-p8XiichzRfcNqHKKfrh45dA8aWs=", "peerDependencies": {"@types/react": "*", "react": "*"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "react": {"optional": true}}}, "node_modules/relay-runtime": {"version": "12.0.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/relay-runtime/-/relay-runtime-12.0.0.tgz", "integrity": "sha1-HgOSgr214MG5p9x/a5oJ1PT/gjc=", "dev": true, "dependencies": {"@babel/runtime": "^7.0.0", "fbjs": "^3.0.0", "invariant": "^2.2.4"}}, "node_modules/remedial": {"version": "1.0.8", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/remedial/-/remedial-1.0.8.tgz", "integrity": "sha1-peT9UqDklWrbr2LaY6WkanjFeKA=", "dev": true, "engines": {"node": "*"}}, "node_modules/remove-trailing-separator": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/remove-trailing-separator/-/remove-trailing-separator-1.1.0.tgz", "integrity": "sha1-wkvOKig62tW8P1jg1IJJuSN52O8=", "dev": true}, "node_modules/remove-trailing-spaces": {"version": "1.0.9", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/remove-trailing-spaces/-/remove-trailing-spaces-1.0.9.tgz", "integrity": "sha1-OcJwownqFv2oQlP/vesbWvoKonE=", "dev": true}, "node_modules/require-directory": {"version": "2.1.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/require-directory/-/require-directory-2.1.1.tgz", "integrity": "sha1-jGStX9MNqxyXbiNE/+f3kqam30I=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/reselect": {"version": "5.1.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/reselect/-/reselect-5.1.1.tgz", "integrity": "sha1-x2ax611VgpHl5VApitsL7MJLty4="}, "node_modules/resolve": {"version": "1.22.10", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/resolve/-/resolve-1.22.10.tgz", "integrity": "sha1-tmPoP/sJu/I4aURza6roAwKbizk=", "dependencies": {"is-core-module": "^2.16.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/resolve-from": {"version": "5.0.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/resolve-from/-/resolve-from-5.0.0.tgz", "integrity": "sha1-w1IlhD3493bfIcV1V7wIfp39/Gk=", "dev": true, "engines": {"node": ">=8"}}, "node_modules/restore-cursor": {"version": "3.1.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/restore-cursor/-/restore-cursor-3.1.0.tgz", "integrity": "sha1-OfZ8VLOnpYzqUjbZXPADQjljH34=", "dev": true, "dependencies": {"onetime": "^5.1.0", "signal-exit": "^3.0.2"}, "engines": {"node": ">=8"}}, "node_modules/reusify": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/reusify/-/reusify-1.1.0.tgz", "integrity": "sha1-D+E7lSLhRz9RtVjueW4I8R+bSJ8=", "dev": true, "engines": {"iojs": ">=1.0.0", "node": ">=0.10.0"}}, "node_modules/rfdc": {"version": "1.4.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/rfdc/-/rfdc-1.4.1.tgz", "integrity": "sha1-d492xPtzHZNBTo+SX77PZMzn9so=", "dev": true}, "node_modules/rollup": {"version": "4.44.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/rollup/-/rollup-4.44.2.tgz", "integrity": "sha1-+u2yfLKqZ0JTDDlmgJLuy694xIg=", "dev": true, "dependencies": {"@types/estree": "1.0.8"}, "bin": {"rollup": "dist/bin/rollup"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "optionalDependencies": {"@rollup/rollup-android-arm-eabi": "4.44.2", "@rollup/rollup-android-arm64": "4.44.2", "@rollup/rollup-darwin-arm64": "4.44.2", "@rollup/rollup-darwin-x64": "4.44.2", "@rollup/rollup-freebsd-arm64": "4.44.2", "@rollup/rollup-freebsd-x64": "4.44.2", "@rollup/rollup-linux-arm-gnueabihf": "4.44.2", "@rollup/rollup-linux-arm-musleabihf": "4.44.2", "@rollup/rollup-linux-arm64-gnu": "4.44.2", "@rollup/rollup-linux-arm64-musl": "4.44.2", "@rollup/rollup-linux-loongarch64-gnu": "4.44.2", "@rollup/rollup-linux-powerpc64le-gnu": "4.44.2", "@rollup/rollup-linux-riscv64-gnu": "4.44.2", "@rollup/rollup-linux-riscv64-musl": "4.44.2", "@rollup/rollup-linux-s390x-gnu": "4.44.2", "@rollup/rollup-linux-x64-gnu": "4.44.2", "@rollup/rollup-linux-x64-musl": "4.44.2", "@rollup/rollup-win32-arm64-msvc": "4.44.2", "@rollup/rollup-win32-ia32-msvc": "4.44.2", "@rollup/rollup-win32-x64-msvc": "4.44.2", "fsevents": "~2.3.2"}}, "node_modules/run-async": {"version": "2.4.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/run-async/-/run-async-2.4.1.tgz", "integrity": "sha1-hEDsz5nqPnC9QJ1JqriOEMGJpFU=", "dev": true, "engines": {"node": ">=0.12.0"}}, "node_modules/run-parallel": {"version": "1.2.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/run-parallel/-/run-parallel-1.2.0.tgz", "integrity": "sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4=", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "dependencies": {"queue-microtask": "^1.2.2"}}, "node_modules/rxjs": {"version": "7.8.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/rxjs/-/rxjs-7.8.2.tgz", "integrity": "sha1-lVvEc+2K8RoAKivlIHG/R1Y4YHs=", "dev": true, "dependencies": {"tslib": "^2.1.0"}}, "node_modules/safe-buffer": {"version": "5.2.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}, "node_modules/safer-buffer": {"version": "2.1.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/safer-buffer/-/safer-buffer-2.1.2.tgz", "integrity": "sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=", "dev": true}, "node_modules/scheduler": {"version": "0.26.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/scheduler/-/scheduler-0.26.0.tgz", "integrity": "sha1-TOiowqIJXxPqEb+aRFvlDFVdYzc="}, "node_modules/scuid": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/scuid/-/scuid-1.1.0.tgz", "integrity": "sha1-0/n5IJVuc3pg9y0OStKAvzJNXas=", "dev": true}, "node_modules/semver": {"version": "6.3.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/semver/-/semver-6.3.1.tgz", "integrity": "sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=", "dev": true, "bin": {"semver": "bin/semver.js"}}, "node_modules/sentence-case": {"version": "3.0.4", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/sentence-case/-/sentence-case-3.0.4.tgz", "integrity": "sha1-NkWnuMEXx4f96HAgViJbtipFEx8=", "dev": true, "dependencies": {"no-case": "^3.0.4", "tslib": "^2.0.3", "upper-case-first": "^2.0.2"}}, "node_modules/set-cookie-parser": {"version": "2.7.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/set-cookie-parser/-/set-cookie-parser-2.7.1.tgz", "integrity": "sha1-MBbxUAciAt++kPre4FNXPMidKUM="}, "node_modules/setimmediate": {"version": "1.0.5", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/setimmediate/-/setimmediate-1.0.5.tgz", "integrity": "sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU=", "dev": true}, "node_modules/shebang-command": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/shebang-command/-/shebang-command-2.0.0.tgz", "integrity": "sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=", "dev": true, "dependencies": {"shebang-regex": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/shebang-regex": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/shebang-regex/-/shebang-regex-3.0.0.tgz", "integrity": "sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=", "dev": true, "engines": {"node": ">=8"}}, "node_modules/shell-quote": {"version": "1.8.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/shell-quote/-/shell-quote-1.8.3.tgz", "integrity": "sha1-VeQO8zz1xomQI1Oj2M0aZyXwi0s=", "dev": true, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/signal-exit": {"version": "3.0.7", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/signal-exit/-/signal-exit-3.0.7.tgz", "integrity": "sha1-qaF2f4r4QVURTqq9c/mSc8j1mtk=", "dev": true}, "node_modules/signedsource": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/signedsource/-/signedsource-1.0.0.tgz", "integrity": "sha1-HdrOSYF5j5O9gzlzgD2A1S6TrWo=", "dev": true}, "node_modules/slash": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/slash/-/slash-3.0.0.tgz", "integrity": "sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ=", "dev": true, "engines": {"node": ">=8"}}, "node_modules/slice-ansi": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/slice-ansi/-/slice-ansi-3.0.0.tgz", "integrity": "sha1-Md3BCTCht+C2ewjJbC9Jt3p4l4c=", "dev": true, "dependencies": {"ansi-styles": "^4.0.0", "astral-regex": "^2.0.0", "is-fullwidth-code-point": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/snake-case": {"version": "3.0.4", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/snake-case/-/snake-case-3.0.4.tgz", "integrity": "sha1-Tyu9Vo6ZNavf1ZPzTGkdrbScRSw=", "dev": true, "dependencies": {"dot-case": "^3.0.4", "tslib": "^2.0.3"}}, "node_modules/source-map": {"version": "0.5.7", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/source-map/-/source-map-0.5.7.tgz", "integrity": "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=", "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-js": {"version": "1.2.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/source-map-js/-/source-map-js-1.2.1.tgz", "integrity": "sha1-HOVlD93YerwJnto33P8CTCZnrkY=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/sponge-case": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/sponge-case/-/sponge-case-1.0.1.tgz", "integrity": "sha1-JggzuGRTiD2XT4SFTNtjrsxa70w=", "dev": true, "dependencies": {"tslib": "^2.0.3"}}, "node_modules/streamsearch": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/streamsearch/-/streamsearch-1.1.0.tgz", "integrity": "sha1-QE3R4iR8qUr1VOhBqO8OqiONp2Q=", "dev": true, "engines": {"node": ">=10.0.0"}}, "node_modules/string_decoder": {"version": "1.3.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/string_decoder/-/string_decoder-1.3.0.tgz", "integrity": "sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4=", "dev": true, "dependencies": {"safe-buffer": "~5.2.0"}}, "node_modules/string-env-interpolation": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/string-env-interpolation/-/string-env-interpolation-1.0.1.tgz", "integrity": "sha1-rUOXrkrFP+bJHRQCrW9qUoYscVI=", "dev": true}, "node_modules/string-width": {"version": "4.2.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/string-width/-/string-width-4.2.3.tgz", "integrity": "sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=", "dev": true, "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-ansi": {"version": "6.0.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/strip-ansi/-/strip-ansi-6.0.1.tgz", "integrity": "sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=", "dev": true, "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-json-comments": {"version": "3.1.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/strip-json-comments/-/strip-json-comments-3.1.1.tgz", "integrity": "sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=", "dev": true, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/stylis": {"version": "4.2.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/stylis/-/stylis-4.2.0.tgz", "integrity": "sha1-edruAgiWTI/mlaQvz/ysYzohGlE="}, "node_modules/supports-color": {"version": "7.2.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/supports-color/-/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "dev": true, "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/supports-preserve-symlinks-flag": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz", "integrity": "sha1-btpL00SjyUrqN21MwxvHcxEDngk=", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/swap-case": {"version": "2.0.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/swap-case/-/swap-case-2.0.2.tgz", "integrity": "sha1-Zxrts8nBN+KYXvUcUfnphEW/cNk=", "dev": true, "dependencies": {"tslib": "^2.0.3"}}, "node_modules/symbol-observable": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/symbol-observable/-/symbol-observable-4.0.0.tgz", "integrity": "sha1-W0JfGSJ56H8vm5N6yFQNGYSzkgU=", "engines": {"node": ">=0.10"}}, "node_modules/sync-fetch": {"version": "0.6.0-2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/sync-fetch/-/sync-fetch-0.6.0-2.tgz", "integrity": "sha1-2C1tyO+vLRA6kBXnvXugv8jgePI=", "dev": true, "dependencies": {"node-fetch": "^3.3.2", "timeout-signal": "^2.0.0", "whatwg-mimetype": "^4.0.0"}, "engines": {"node": ">=18"}}, "node_modules/sync-fetch/node_modules/node-fetch": {"version": "3.3.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/node-fetch/-/node-fetch-3.3.2.tgz", "integrity": "sha1-0eiJus33M7T/OyskPrehKGagt4s=", "dev": true, "dependencies": {"data-uri-to-buffer": "^4.0.0", "fetch-blob": "^3.1.4", "formdata-polyfill": "^4.0.10"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/node-fetch"}}, "node_modules/synckit": {"version": "0.11.8", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/synckit/-/synckit-0.11.8.tgz", "integrity": "sha1-sqqumYpO9H3tYHc60G58uCH1VFc=", "dev": true, "dependencies": {"@pkgr/core": "^0.2.4"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/synckit"}}, "node_modules/through": {"version": "2.3.8", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/through/-/through-2.3.8.tgz", "integrity": "sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=", "dev": true}, "node_modules/timeout-signal": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/timeout-signal/-/timeout-signal-2.0.0.tgz", "integrity": "sha1-IyB+pEjVAli7De/jvupKRnZDq7o=", "dev": true, "engines": {"node": ">=16"}}, "node_modules/tiny-invariant": {"version": "1.3.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/tiny-invariant/-/tiny-invariant-1.3.3.tgz", "integrity": "sha1-RmgLeoc6DV0QAFmV65CnDXTWASc="}, "node_modules/tinyglobby": {"version": "0.2.14", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/tinyglobby/-/tinyglobby-0.2.14.tgz", "integrity": "sha1-UoCwzz+XKwUOdK6IQGwKalj0B50=", "dev": true, "dependencies": {"fdir": "^6.4.4", "picomatch": "^4.0.2"}, "engines": {"node": ">=12.0.0"}, "funding": {"url": "https://github.com/sponsors/SuperchupuDev"}}, "node_modules/tinyglobby/node_modules/fdir": {"version": "6.4.6", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/fdir/-/fdir-6.4.6.tgz", "integrity": "sha1-KyaMAjJpcGMRG78/ZIEKKnQbooE=", "dev": true, "peerDependencies": {"picomatch": "^3 || ^4"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}}, "node_modules/tinyglobby/node_modules/picomatch": {"version": "4.0.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/picomatch/-/picomatch-4.0.2.tgz", "integrity": "sha1-d8dCkx6PO4gglGx2zQwfE3MNHas=", "dev": true, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/title-case": {"version": "3.0.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/title-case/-/title-case-3.0.3.tgz", "integrity": "sha1-vGibRvAuQR8dHh0IH3w97KBImYI=", "dev": true, "dependencies": {"tslib": "^2.0.3"}}, "node_modules/tmp": {"version": "0.0.33", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/tmp/-/tmp-0.0.33.tgz", "integrity": "sha1-bTQzWIl2jSGyvNoKonfO07G/rfk=", "dev": true, "dependencies": {"os-tmpdir": "~1.0.2"}, "engines": {"node": ">=0.6.0"}}, "node_modules/to-regex-range": {"version": "5.0.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/to-regex-range/-/to-regex-range-5.0.1.tgz", "integrity": "sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=", "dev": true, "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/tr46": {"version": "0.0.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/tr46/-/tr46-0.0.3.tgz", "integrity": "sha1-gYT9NH2snNwYWZLzpmIuFLnZq2o=", "dev": true}, "node_modules/ts-api-utils": {"version": "2.1.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/ts-api-utils/-/ts-api-utils-2.1.0.tgz", "integrity": "sha1-WV9wlORu7TZME/0j51+VE9Kbr5E=", "dev": true, "engines": {"node": ">=18.12"}, "peerDependencies": {"typescript": ">=4.8.4"}}, "node_modules/ts-invariant": {"version": "0.10.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/ts-invariant/-/ts-invariant-0.10.3.tgz", "integrity": "sha1-PgSP+W6RRZ/8oBME28f2HB9kL2w=", "dependencies": {"tslib": "^2.1.0"}, "engines": {"node": ">=8"}}, "node_modules/ts-log": {"version": "2.2.7", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/ts-log/-/ts-log-2.2.7.tgz", "integrity": "sha1-T0USFEiYt3yZhOkVhwdvy4UYaI4=", "dev": true}, "node_modules/tslib": {"version": "2.8.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/tslib/-/tslib-2.8.1.tgz", "integrity": "sha1-YS7+TtI11Wfoq6Xypfq3AoCt6D8="}, "node_modules/type-check": {"version": "0.4.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/type-check/-/type-check-0.4.0.tgz", "integrity": "sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE=", "dev": true, "dependencies": {"prelude-ls": "^1.2.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/type-fest": {"version": "0.21.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/type-fest/-/type-fest-0.21.3.tgz", "integrity": "sha1-0mCiSwGYQ24TP6JqUkptZfo7Ljc=", "dev": true, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/typescript": {"version": "5.7.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/typescript/-/typescript-5.7.3.tgz", "integrity": "sha1-kZtEp9u4WDqbhW0WK+JKVL+ABz4=", "devOptional": true, "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=14.17"}}, "node_modules/typescript-eslint": {"version": "8.35.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/typescript-eslint/-/typescript-eslint-8.35.1.tgz", "integrity": "sha1-Td7aXFd3p72GUWKA2Ama2gYFXy8=", "dev": true, "dependencies": {"@typescript-eslint/eslint-plugin": "8.35.1", "@typescript-eslint/parser": "8.35.1", "@typescript-eslint/utils": "8.35.1"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <5.9.0"}}, "node_modules/ua-parser-js": {"version": "1.0.40", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/ua-parser-js/-/ua-parser-js-1.0.40.tgz", "integrity": "sha1-rGr/T9jqPnlKaqdD7Jwvwp51tnU=", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/ua-parser-js"}, {"type": "paypal", "url": "https://paypal.me/faisalman"}, {"type": "github", "url": "https://github.com/sponsors/faisalman"}], "bin": {"ua-parser-js": "script/cli.js"}, "engines": {"node": "*"}}, "node_modules/unc-path-regex": {"version": "0.1.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/unc-path-regex/-/unc-path-regex-0.1.2.tgz", "integrity": "sha1-5z3T17DXxe2G+6xrCufYxqadUPo=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/undici-types": {"version": "6.21.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/undici-types/-/undici-types-6.21.0.tgz", "integrity": "sha1-aR0ArzkJvpOn+qE75hs6W1DvEss=", "dev": true}, "node_modules/unixify": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/unixify/-/unixify-1.0.0.tgz", "integrity": "sha1-OmQcjC/7zk2mg6XHDwOkYpQMIJA=", "dev": true, "dependencies": {"normalize-path": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/update-browserslist-db": {"version": "1.1.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz", "integrity": "sha1-NIN33SRSFvnnBg/1CxWht0C3VCA=", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "dependencies": {"escalade": "^3.2.0", "picocolors": "^1.1.1"}, "bin": {"update-browserslist-db": "cli.js"}, "peerDependencies": {"browserslist": ">= 4.21.0"}}, "node_modules/upper-case": {"version": "2.0.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/upper-case/-/upper-case-2.0.2.tgz", "integrity": "sha1-2JgQgj+qsd8VSbfZenb4Ziuub3o=", "dev": true, "dependencies": {"tslib": "^2.0.3"}}, "node_modules/upper-case-first": {"version": "2.0.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/upper-case-first/-/upper-case-first-2.0.2.tgz", "integrity": "sha1-mSwyc/iCq9GdHgKJTMFHEX+EQyQ=", "dev": true, "dependencies": {"tslib": "^2.0.3"}}, "node_modules/uri-js": {"version": "4.4.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/uri-js/-/uri-js-4.4.1.tgz", "integrity": "sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=", "dev": true, "dependencies": {"punycode": "^2.1.0"}}, "node_modules/uri-js/node_modules/punycode": {"version": "2.3.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/punycode/-/punycode-2.3.1.tgz", "integrity": "sha1-AnQi4vrsCyXhVJw+G9gwm5EztuU=", "dev": true, "engines": {"node": ">=6"}}, "node_modules/urlpattern-polyfill": {"version": "8.0.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/urlpattern-polyfill/-/urlpattern-polyfill-8.0.2.tgz", "integrity": "sha1-mfCW417/i/S1oqp9WKFSPW68fOU=", "dev": true}, "node_modules/use-sync-external-store": {"version": "1.5.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/use-sync-external-store/-/use-sync-external-store-1.5.0.tgz", "integrity": "sha1-VRIuKj7dKmwQYXTCdIXg/Vm8/KA=", "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}}, "node_modules/util-deprecate": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/util-deprecate/-/util-deprecate-1.0.2.tgz", "integrity": "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=", "dev": true}, "node_modules/victory-vendor": {"version": "36.9.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/victory-vendor/-/victory-vendor-36.9.2.tgz", "integrity": "sha1-ZosCpEj6TqD3iNv0Iot+ZGaf+AE=", "dependencies": {"@types/d3-array": "^3.0.3", "@types/d3-ease": "^3.0.0", "@types/d3-interpolate": "^3.0.1", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-time": "^3.0.0", "@types/d3-timer": "^3.0.0", "d3-array": "^3.1.6", "d3-ease": "^3.0.1", "d3-interpolate": "^3.0.1", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-time": "^3.0.0", "d3-timer": "^3.0.1"}}, "node_modules/vite": {"version": "6.3.5", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/vite/-/vite-6.3.5.tgz", "integrity": "sha1-/sc4eQE8nAEoyNKEUExtGUENEqM=", "dev": true, "dependencies": {"esbuild": "^0.25.0", "fdir": "^6.4.4", "picomatch": "^4.0.2", "postcss": "^8.5.3", "rollup": "^4.34.9", "tinyglobby": "^0.2.13"}, "bin": {"vite": "bin/vite.js"}, "engines": {"node": "^18.0.0 || ^20.0.0 || >=22.0.0"}, "funding": {"url": "https://github.com/vitejs/vite?sponsor=1"}, "optionalDependencies": {"fsevents": "~2.3.3"}, "peerDependencies": {"@types/node": "^18.0.0 || ^20.0.0 || >=22.0.0", "jiti": ">=1.21.0", "less": "*", "lightningcss": "^1.21.0", "sass": "*", "sass-embedded": "*", "stylus": "*", "sugarss": "*", "terser": "^5.16.0", "tsx": "^4.8.1", "yaml": "^2.4.2"}, "peerDependenciesMeta": {"@types/node": {"optional": true}, "jiti": {"optional": true}, "less": {"optional": true}, "lightningcss": {"optional": true}, "sass": {"optional": true}, "sass-embedded": {"optional": true}, "stylus": {"optional": true}, "sugarss": {"optional": true}, "terser": {"optional": true}, "tsx": {"optional": true}, "yaml": {"optional": true}}}, "node_modules/vite/node_modules/fdir": {"version": "6.4.6", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/fdir/-/fdir-6.4.6.tgz", "integrity": "sha1-KyaMAjJpcGMRG78/ZIEKKnQbooE=", "dev": true, "peerDependencies": {"picomatch": "^3 || ^4"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}}, "node_modules/vite/node_modules/picomatch": {"version": "4.0.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/picomatch/-/picomatch-4.0.2.tgz", "integrity": "sha1-d8dCkx6PO4gglGx2zQwfE3MNHas=", "dev": true, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/void-elements": {"version": "3.1.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/void-elements/-/void-elements-3.1.0.tgz", "integrity": "sha1-YU9/v42AHwu18GYfWy9XhXUOTwk=", "engines": {"node": ">=0.10.0"}}, "node_modules/wcwidth": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/wcwidth/-/wcwidth-1.0.1.tgz", "integrity": "sha1-8LDc+RW8X/FSivrbLA4XtTLaL+g=", "dev": true, "dependencies": {"defaults": "^1.0.3"}}, "node_modules/web-streams-polyfill": {"version": "3.3.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/web-streams-polyfill/-/web-streams-polyfill-3.3.3.tgz", "integrity": "sha1-IHO5Gi/bH7+9QB594KyfghTOy0s=", "dev": true, "engines": {"node": ">= 8"}}, "node_modules/webcrypto-core": {"version": "1.8.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/webcrypto-core/-/webcrypto-core-1.8.1.tgz", "integrity": "sha1-CdW9ipxI6fvK9BLgax/xpXUUzoY=", "dev": true, "dependencies": {"@peculiar/asn1-schema": "^2.3.13", "@peculiar/json-schema": "^1.1.12", "asn1js": "^3.0.5", "pvtsutils": "^1.3.5", "tslib": "^2.7.0"}}, "node_modules/webidl-conversions": {"version": "3.0.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/webidl-conversions/-/webidl-conversions-3.0.1.tgz", "integrity": "sha1-JFNCdeKnvGvnvIZhHMFq4KVlSHE=", "dev": true}, "node_modules/whatwg-mimetype": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/whatwg-mimetype/-/whatwg-mimetype-4.0.0.tgz", "integrity": "sha1-vBv5SphdxQOI1UqSWKxAXDyi/Ao=", "dev": true, "engines": {"node": ">=18"}}, "node_modules/whatwg-url": {"version": "5.0.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/whatwg-url/-/whatwg-url-5.0.0.tgz", "integrity": "sha1-lmRU6HZUYuN2RNNib2dCzotwll0=", "dev": true, "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}}, "node_modules/which": {"version": "2.0.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/which/-/which-2.0.2.tgz", "integrity": "sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=", "dev": true, "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "node_modules/word-wrap": {"version": "1.2.5", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/word-wrap/-/word-wrap-1.2.5.tgz", "integrity": "sha1-0sRcbdT7zmIaZvE2y+Mor9BBCzQ=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/wrap-ansi": {"version": "6.2.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/wrap-ansi/-/wrap-ansi-6.2.0.tgz", "integrity": "sha1-6Tk7oHEC5skaOyIUePAlfNKFblM=", "dev": true, "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=8"}}, "node_modules/ws": {"version": "8.18.3", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/ws/-/ws-8.18.3.tgz", "integrity": "sha1-tWuIq//eYnkcY5FwQAyT3LDJVHI=", "dev": true, "engines": {"node": ">=10.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/y18n": {"version": "5.0.8", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/y18n/-/y18n-5.0.8.tgz", "integrity": "sha1-f0k00PfKjFb5UxSTndzS3ZHOHVU=", "dev": true, "engines": {"node": ">=10"}}, "node_modules/yallist": {"version": "3.1.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/yallist/-/yallist-3.1.1.tgz", "integrity": "sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=", "dev": true}, "node_modules/yaml": {"version": "2.8.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/yaml/-/yaml-2.8.0.tgz", "integrity": "sha1-FfjJhmIRvcLTeBoIkORNT6Gl//Y=", "dev": true, "bin": {"yaml": "bin.mjs"}, "engines": {"node": ">= 14.6"}}, "node_modules/yaml-ast-parser": {"version": "0.0.43", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/yaml-ast-parser/-/yaml-ast-parser-0.0.43.tgz", "integrity": "sha1-6KI+b7TDgHarkplcXcoz89PXybs=", "dev": true}, "node_modules/yargs": {"version": "17.7.2", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/yargs/-/yargs-17.7.2.tgz", "integrity": "sha1-mR3zmspnWhkrgW4eA2P5110qomk=", "dev": true, "dependencies": {"cliui": "^8.0.1", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.3", "y18n": "^5.0.5", "yargs-parser": "^21.1.1"}, "engines": {"node": ">=12"}}, "node_modules/yargs-parser": {"version": "21.1.1", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/yargs-parser/-/yargs-parser-21.1.1.tgz", "integrity": "sha1-kJa87r+ZDSG7MfqVFuDt4pSnfTU=", "dev": true, "engines": {"node": ">=12"}}, "node_modules/yocto-queue": {"version": "0.1.0", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/yocto-queue/-/yocto-queue-0.1.0.tgz", "integrity": "sha1-ApTrPe4FAo0x7hpfosVWpqrxChs=", "dev": true, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/zen-observable": {"version": "0.8.15", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/zen-observable/-/zen-observable-0.8.15.tgz", "integrity": "sha1-lkFcUS2OP/2SCv04iWBOMLnqrBU="}, "node_modules/zen-observable-ts": {"version": "1.2.5", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/zen-observable-ts/-/zen-observable-ts-1.2.5.tgz", "integrity": "sha1-bG2eo9OoQoEsbpUZIJNloSK6i1g=", "dependencies": {"zen-observable": "0.8.15"}}, "node_modules/zustand": {"version": "5.0.6", "resolved": "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/zustand/-/zustand-5.0.6.tgz", "integrity": "sha1-otpD2Nw9MeMUJ55brsBil76nClw=", "engines": {"node": ">=12.20.0"}, "peerDependencies": {"@types/react": ">=18.0.0", "immer": ">=9.0.6", "react": ">=18.0.0", "use-sync-external-store": ">=1.2.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "immer": {"optional": true}, "react": {"optional": true}, "use-sync-external-store": {"optional": true}}}}}