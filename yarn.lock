# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@ampproject/remapping@^2.2.0":
  version "2.3.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@ampproject/remapping/-/remapping-2.3.0.tgz"
  integrity sha1-7UQbb6YAByUgzhi0PSyMyMrsx/Q=
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.24"

"@apollo/client@^3.13.1":
  version "3.13.8"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@apollo/client/-/client-3.13.8.tgz"
  integrity sha1-7x1JpbE0xppV4/E3FkqOMjrvTio=
  dependencies:
    "@graphql-typed-document-node/core" "^3.1.1"
    "@wry/caches" "^1.0.0"
    "@wry/equality" "^0.5.6"
    "@wry/trie" "^0.5.0"
    graphql-tag "^2.12.6"
    hoist-non-react-statics "^3.3.2"
    optimism "^0.18.0"
    prop-types "^15.7.2"
    rehackt "^0.1.0"
    symbol-observable "^4.0.0"
    ts-invariant "^0.10.3"
    tslib "^2.3.0"
    zen-observable-ts "^1.2.5"

"@ardatan/relay-compiler@^12.0.3":
  version "12.0.3"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@ardatan/relay-compiler/-/relay-compiler-12.0.3.tgz"
  integrity sha1-pggkZy2n987yo4ee2DOxINKS+gg=
  dependencies:
    "@babel/generator" "^7.26.10"
    "@babel/parser" "^7.26.10"
    "@babel/runtime" "^7.26.10"
    chalk "^4.0.0"
    fb-watchman "^2.0.0"
    immutable "~3.7.6"
    invariant "^2.2.4"
    nullthrows "^1.1.1"
    relay-runtime "12.0.0"
    signedsource "^1.0.0"

"@azure/msal-browser@^4.14.0", "@azure/msal-browser@^4.4.0":
  version "4.14.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@azure/msal-browser/-/msal-browser-4.14.0.tgz"
  integrity sha1-D53VjXsPAPDfht5fAICtTG+xXq0=
  dependencies:
    "@azure/msal-common" "15.8.0"

"@azure/msal-common@15.8.0":
  version "15.8.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@azure/msal-common/-/msal-common-15.8.0.tgz"
  integrity sha1-ql8Eqp+Zmo8Asz/PdbNb3EnXz18=

"@azure/msal-react@^3.0.5":
  version "3.0.14"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@azure/msal-react/-/msal-react-3.0.14.tgz"
  integrity sha1-tPAvT+nYEWiGx2h87cLIwPnOFR8=

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@babel/code-frame/-/code-frame-7.27.1.tgz"
  integrity sha1-IA9xXmbVKiOyIalDVTSpHME61b4=
  dependencies:
    "@babel/helper-validator-identifier" "^7.27.1"
    js-tokens "^4.0.0"
    picocolors "^1.1.1"

"@babel/compat-data@^7.27.2":
  version "7.28.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@babel/compat-data/-/compat-data-7.28.0.tgz"
  integrity sha1-n8b9WMKmoVJDzROYMiSWg5IHB5A=

"@babel/core@^7.0.0", "@babel/core@^7.0.0-0", "@babel/core@^7.26.10", "@babel/core@^7.27.4":
  version "7.28.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@babel/core/-/core-7.28.0.tgz"
  integrity sha1-VdrYCNW/NEWhCO78iOo/3wNHSaQ=
  dependencies:
    "@ampproject/remapping" "^2.2.0"
    "@babel/code-frame" "^7.27.1"
    "@babel/generator" "^7.28.0"
    "@babel/helper-compilation-targets" "^7.27.2"
    "@babel/helper-module-transforms" "^7.27.3"
    "@babel/helpers" "^7.27.6"
    "@babel/parser" "^7.28.0"
    "@babel/template" "^7.27.2"
    "@babel/traverse" "^7.28.0"
    "@babel/types" "^7.28.0"
    convert-source-map "^2.0.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.2"
    json5 "^2.2.3"
    semver "^6.3.1"

"@babel/generator@^7.18.13", "@babel/generator@^7.26.10", "@babel/generator@^7.28.0":
  version "7.28.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@babel/generator/-/generator-7.28.0.tgz"
  integrity sha1-nML3vW6wVNd9xmwmZBSKDFEYrNI=
  dependencies:
    "@babel/parser" "^7.28.0"
    "@babel/types" "^7.28.0"
    "@jridgewell/gen-mapping" "^0.3.12"
    "@jridgewell/trace-mapping" "^0.3.28"
    jsesc "^3.0.2"

"@babel/helper-compilation-targets@^7.27.2":
  version "7.27.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz"
  integrity sha1-RqD276uAjVHSnOloWN0Qzocycz0=
  dependencies:
    "@babel/compat-data" "^7.27.2"
    "@babel/helper-validator-option" "^7.27.1"
    browserslist "^4.24.0"
    lru-cache "^5.1.1"
    semver "^6.3.1"

"@babel/helper-globals@^7.28.0":
  version "7.28.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@babel/helper-globals/-/helper-globals-7.28.0.tgz"
  integrity sha1-uUMN8qpOF7woZl6t6uiqHZheZnQ=

"@babel/helper-module-imports@^7.16.7", "@babel/helper-module-imports@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz"
  integrity sha1-fvdpoyPiZV4SZnO7bS1pE7vq0gQ=
  dependencies:
    "@babel/traverse" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/helper-module-transforms@^7.27.3":
  version "7.27.3"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@babel/helper-module-transforms/-/helper-module-transforms-7.27.3.tgz"
  integrity sha1-2wu8+6WAL573hwcFp++HiFCO3gI=
  dependencies:
    "@babel/helper-module-imports" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"
    "@babel/traverse" "^7.27.3"

"@babel/helper-plugin-utils@^7.20.2", "@babel/helper-plugin-utils@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz"
  integrity sha1-3bL4dlNP+AE+bCspm/TTmzxR1Ew=

"@babel/helper-string-parser@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz"
  integrity sha1-VNp5YJerGc5n7Z+ItHuy7Ek2doc=

"@babel/helper-validator-identifier@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz"
  integrity sha1-pwVNzBRaln3U3I/uhFpXwTFsnfg=

"@babel/helper-validator-option@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz"
  integrity sha1-+lL1sefbGrBJRFtCHERxMDiXcC8=

"@babel/helpers@^7.27.6":
  version "7.27.6"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@babel/helpers/-/helpers-7.27.6.tgz"
  integrity sha1-ZFb+0VsstmnS0fq+hLZrNJkdgSw=
  dependencies:
    "@babel/template" "^7.27.2"
    "@babel/types" "^7.27.6"

"@babel/parser@^7.1.0", "@babel/parser@^7.20.7", "@babel/parser@^7.26.10", "@babel/parser@^7.27.2", "@babel/parser@^7.28.0":
  version "7.28.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@babel/parser/-/parser-7.28.0.tgz"
  integrity sha1-l5gp+6tRop4TkB5agHE9vLhAgl4=
  dependencies:
    "@babel/types" "^7.28.0"

"@babel/plugin-syntax-import-assertions@^7.26.0":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-7.27.1.tgz"
  integrity sha1-iIlK79KwO17mrRVip8jhWHSWrs0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-react-jsx-self@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.27.1.tgz"
  integrity sha1-r2eNhQas9SxXfKxz/3/mYVyF/JI=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-react-jsx-source@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.27.1.tgz"
  integrity sha1-3P4sJAlLt1e/c5YDdOfFXkNPGfA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/runtime@^7.0.0", "@babel/runtime@^7.12.5", "@babel/runtime@^7.18.3", "@babel/runtime@^7.25.7", "@babel/runtime@^7.26.10", "@babel/runtime@^7.27.6", "@babel/runtime@^7.5.5", "@babel/runtime@^7.8.7":
  version "7.27.6"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@babel/runtime/-/runtime-7.27.6.tgz"
  integrity sha1-7EBwoE12uujduxB3C6VXFKQXt8Y=

"@babel/template@^7.18.10", "@babel/template@^7.20.7", "@babel/template@^7.27.2":
  version "7.27.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@babel/template/-/template-7.27.2.tgz"
  integrity sha1-+njO7TxOe2Pr9ss55YUvykX2gJ0=
  dependencies:
    "@babel/code-frame" "^7.27.1"
    "@babel/parser" "^7.27.2"
    "@babel/types" "^7.27.1"

"@babel/traverse@^7.26.10", "@babel/traverse@^7.27.1", "@babel/traverse@^7.27.3", "@babel/traverse@^7.28.0":
  version "7.28.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@babel/traverse/-/traverse-7.28.0.tgz"
  integrity sha1-UYqhEzWbBiBCN54zPbGDgLU340s=
  dependencies:
    "@babel/code-frame" "^7.27.1"
    "@babel/generator" "^7.28.0"
    "@babel/helper-globals" "^7.28.0"
    "@babel/parser" "^7.28.0"
    "@babel/template" "^7.27.2"
    "@babel/types" "^7.28.0"
    debug "^4.3.1"

"@babel/types@^7.0.0", "@babel/types@^7.18.13", "@babel/types@^7.20.7", "@babel/types@^7.26.10", "@babel/types@^7.27.1", "@babel/types@^7.27.6", "@babel/types@^7.28.0":
  version "7.28.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@babel/types/-/types-7.28.0.tgz"
  integrity sha1-L9AVmm3HNTkzkgxDE2M1qbJk2VA=
  dependencies:
    "@babel/helper-string-parser" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"

"@emotion/babel-plugin@^11.13.5":
  version "11.13.5"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@emotion/babel-plugin/-/babel-plugin-11.13.5.tgz"
  integrity sha1-6rjWXb3tdODs/SjcIY51YHxOe8A=
  dependencies:
    "@babel/helper-module-imports" "^7.16.7"
    "@babel/runtime" "^7.18.3"
    "@emotion/hash" "^0.9.2"
    "@emotion/memoize" "^0.9.0"
    "@emotion/serialize" "^1.3.3"
    babel-plugin-macros "^3.1.0"
    convert-source-map "^1.5.0"
    escape-string-regexp "^4.0.0"
    find-root "^1.1.0"
    source-map "^0.5.7"
    stylis "4.2.0"

"@emotion/cache@^11.14.0":
  version "11.14.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@emotion/cache/-/cache-11.14.0.tgz"
  integrity sha1-7kSyaYbuuTyL6Cu5Lx96myGy7XY=
  dependencies:
    "@emotion/memoize" "^0.9.0"
    "@emotion/sheet" "^1.4.0"
    "@emotion/utils" "^1.4.2"
    "@emotion/weak-memoize" "^0.4.0"
    stylis "4.2.0"

"@emotion/hash@^0.9.2":
  version "0.9.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@emotion/hash/-/hash-0.9.2.tgz"
  integrity sha1-/5IhufWLTf5h5hmneIc0vWP2iYs=

"@emotion/is-prop-valid@^1.3.0":
  version "1.3.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@emotion/is-prop-valid/-/is-prop-valid-1.3.1.tgz"
  integrity sha1-jVzxEy+DbXrb5CzwtJ33gW/IgkA=
  dependencies:
    "@emotion/memoize" "^0.9.0"

"@emotion/memoize@^0.9.0":
  version "0.9.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@emotion/memoize/-/memoize-0.9.0.tgz"
  integrity sha1-dFlp1kmXd3a0P8dkjFVqqkYrQQI=

"@emotion/react@^11.0.0-rc.0", "@emotion/react@^11.14.0", "@emotion/react@^11.4.1", "@emotion/react@^11.5.0", "@emotion/react@^11.9.0":
  version "11.14.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@emotion/react/-/react-11.14.0.tgz"
  integrity sha1-z6rjXrxn3Z706i6azGzSnhV90F0=
  dependencies:
    "@babel/runtime" "^7.18.3"
    "@emotion/babel-plugin" "^11.13.5"
    "@emotion/cache" "^11.14.0"
    "@emotion/serialize" "^1.3.3"
    "@emotion/use-insertion-effect-with-fallbacks" "^1.2.0"
    "@emotion/utils" "^1.4.2"
    "@emotion/weak-memoize" "^0.4.0"
    hoist-non-react-statics "^3.3.1"

"@emotion/serialize@^1.3.3":
  version "1.3.3"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@emotion/serialize/-/serialize-1.3.3.tgz"
  integrity sha1-0pFTEAXxfXBNBGOgMv5nnzdlCeg=
  dependencies:
    "@emotion/hash" "^0.9.2"
    "@emotion/memoize" "^0.9.0"
    "@emotion/unitless" "^0.10.0"
    "@emotion/utils" "^1.4.2"
    csstype "^3.0.2"

"@emotion/sheet@^1.4.0":
  version "1.4.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@emotion/sheet/-/sheet-1.4.0.tgz"
  integrity sha1-ySmcNNJIvCboJWNzX3iVPS78qDw=

"@emotion/styled@^11.14.0", "@emotion/styled@^11.3.0", "@emotion/styled@^11.8.1":
  version "11.14.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@emotion/styled/-/styled-11.14.1.tgz"
  integrity sha1-jDS+0pSOg+GYA3AwVhTCCVWqzRw=
  dependencies:
    "@babel/runtime" "^7.18.3"
    "@emotion/babel-plugin" "^11.13.5"
    "@emotion/is-prop-valid" "^1.3.0"
    "@emotion/serialize" "^1.3.3"
    "@emotion/use-insertion-effect-with-fallbacks" "^1.2.0"
    "@emotion/utils" "^1.4.2"

"@emotion/unitless@^0.10.0":
  version "0.10.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@emotion/unitless/-/unitless-0.10.0.tgz"
  integrity sha1-KvL3x+UVD0l72r2EjOeyGKJ890U=

"@emotion/use-insertion-effect-with-fallbacks@^1.2.0":
  version "1.2.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@emotion/use-insertion-effect-with-fallbacks/-/use-insertion-effect-with-fallbacks-1.2.0.tgz"
  integrity sha1-ioy3e1kOCa/7lg9P8emonlMnOL8=

"@emotion/utils@^1.4.2":
  version "1.4.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@emotion/utils/-/utils-1.4.2.tgz"
  integrity sha1-bfbEWIH8scQS1miKMRqYt/WcG1I=

"@emotion/weak-memoize@^0.4.0":
  version "0.4.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@emotion/weak-memoize/-/weak-memoize-0.4.0.tgz"
  integrity sha1-XhP6yIfwjET3awzK8zcOsA/sm7Y=

"@envelop/core@^5.2.3":
  version "5.3.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@envelop/core/-/core-5.3.0.tgz"
  integrity sha1-7mZsqpTcL9ywDJZZz8Bndf+V+qE=
  dependencies:
    "@envelop/instrumentation" "^1.0.0"
    "@envelop/types" "^5.2.1"
    "@whatwg-node/promise-helpers" "^1.2.4"
    tslib "^2.5.0"

"@envelop/instrumentation@^1.0.0":
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@envelop/instrumentation/-/instrumentation-1.0.0.tgz"
  integrity sha1-QyaDkuBl2LqXXKy99Pwpff4+EeU=
  dependencies:
    "@whatwg-node/promise-helpers" "^1.2.1"
    tslib "^2.5.0"

"@envelop/types@^5.2.1":
  version "5.2.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@envelop/types/-/types-5.2.1.tgz"
  integrity sha1-a8lxPyrqVtfePqOei7cANcBHWzY=
  dependencies:
    "@whatwg-node/promise-helpers" "^1.0.0"
    tslib "^2.5.0"

"@esbuild/darwin-arm64@0.25.5":
  version "0.25.5"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@esbuild/darwin-arm64/-/darwin-arm64-0.25.5.tgz"
  integrity sha1-Sdi/ix35X3WayB6x0HNgGABtfjQ=

"@eslint-community/eslint-utils@^4.2.0", "@eslint-community/eslint-utils@^4.7.0":
  version "4.7.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@eslint-community/eslint-utils/-/eslint-utils-4.7.0.tgz"
  integrity sha1-YHCEYwxsAzmSoILebm+8GotSF1o=
  dependencies:
    eslint-visitor-keys "^3.4.3"

"@eslint-community/regexpp@^4.10.0", "@eslint-community/regexpp@^4.12.1":
  version "4.12.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@eslint-community/regexpp/-/regexpp-4.12.1.tgz"
  integrity sha1-z8bP/jnfOQo4Qc3iq8z5Lqp64OA=

"@eslint/config-array@^0.21.0":
  version "0.21.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@eslint/config-array/-/config-array-0.21.0.tgz"
  integrity sha1-q9vL0WsSTGOAgXZjkqTWtQn3JjY=
  dependencies:
    "@eslint/object-schema" "^2.1.6"
    debug "^4.3.1"
    minimatch "^3.1.2"

"@eslint/config-helpers@^0.3.0":
  version "0.3.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@eslint/config-helpers/-/config-helpers-0.3.0.tgz"
  integrity sha1-PgmpDfuH4ABcdpR5HljpcHcnEoY=

"@eslint/core@^0.14.0":
  version "0.14.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@eslint/core/-/core-0.14.0.tgz"
  integrity sha1-MmKJOAlo6vfpbzZOHkz4863y0AM=
  dependencies:
    "@types/json-schema" "^7.0.15"

"@eslint/core@^0.15.1":
  version "0.15.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@eslint/core/-/core-0.15.1.tgz"
  integrity sha1-1TDUQgnL/i+C74bWugh2AZbdO2A=
  dependencies:
    "@types/json-schema" "^7.0.15"

"@eslint/eslintrc@^3.3.1":
  version "3.3.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@eslint/eslintrc/-/eslintrc-3.3.1.tgz"
  integrity sha1-5V9/HdQAYA3QZtu6NJxMC6yRaWQ=
  dependencies:
    ajv "^6.12.4"
    debug "^4.3.2"
    espree "^10.0.1"
    globals "^14.0.0"
    ignore "^5.2.0"
    import-fresh "^3.2.1"
    js-yaml "^4.1.0"
    minimatch "^3.1.2"
    strip-json-comments "^3.1.1"

"@eslint/js@^9.19.0", "@eslint/js@9.30.1":
  version "9.30.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@eslint/js/-/js-9.30.1.tgz"
  integrity sha1-6+ndUqODRXhMSGMAF1ooxgE8CI0=

"@eslint/object-schema@^2.1.6":
  version "2.1.6"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@eslint/object-schema/-/object-schema-2.1.6.tgz"
  integrity sha1-WDaatbWzyhF4gMD2wLDzL2lQ8k8=

"@eslint/plugin-kit@^0.3.1":
  version "0.3.3"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@eslint/plugin-kit/-/plugin-kit-0.3.3.tgz"
  integrity sha1-MpJrWb1AfVjYF5QeSLKnBJNZsf0=
  dependencies:
    "@eslint/core" "^0.15.1"
    levn "^0.4.1"

"@fastify/busboy@^3.1.1":
  version "3.1.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@fastify/busboy/-/busboy-3.1.1.tgz"
  integrity sha1-rzrqfx5S7JFti1ydzA8J1MBgo/w=

"@graphql-codegen/add@^5.0.3":
  version "5.0.3"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-codegen/add/-/add-5.0.3.tgz"
  integrity sha1-Ht5rrJqTZh7X+lgIsgPQeeGx0hU=
  dependencies:
    "@graphql-codegen/plugin-helpers" "^5.0.3"
    tslib "~2.6.0"

"@graphql-codegen/cli@5.0.2":
  version "5.0.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-codegen/cli/-/cli-5.0.2.tgz"
  integrity sha1-B/9pHBbaTD3MDhmV0yMVMDeasxc=
  dependencies:
    "@babel/generator" "^7.18.13"
    "@babel/template" "^7.18.10"
    "@babel/types" "^7.18.13"
    "@graphql-codegen/client-preset" "^4.2.2"
    "@graphql-codegen/core" "^4.0.2"
    "@graphql-codegen/plugin-helpers" "^5.0.3"
    "@graphql-tools/apollo-engine-loader" "^8.0.0"
    "@graphql-tools/code-file-loader" "^8.0.0"
    "@graphql-tools/git-loader" "^8.0.0"
    "@graphql-tools/github-loader" "^8.0.0"
    "@graphql-tools/graphql-file-loader" "^8.0.0"
    "@graphql-tools/json-file-loader" "^8.0.0"
    "@graphql-tools/load" "^8.0.0"
    "@graphql-tools/prisma-loader" "^8.0.0"
    "@graphql-tools/url-loader" "^8.0.0"
    "@graphql-tools/utils" "^10.0.0"
    "@whatwg-node/fetch" "^0.8.0"
    chalk "^4.1.0"
    cosmiconfig "^8.1.3"
    debounce "^1.2.0"
    detect-indent "^6.0.0"
    graphql-config "^5.0.2"
    inquirer "^8.0.0"
    is-glob "^4.0.1"
    jiti "^1.17.1"
    json-to-pretty-yaml "^1.2.2"
    listr2 "^4.0.5"
    log-symbols "^4.0.0"
    micromatch "^4.0.5"
    shell-quote "^1.7.3"
    string-env-interpolation "^1.0.1"
    ts-log "^2.2.3"
    tslib "^2.4.0"
    yaml "^2.3.1"
    yargs "^17.0.0"

"@graphql-codegen/client-preset@^4.2.2", "@graphql-codegen/client-preset@4.3.3":
  version "4.3.3"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-codegen/client-preset/-/client-preset-4.3.3.tgz"
  integrity sha1-O75d9cSajJdjz8kZZ5sV7u+/FSY=
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/template" "^7.20.7"
    "@graphql-codegen/add" "^5.0.3"
    "@graphql-codegen/gql-tag-operations" "4.0.9"
    "@graphql-codegen/plugin-helpers" "^5.0.4"
    "@graphql-codegen/typed-document-node" "^5.0.9"
    "@graphql-codegen/typescript" "^4.0.9"
    "@graphql-codegen/typescript-operations" "^4.2.3"
    "@graphql-codegen/visitor-plugin-common" "^5.3.1"
    "@graphql-tools/documents" "^1.0.0"
    "@graphql-tools/utils" "^10.0.0"
    "@graphql-typed-document-node/core" "3.2.0"
    tslib "~2.6.0"

"@graphql-codegen/core@^4.0.2":
  version "4.0.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-codegen/core/-/core-4.0.2.tgz"
  integrity sha1-fm7CZidvVLvwL2BZnZ5Rj0pZ2F4=
  dependencies:
    "@graphql-codegen/plugin-helpers" "^5.0.3"
    "@graphql-tools/schema" "^10.0.0"
    "@graphql-tools/utils" "^10.0.0"
    tslib "~2.6.0"

"@graphql-codegen/gql-tag-operations@4.0.9":
  version "4.0.9"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-codegen/gql-tag-operations/-/gql-tag-operations-4.0.9.tgz"
  integrity sha1-Jh7LwuldUlyqEsrS4A18JldVMuQ=
  dependencies:
    "@graphql-codegen/plugin-helpers" "^5.0.4"
    "@graphql-codegen/visitor-plugin-common" "5.3.1"
    "@graphql-tools/utils" "^10.0.0"
    auto-bind "~4.0.0"
    tslib "~2.6.0"

"@graphql-codegen/introspection@4.0.3":
  version "4.0.3"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-codegen/introspection/-/introspection-4.0.3.tgz"
  integrity sha1-dW45+0Up6hXTKhIvC86bCmZCU3k=
  dependencies:
    "@graphql-codegen/plugin-helpers" "^5.0.3"
    "@graphql-codegen/visitor-plugin-common" "^5.0.0"
    tslib "~2.6.0"

"@graphql-codegen/plugin-helpers@^5.0.3", "@graphql-codegen/plugin-helpers@^5.0.4", "@graphql-codegen/plugin-helpers@^5.1.0":
  version "5.1.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-codegen/plugin-helpers/-/plugin-helpers-5.1.1.tgz"
  integrity sha1-t8dEyIJjZ8MALDRhEt480bD5mxY=
  dependencies:
    "@graphql-tools/utils" "^10.0.0"
    change-case-all "1.0.15"
    common-tags "1.8.2"
    import-from "4.0.0"
    lodash "~4.17.0"
    tslib "~2.6.0"

"@graphql-codegen/schema-ast@^4.0.2":
  version "4.1.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-codegen/schema-ast/-/schema-ast-4.1.0.tgz"
  integrity sha1-oecfmTRklbknIWGp7Qd1boJkhyY=
  dependencies:
    "@graphql-codegen/plugin-helpers" "^5.0.3"
    "@graphql-tools/utils" "^10.0.0"
    tslib "~2.6.0"

"@graphql-codegen/typed-document-node@^5.0.9":
  version "5.1.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-codegen/typed-document-node/-/typed-document-node-5.1.2.tgz"
  integrity sha1-oaPaLZ/hhHgxrabkHgnFu4Hdazw=
  dependencies:
    "@graphql-codegen/plugin-helpers" "^5.1.0"
    "@graphql-codegen/visitor-plugin-common" "5.8.0"
    auto-bind "~4.0.0"
    change-case-all "1.0.15"
    tslib "~2.6.0"

"@graphql-codegen/typescript-operations@^4.2.3":
  version "4.6.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-codegen/typescript-operations/-/typescript-operations-4.6.1.tgz"
  integrity sha1-dj7aKMd/3e4rmum9e6rQUM//8KU=
  dependencies:
    "@graphql-codegen/plugin-helpers" "^5.1.0"
    "@graphql-codegen/typescript" "^4.1.6"
    "@graphql-codegen/visitor-plugin-common" "5.8.0"
    auto-bind "~4.0.0"
    tslib "~2.6.0"

"@graphql-codegen/typescript@^4.0.9", "@graphql-codegen/typescript@^4.1.6":
  version "4.1.6"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-codegen/typescript/-/typescript-4.1.6.tgz"
  integrity sha1-80gczBZW6WiS1ilnH7LP9dq8RYs=
  dependencies:
    "@graphql-codegen/plugin-helpers" "^5.1.0"
    "@graphql-codegen/schema-ast" "^4.0.2"
    "@graphql-codegen/visitor-plugin-common" "5.8.0"
    auto-bind "~4.0.0"
    tslib "~2.6.0"

"@graphql-codegen/visitor-plugin-common@^5.0.0", "@graphql-codegen/visitor-plugin-common@^5.3.1", "@graphql-codegen/visitor-plugin-common@5.8.0":
  version "5.8.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-codegen/visitor-plugin-common/-/visitor-plugin-common-5.8.0.tgz"
  integrity sha1-G3lkU+uW2OatXU06z/ME5GcngaA=
  dependencies:
    "@graphql-codegen/plugin-helpers" "^5.1.0"
    "@graphql-tools/optimize" "^2.0.0"
    "@graphql-tools/relay-operation-optimizer" "^7.0.0"
    "@graphql-tools/utils" "^10.0.0"
    auto-bind "~4.0.0"
    change-case-all "1.0.15"
    dependency-graph "^0.11.0"
    graphql-tag "^2.11.0"
    parse-filepath "^1.0.2"
    tslib "~2.6.0"

"@graphql-codegen/visitor-plugin-common@5.3.1":
  version "5.3.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-codegen/visitor-plugin-common/-/visitor-plugin-common-5.3.1.tgz"
  integrity sha1-0/tfYzbL71jilgRxQi2j88r/fxc=
  dependencies:
    "@graphql-codegen/plugin-helpers" "^5.0.4"
    "@graphql-tools/optimize" "^2.0.0"
    "@graphql-tools/relay-operation-optimizer" "^7.0.0"
    "@graphql-tools/utils" "^10.0.0"
    auto-bind "~4.0.0"
    change-case-all "1.0.15"
    dependency-graph "^0.11.0"
    graphql-tag "^2.11.0"
    parse-filepath "^1.0.2"
    tslib "~2.6.0"

"@graphql-hive/signal@^1.0.0":
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-hive/signal/-/signal-1.0.0.tgz"
  integrity sha1-biGTZgpHySWrrb5yKT38lDDiT48=

"@graphql-tools/apollo-engine-loader@^8.0.0":
  version "8.0.20"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/apollo-engine-loader/-/apollo-engine-loader-8.0.20.tgz"
  integrity sha1-27fSlKP78B7LBQBL1uNKAQKXGM0=
  dependencies:
    "@graphql-tools/utils" "^10.8.6"
    "@whatwg-node/fetch" "^0.10.0"
    sync-fetch "0.6.0-2"
    tslib "^2.4.0"

"@graphql-tools/batch-execute@^9.0.17":
  version "9.0.17"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/batch-execute/-/batch-execute-9.0.17.tgz"
  integrity sha1-wu8S5sKy4J9eYRbCCiIpv67NOZw=
  dependencies:
    "@graphql-tools/utils" "^10.8.1"
    "@whatwg-node/promise-helpers" "^1.3.0"
    dataloader "^2.2.3"
    tslib "^2.8.1"

"@graphql-tools/code-file-loader@^8.0.0":
  version "8.1.20"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/code-file-loader/-/code-file-loader-8.1.20.tgz"
  integrity sha1-g/GvewUeDiqR2fZBLjaKU26ruGw=
  dependencies:
    "@graphql-tools/graphql-tag-pluck" "8.3.19"
    "@graphql-tools/utils" "^10.8.6"
    globby "^11.0.3"
    tslib "^2.4.0"
    unixify "^1.0.0"

"@graphql-tools/delegate@^10.2.20":
  version "10.2.20"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/delegate/-/delegate-10.2.20.tgz"
  integrity sha1-ECcN5fYcxKfFekMEfJr1Q3aq11w=
  dependencies:
    "@graphql-tools/batch-execute" "^9.0.17"
    "@graphql-tools/executor" "^1.4.7"
    "@graphql-tools/schema" "^10.0.11"
    "@graphql-tools/utils" "^10.8.1"
    "@repeaterjs/repeater" "^3.0.6"
    "@whatwg-node/promise-helpers" "^1.3.0"
    dataloader "^2.2.3"
    dset "^3.1.2"
    tslib "^2.8.1"

"@graphql-tools/documents@^1.0.0":
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/documents/-/documents-1.0.1.tgz"
  integrity sha1-rhnNVmfSLCOzMdOhQpRD7XEw+u4=
  dependencies:
    lodash.sortby "^4.7.0"
    tslib "^2.4.0"

"@graphql-tools/executor-common@^0.0.4":
  version "0.0.4"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/executor-common/-/executor-common-0.0.4.tgz"
  integrity sha1-djYDpteiK7CdZ85oLoSg1zD/K/k=
  dependencies:
    "@envelop/core" "^5.2.3"
    "@graphql-tools/utils" "^10.8.1"

"@graphql-tools/executor-graphql-ws@^2.0.1":
  version "2.0.5"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/executor-graphql-ws/-/executor-graphql-ws-2.0.5.tgz"
  integrity sha1-Tlj8lsLwgDOO5bmvrujV4maB1zI=
  dependencies:
    "@graphql-tools/executor-common" "^0.0.4"
    "@graphql-tools/utils" "^10.8.1"
    "@whatwg-node/disposablestack" "^0.0.6"
    graphql-ws "^6.0.3"
    isomorphic-ws "^5.0.0"
    tslib "^2.8.1"
    ws "^8.17.1"

"@graphql-tools/executor-http@^1.1.9":
  version "1.3.3"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/executor-http/-/executor-http-1.3.3.tgz"
  integrity sha1-BX3nnryQ7b0kIlnMyxJo037mxXk=
  dependencies:
    "@graphql-hive/signal" "^1.0.0"
    "@graphql-tools/executor-common" "^0.0.4"
    "@graphql-tools/utils" "^10.8.1"
    "@repeaterjs/repeater" "^3.0.4"
    "@whatwg-node/disposablestack" "^0.0.6"
    "@whatwg-node/fetch" "^0.10.4"
    "@whatwg-node/promise-helpers" "^1.3.0"
    meros "^1.2.1"
    tslib "^2.8.1"

"@graphql-tools/executor-legacy-ws@^1.1.17":
  version "1.1.17"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/executor-legacy-ws/-/executor-legacy-ws-1.1.17.tgz"
  integrity sha1-Vc82mGb/8LJolj2vlAQbyGIbs0U=
  dependencies:
    "@graphql-tools/utils" "^10.8.6"
    "@types/ws" "^8.0.0"
    isomorphic-ws "^5.0.0"
    tslib "^2.4.0"
    ws "^8.17.1"

"@graphql-tools/executor@^1.4.7":
  version "1.4.7"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/executor/-/executor-1.4.7.tgz"
  integrity sha1-hr8LJvKt1baG7JboZu4i0bgfm2s=
  dependencies:
    "@graphql-tools/utils" "^10.8.6"
    "@graphql-typed-document-node/core" "^3.2.0"
    "@repeaterjs/repeater" "^3.0.4"
    "@whatwg-node/disposablestack" "^0.0.6"
    "@whatwg-node/promise-helpers" "^1.0.0"
    tslib "^2.4.0"

"@graphql-tools/git-loader@^8.0.0":
  version "8.0.24"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/git-loader/-/git-loader-8.0.24.tgz"
  integrity sha1-ztaG6CZMo2rVELGWeAQjiqTwSFg=
  dependencies:
    "@graphql-tools/graphql-tag-pluck" "8.3.19"
    "@graphql-tools/utils" "^10.8.6"
    is-glob "4.0.3"
    micromatch "^4.0.8"
    tslib "^2.4.0"
    unixify "^1.0.0"

"@graphql-tools/github-loader@^8.0.0":
  version "8.0.20"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/github-loader/-/github-loader-8.0.20.tgz"
  integrity sha1-dpcXgoxH91H1waJbROwnDNzSIr4=
  dependencies:
    "@graphql-tools/executor-http" "^1.1.9"
    "@graphql-tools/graphql-tag-pluck" "^8.3.19"
    "@graphql-tools/utils" "^10.8.6"
    "@whatwg-node/fetch" "^0.10.0"
    "@whatwg-node/promise-helpers" "^1.0.0"
    sync-fetch "0.6.0-2"
    tslib "^2.4.0"

"@graphql-tools/graphql-file-loader@^8.0.0":
  version "8.0.20"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/graphql-file-loader/-/graphql-file-loader-8.0.20.tgz"
  integrity sha1-2m6ymXDBkb1bFkMvFfkZJHjEq6s=
  dependencies:
    "@graphql-tools/import" "7.0.19"
    "@graphql-tools/utils" "^10.8.6"
    globby "^11.0.3"
    tslib "^2.4.0"
    unixify "^1.0.0"

"@graphql-tools/graphql-tag-pluck@^8.3.19", "@graphql-tools/graphql-tag-pluck@8.3.19":
  version "8.3.19"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/graphql-tag-pluck/-/graphql-tag-pluck-8.3.19.tgz"
  integrity sha1-pk2hSXQuY3F7QJADrC8fWWYF5Sk=
  dependencies:
    "@babel/core" "^7.26.10"
    "@babel/parser" "^7.26.10"
    "@babel/plugin-syntax-import-assertions" "^7.26.0"
    "@babel/traverse" "^7.26.10"
    "@babel/types" "^7.26.10"
    "@graphql-tools/utils" "^10.8.6"
    tslib "^2.4.0"

"@graphql-tools/import@7.0.19":
  version "7.0.19"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/import/-/import-7.0.19.tgz"
  integrity sha1-TYo9xegwJz1i5HKk8NwCFwxZvuo=
  dependencies:
    "@graphql-tools/utils" "^10.8.6"
    resolve-from "5.0.0"
    tslib "^2.4.0"

"@graphql-tools/json-file-loader@^8.0.0":
  version "8.0.18"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/json-file-loader/-/json-file-loader-8.0.18.tgz"
  integrity sha1-+JQ4wUY9t1+X9BnBe+I02VJb710=
  dependencies:
    "@graphql-tools/utils" "^10.8.6"
    globby "^11.0.3"
    tslib "^2.4.0"
    unixify "^1.0.0"

"@graphql-tools/load@^8.0.0", "@graphql-tools/load@^8.1.0":
  version "8.1.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/load/-/load-8.1.0.tgz"
  integrity sha1-SqA/BxqHd+MUsQKJt9YJfaqvh4M=
  dependencies:
    "@graphql-tools/schema" "^10.0.23"
    "@graphql-tools/utils" "^10.8.6"
    p-limit "3.1.0"
    tslib "^2.4.0"

"@graphql-tools/merge@^9.0.0", "@graphql-tools/merge@^9.0.24":
  version "9.0.24"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/merge/-/merge-9.0.24.tgz"
  integrity sha1-HzZuhViIlMtJa9HDMr52ZdsUPfI=
  dependencies:
    "@graphql-tools/utils" "^10.8.6"
    tslib "^2.4.0"

"@graphql-tools/optimize@^2.0.0":
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/optimize/-/optimize-2.0.0.tgz"
  integrity sha1-epd50YCCRREkilDFokHv9uei2QY=
  dependencies:
    tslib "^2.4.0"

"@graphql-tools/prisma-loader@^8.0.0":
  version "8.0.17"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/prisma-loader/-/prisma-loader-8.0.17.tgz"
  integrity sha1-vH7921efuFgAVA3TUoQ4Dp34UaI=
  dependencies:
    "@graphql-tools/url-loader" "^8.0.15"
    "@graphql-tools/utils" "^10.5.6"
    "@types/js-yaml" "^4.0.0"
    "@whatwg-node/fetch" "^0.10.0"
    chalk "^4.1.0"
    debug "^4.3.1"
    dotenv "^16.0.0"
    graphql-request "^6.0.0"
    http-proxy-agent "^7.0.0"
    https-proxy-agent "^7.0.0"
    jose "^5.0.0"
    js-yaml "^4.0.0"
    lodash "^4.17.20"
    scuid "^1.1.0"
    tslib "^2.4.0"
    yaml-ast-parser "^0.0.43"

"@graphql-tools/relay-operation-optimizer@^7.0.0":
  version "7.0.19"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/relay-operation-optimizer/-/relay-operation-optimizer-7.0.19.tgz"
  integrity sha1-8EvQmYd9xHC57UxlpTdaGj4jNfE=
  dependencies:
    "@ardatan/relay-compiler" "^12.0.3"
    "@graphql-tools/utils" "^10.8.6"
    tslib "^2.4.0"

"@graphql-tools/schema@^10.0.0", "@graphql-tools/schema@^10.0.11", "@graphql-tools/schema@^10.0.23":
  version "10.0.23"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/schema/-/schema-10.0.23.tgz"
  integrity sha1-2IZelvN6BMpDMD1wJK3X7b4MXtQ=
  dependencies:
    "@graphql-tools/merge" "^9.0.24"
    "@graphql-tools/utils" "^10.8.6"
    tslib "^2.4.0"

"@graphql-tools/url-loader@^8.0.0", "@graphql-tools/url-loader@^8.0.15":
  version "8.0.31"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/url-loader/-/url-loader-8.0.31.tgz"
  integrity sha1-dc02uLRch5k7TdIUmoP6mtPNKmc=
  dependencies:
    "@graphql-tools/executor-graphql-ws" "^2.0.1"
    "@graphql-tools/executor-http" "^1.1.9"
    "@graphql-tools/executor-legacy-ws" "^1.1.17"
    "@graphql-tools/utils" "^10.8.6"
    "@graphql-tools/wrap" "^10.0.16"
    "@types/ws" "^8.0.0"
    "@whatwg-node/fetch" "^0.10.0"
    "@whatwg-node/promise-helpers" "^1.0.0"
    isomorphic-ws "^5.0.0"
    sync-fetch "0.6.0-2"
    tslib "^2.4.0"
    ws "^8.17.1"

"@graphql-tools/utils@^10.0.0", "@graphql-tools/utils@^10.5.6", "@graphql-tools/utils@^10.8.1", "@graphql-tools/utils@^10.8.6":
  version "10.8.6"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/utils/-/utils-10.8.6.tgz"
  integrity sha1-ae8p5AiieRkQiysiJ/6LRlrPnlw=
  dependencies:
    "@graphql-typed-document-node/core" "^3.1.1"
    "@whatwg-node/promise-helpers" "^1.0.0"
    cross-inspect "1.0.1"
    dset "^3.1.4"
    tslib "^2.4.0"

"@graphql-tools/wrap@^10.0.16":
  version "10.1.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-tools/wrap/-/wrap-10.1.1.tgz"
  integrity sha1-1ubacBZv6QtWO+ObUvxBZVUoeAg=
  dependencies:
    "@graphql-tools/delegate" "^10.2.20"
    "@graphql-tools/schema" "^10.0.11"
    "@graphql-tools/utils" "^10.8.1"
    "@whatwg-node/promise-helpers" "^1.3.0"
    tslib "^2.8.1"

"@graphql-typed-document-node/core@^3.1.1", "@graphql-typed-document-node/core@^3.2.0", "@graphql-typed-document-node/core@3.2.0":
  version "3.2.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@graphql-typed-document-node/core/-/core-3.2.0.tgz"
  integrity sha1-Xz2W7GsjVK1tiii/IWodl7VCaGE=

"@humanfs/core@^0.19.1":
  version "0.19.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@humanfs/core/-/core-0.19.1.tgz"
  integrity sha1-F8Vcp9Qmcz/jxWGQa4Fzwza0Cnc=

"@humanfs/node@^0.16.6":
  version "0.16.6"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@humanfs/node/-/node-0.16.6.tgz"
  integrity sha1-7ioQ6qvRExmHvwSI/ZuCAXTNdl4=
  dependencies:
    "@humanfs/core" "^0.19.1"
    "@humanwhocodes/retry" "^0.3.0"

"@humanwhocodes/module-importer@^1.0.1":
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz"
  integrity sha1-r1smkaIrRL6EewyoFkHF+2rQFyw=

"@humanwhocodes/retry@^0.3.0":
  version "0.3.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@humanwhocodes/retry/-/retry-0.3.1.tgz"
  integrity sha1-xypcdqn7rzSI4jGxPcUsDae6tCo=

"@humanwhocodes/retry@^0.4.2":
  version "0.4.3"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@humanwhocodes/retry/-/retry-0.4.3.tgz"
  integrity sha1-wrnS43TuYsWG062+qHGZsdenpro=

"@jridgewell/gen-mapping@^0.3.12", "@jridgewell/gen-mapping@^0.3.5":
  version "0.3.12"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@jridgewell/gen-mapping/-/gen-mapping-0.3.12.tgz"
  integrity sha1-IjTOJsYoifA9s9f+pDwZMqs+kns=
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.5.0"
    "@jridgewell/trace-mapping" "^0.3.24"

"@jridgewell/resolve-uri@^3.1.0":
  version "3.1.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz"
  integrity sha1-eg7mAfYPmaIMfHxf8MgDiMEYm9Y=

"@jridgewell/sourcemap-codec@^1.4.14", "@jridgewell/sourcemap-codec@^1.5.0":
  version "1.5.4"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.4.tgz"
  integrity sha1-c1gENDOy5dpWmqAsvEwSHaOvJ9c=

"@jridgewell/trace-mapping@^0.3.24", "@jridgewell/trace-mapping@^0.3.28":
  version "0.3.29"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@jridgewell/trace-mapping/-/trace-mapping-0.3.29.tgz"
  integrity sha1-pY0x6q2vksZpVoCy4dRkqbj79/w=
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

<<<<<<< Updated upstream
"@mui/core-downloads-tracker@^7.2.0":
  version "7.2.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@mui/core-downloads-tracker/-/core-downloads-tracker-7.2.0.tgz#af74715885a7686cbf50806ad36691e710b10dbc"
  integrity sha1-r3RxWIWnaGy/UIBq02aR5xCxDbw=

"@mui/icons-material@^7.2.0":
  version "7.2.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@mui/icons-material/-/icons-material-7.2.0.tgz#e01de90ecf3cdccee7f8e88e7e033df5e3f050e1"
  integrity sha1-4B3pDs883M7n+OiOfgM99ePwUOE=
=======
"@mui/core-downloads-tracker@^6.4.12":
  version "6.4.12"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@mui/core-downloads-tracker/-/core-downloads-tracker-6.4.12.tgz"
  integrity sha1-5TvFWGQdAvNZ6fQRKi7xn/1NtlI=

"@mui/icons-material@^6.4.5":
  version "6.4.12"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@mui/icons-material/-/icons-material-6.4.12.tgz"
  integrity sha1-6ONc0IqHq+cMHHYMSbTSbCEVRSc=
>>>>>>> Stashed changes
  dependencies:
    "@babel/runtime" "^7.27.6"

<<<<<<< Updated upstream
"@mui/material@^7.2.0":
  version "7.2.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@mui/material/-/material-7.2.0.tgz#957da3b927c67e5a36a026658ffb40c10a3b6b5f"
  integrity sha1-lX2juSfGflo2oCZlj/tAwQo7a18=
=======
"@mui/material@^5.15.14 || ^6.0.0 || ^7.0.0", "@mui/material@^6.4.12", "@mui/material@^6.4.5":
  version "6.4.12"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@mui/material/-/material-6.4.12.tgz"
  integrity sha1-EbmGaB+wsKZtP6VrbHlMK9HbNis=
>>>>>>> Stashed changes
  dependencies:
    "@babel/runtime" "^7.27.6"
    "@mui/core-downloads-tracker" "^7.2.0"
    "@mui/system" "^7.2.0"
    "@mui/types" "^7.4.4"
    "@mui/utils" "^7.2.0"
    "@popperjs/core" "^2.11.8"
    "@types/react-transition-group" "^4.4.12"
    clsx "^2.1.1"
    csstype "^3.1.3"
    prop-types "^15.8.1"
    react-is "^19.1.0"
    react-transition-group "^4.4.5"

<<<<<<< Updated upstream
"@mui/private-theming@^7.2.0":
  version "7.2.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@mui/private-theming/-/private-theming-7.2.0.tgz#8d601e0949c81598da4621559181f1ac8231efc5"
  integrity sha1-jWAeCUnIFZjaRiFVkYHxrIIx78U=
=======
"@mui/private-theming@^6.4.9":
  version "6.4.9"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@mui/private-theming/-/private-theming-6.4.9.tgz"
  integrity sha1-DB1lpjihdAqtDrcV1552Rxq+gXU=
>>>>>>> Stashed changes
  dependencies:
    "@babel/runtime" "^7.27.6"
    "@mui/utils" "^7.2.0"
    prop-types "^15.8.1"

<<<<<<< Updated upstream
"@mui/styled-engine@^7.2.0":
  version "7.2.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@mui/styled-engine/-/styled-engine-7.2.0.tgz#98bf5abe1f80adabd66d4f9c13ea9e4a2616908e"
  integrity sha1-mL9avh+AravWbU+cE+qeSiYWkI4=
=======
"@mui/private-theming@^7.2.0":
  version "7.2.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@mui/private-theming/-/private-theming-7.2.0.tgz"
  integrity sha1-jWAeCUnIFZjaRiFVkYHxrIIx78U=
  dependencies:
    "@babel/runtime" "^7.27.6"
    "@mui/utils" "^7.2.0"
    prop-types "^15.8.1"

"@mui/styled-engine@^6.4.11":
  version "6.4.11"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@mui/styled-engine/-/styled-engine-6.4.11.tgz"
  integrity sha1-pI9IFlOClDAY9wUZ3h0x4DarsFQ=
>>>>>>> Stashed changes
  dependencies:
    "@babel/runtime" "^7.27.6"
    "@emotion/cache" "^11.14.0"
    "@emotion/serialize" "^1.3.3"
    "@emotion/sheet" "^1.4.0"
    csstype "^3.1.3"
    prop-types "^15.8.1"

<<<<<<< Updated upstream
"@mui/system@^7.2.0":
  version "7.2.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@mui/system/-/system-7.2.0.tgz#b13f99cb5937912228f29175d6ea67857d626d70"
  integrity sha1-sT+Zy1k3kSIo8pF11upnhX1ibXA=
=======
"@mui/styled-engine@^7.2.0":
  version "7.2.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@mui/styled-engine/-/styled-engine-7.2.0.tgz"
  integrity sha1-mL9avh+AravWbU+cE+qeSiYWkI4=
  dependencies:
    "@babel/runtime" "^7.27.6"
    "@emotion/cache" "^11.14.0"
    "@emotion/serialize" "^1.3.3"
    "@emotion/sheet" "^1.4.0"
    csstype "^3.1.3"
    prop-types "^15.8.1"

"@mui/system@^5.15.14 || ^6.0.0 || ^7.0.0":
  version "7.2.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@mui/system/-/system-7.2.0.tgz"
  integrity sha1-sT+Zy1k3kSIo8pF11upnhX1ibXA=
  dependencies:
    "@babel/runtime" "^7.27.6"
    "@mui/private-theming" "^7.2.0"
    "@mui/styled-engine" "^7.2.0"
    "@mui/types" "^7.4.4"
    "@mui/utils" "^7.2.0"
    clsx "^2.1.1"
    csstype "^3.1.3"
    prop-types "^15.8.1"

"@mui/system@^6.4.12":
  version "6.4.12"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@mui/system/-/system-6.4.12.tgz"
  integrity sha1-TowE5ZLFn0pCjGXHrrNuK6ZZ3N8=
>>>>>>> Stashed changes
  dependencies:
    "@babel/runtime" "^7.27.6"
    "@mui/private-theming" "^7.2.0"
    "@mui/styled-engine" "^7.2.0"
    "@mui/types" "^7.4.4"
    "@mui/utils" "^7.2.0"
    clsx "^2.1.1"
    csstype "^3.1.3"
    prop-types "^15.8.1"

"@mui/types@^7.4.4":
  version "7.4.4"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@mui/types/-/types-7.4.4.tgz"
  integrity sha1-DFzVaQUjHicJa0HQlvHJSMJr3V0=
  dependencies:
    "@babel/runtime" "^7.27.6"

<<<<<<< Updated upstream
"@mui/utils@^5.16.6 || ^6.0.0 || ^7.0.0", "@mui/utils@^7.2.0":
  version "7.2.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@mui/utils/-/utils-7.2.0.tgz#31062697b41aa8ea8ef04e3d3fadca1dec3e1de1"
=======
"@mui/types@~7.2.24":
  version "7.2.24"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@mui/types/-/types-7.2.24.tgz"
  integrity sha1-Xv9jEp2cKdgLvy0uVhvQaQMU3sI=

"@mui/utils@^5.16.6 || ^6.0.0 || ^7.0.0", "@mui/utils@^6.4.9":
  version "6.4.9"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@mui/utils/-/utils-6.4.9.tgz"
  integrity sha1-sN8B2qJUx8MqGjCzClF54Z7wcac=
  dependencies:
    "@babel/runtime" "^7.26.0"
    "@mui/types" "~7.2.24"
    "@types/prop-types" "^15.7.14"
    clsx "^2.1.1"
    prop-types "^15.8.1"
    react-is "^19.0.0"

"@mui/utils@^7.2.0":
  version "7.2.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@mui/utils/-/utils-7.2.0.tgz"
>>>>>>> Stashed changes
  integrity sha1-MQYml7QaqOqO8E49P63KHew+HeE=
  dependencies:
    "@babel/runtime" "^7.27.6"
    "@mui/types" "^7.4.4"
    "@types/prop-types" "^15.7.15"
    clsx "^2.1.1"
    prop-types "^15.8.1"
    react-is "^19.1.0"

"@mui/x-data-grid@^7.27.0":
  version "7.29.8"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@mui/x-data-grid/-/x-data-grid-7.29.8.tgz"
  integrity sha1-6jd2wCxqpEzmsYrvKSQnCppNfmY=
  dependencies:
    "@babel/runtime" "^7.25.7"
    "@mui/utils" "^5.16.6 || ^6.0.0 || ^7.0.0"
    "@mui/x-internals" "7.29.0"
    clsx "^2.1.1"
    prop-types "^15.8.1"
    reselect "^5.1.1"
    use-sync-external-store "^1.0.0"

"@mui/x-date-pickers@^7.27.0":
  version "7.29.4"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@mui/x-date-pickers/-/x-date-pickers-7.29.4.tgz"
  integrity sha1-uICMuOKMHU5SizezNu/8gHTmX68=
  dependencies:
    "@babel/runtime" "^7.25.7"
    "@mui/utils" "^5.16.6 || ^6.0.0 || ^7.0.0"
    "@mui/x-internals" "7.29.0"
    "@types/react-transition-group" "^4.4.11"
    clsx "^2.1.1"
    prop-types "^15.8.1"
    react-transition-group "^4.4.5"

"@mui/x-internals@7.29.0":
  version "7.29.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@mui/x-internals/-/x-internals-7.29.0.tgz"
  integrity sha1-HzU7aX7Rv1WUrFSVVq3i5oQfS/U=
  dependencies:
    "@babel/runtime" "^7.25.7"
    "@mui/utils" "^5.16.6 || ^6.0.0 || ^7.0.0"

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz"
  integrity sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U=
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@^2.0.2", "@nodelib/fs.stat@2.0.5":
  version "2.0.5"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz"
  integrity sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos=

"@nodelib/fs.walk@^1.2.3":
  version "1.2.8"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz"
  integrity sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po=
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@peculiar/asn1-schema@^2.3.13", "@peculiar/asn1-schema@^2.3.8":
  version "2.3.15"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@peculiar/asn1-schema/-/asn1-schema-2.3.15.tgz"
  integrity sha1-6Sa/3u1RlFoG84vnA0mefYNBpdM=
  dependencies:
    asn1js "^3.0.5"
    pvtsutils "^1.3.6"
    tslib "^2.8.1"

"@peculiar/json-schema@^1.1.12":
  version "1.1.12"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@peculiar/json-schema/-/json-schema-1.1.12.tgz"
  integrity sha1-/mHoUlnjtbpa1WbLYsp1s9PNUzk=
  dependencies:
    tslib "^2.0.0"

"@peculiar/webcrypto@^1.4.0":
  version "1.5.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@peculiar/webcrypto/-/webcrypto-1.5.0.tgz"
  integrity sha1-nlcXTALBKRBRxVNgA0fhK4FGnhA=
  dependencies:
    "@peculiar/asn1-schema" "^2.3.8"
    "@peculiar/json-schema" "^1.1.12"
    pvtsutils "^1.3.5"
    tslib "^2.6.2"
    webcrypto-core "^1.8.0"

"@pkgr/core@^0.2.4":
  version "0.2.7"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@pkgr/core/-/core-0.2.7.tgz"
  integrity sha1-61AU39CwPn87ou7v9Qbu2JsCgFg=

"@popperjs/core@^2.11.8":
  version "2.11.8"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@popperjs/core/-/core-2.11.8.tgz"
  integrity sha1-a3kDLnYKCJnNQgRxC+7elyo6GF8=

"@repeaterjs/repeater@^3.0.4", "@repeaterjs/repeater@^3.0.6":
  version "3.0.6"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@repeaterjs/repeater/-/repeater-3.0.6.tgz"
  integrity sha1-viPfAUPO7Dxp+LbCUXlxpVeP2qI=

"@rolldown/pluginutils@1.0.0-beta.19":
  version "1.0.0-beta.19"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.19.tgz"
  integrity sha1-/DuVFFqOejv5J1QmnY5PQO6ookQ=

"@rollup/rollup-darwin-arm64@4.44.2":
  version "4.44.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.44.2.tgz"
  integrity sha1-4hbDM+RIxnlzOG5G2/6OOBqvsFU=

"@types/babel__core@^7.20.5":
  version "7.20.5"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/babel__core/-/babel__core-7.20.5.tgz"
  integrity sha1-PfFfJ7qFMZyqB7oI0HIYibs5wBc=
  dependencies:
    "@babel/parser" "^7.20.7"
    "@babel/types" "^7.20.7"
    "@types/babel__generator" "*"
    "@types/babel__template" "*"
    "@types/babel__traverse" "*"

"@types/babel__generator@*":
  version "7.27.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/babel__generator/-/babel__generator-7.27.0.tgz"
  integrity sha1-tYGSlMUReZV6+uw0FEL5NB5BCKk=
  dependencies:
    "@babel/types" "^7.0.0"

"@types/babel__template@*":
  version "7.4.4"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/babel__template/-/babel__template-7.4.4.tgz"
  integrity sha1-VnJRNwHBshmbxtrWNqnXSRWGdm8=
  dependencies:
    "@babel/parser" "^7.1.0"
    "@babel/types" "^7.0.0"

"@types/babel__traverse@*":
  version "7.20.7"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/babel__traverse/-/babel__traverse-7.20.7.tgz"
  integrity sha1-lozcI2bsPaFZ9hFmQo7kDzcOVsI=
  dependencies:
    "@babel/types" "^7.20.7"

"@types/d3-array@^3.0.3":
  version "3.2.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/d3-array/-/d3-array-3.2.1.tgz"
  integrity sha1-H2ZY49IAbE/OrFP95GQWaFn4uMU=

"@types/d3-color@*":
  version "3.1.3"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/d3-color/-/d3-color-3.1.3.tgz"
  integrity sha1-NoyWGhjech2oIA6AvzlD+1MTavI=

"@types/d3-ease@^3.0.0":
  version "3.0.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/d3-ease/-/d3-ease-3.0.2.tgz"
  integrity sha1-4o2xv7+mFwdvd3DdHZpI6qO2xRs=

"@types/d3-interpolate@^3.0.1":
  version "3.0.4"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/d3-interpolate/-/d3-interpolate-3.0.4.tgz"
  integrity sha1-QSuQ6EhwKF8v+KhGxutgNE8SpBw=
  dependencies:
    "@types/d3-color" "*"

"@types/d3-path@*":
  version "3.1.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/d3-path/-/d3-path-3.1.1.tgz"
  integrity sha1-9jKzgMOsoduo40qgSbzWpK8j34o=

"@types/d3-scale@^4.0.2":
  version "4.0.9"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/d3-scale/-/d3-scale-4.0.9.tgz"
  integrity sha1-V6L3ByQub+Hega17/Myq9gYXmvs=
  dependencies:
    "@types/d3-time" "*"

"@types/d3-shape@^3.1.0":
  version "3.1.7"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/d3-shape/-/d3-shape-3.1.7.tgz"
  integrity sha1-K3tCPcLf5pyMk1luZz43RDNIxVU=
  dependencies:
    "@types/d3-path" "*"

"@types/d3-time@*", "@types/d3-time@^3.0.0":
  version "3.0.4"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/d3-time/-/d3-time-3.0.4.tgz"
  integrity sha1-hHL+7NY5aRRQ3YAA6zPt1EThMj8=

"@types/d3-timer@^3.0.0":
  version "3.0.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/d3-timer/-/d3-timer-3.0.2.tgz"
  integrity sha1-cLvad9wjqnJ0E+IuIUr6Pw6FL3A=

"@types/estree@^1.0.6", "@types/estree@1.0.8":
  version "1.0.8"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/estree/-/estree-1.0.8.tgz"
  integrity sha1-lYuRyZGxhnztMYvt6g4hXuBQcm4=

"@types/file-saver@^2.0.7":
  version "2.0.7"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/file-saver/-/file-saver-2.0.7.tgz"
  integrity sha1-jbsvJL3HSGxUqoVOtBSUC70Fb30=

"@types/js-yaml@^4.0.0":
  version "4.0.9"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/js-yaml/-/js-yaml-4.0.9.tgz"
  integrity sha1-zYI4LE+QL+2WkaLteexoxYmK9MI=

"@types/json-schema@^7.0.15":
  version "7.0.15"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/json-schema/-/json-schema-7.0.15.tgz"
  integrity sha1-WWoXRyM2lNUPatinhp/Lb1bPWEE=

"@types/lodash@^4.17.16":
  version "4.17.20"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/lodash/-/lodash-4.17.20.tgz"
  integrity sha1-HKdzYdc2NDLSn15VlQ2eweHG6pM=

"@types/node@*", "@types/node@^18.0.0 || ^20.0.0 || >=22.0.0", "@types/node@^22.13.4", "@types/node@>=13":
  version "22.16.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/node/-/node-22.16.0.tgz"
  integrity sha1-NSvElR/Qid8y8rZBKmHTObZ97Ys=
  dependencies:
    undici-types "~6.21.0"

"@types/parse-json@^4.0.0":
  version "4.0.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/parse-json/-/parse-json-4.0.2.tgz"
  integrity sha1-WVDlCWB5MFWEXpVsQn/CsNcMUjk=

"@types/prop-types@^15.7.15":
  version "15.7.15"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/prop-types/-/prop-types-15.7.15.tgz"
  integrity sha1-5uWobWAr6spxzlFj+t9fldcJMcc=

"@types/react-dom@^19.0.3":
  version "19.1.6"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/react-dom/-/react-dom-19.1.6.tgz"
  integrity sha1-SvYp2g6fnA9Qb8TRyqYQOZxZXWQ=

"@types/react-transition-group@^4.4.11", "@types/react-transition-group@^4.4.12":
  version "4.4.12"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/react-transition-group/-/react-transition-group-4.4.12.tgz"
  integrity sha1-tddlaEhbAqMHI4Jwv+lstR7ioEQ=

"@types/react@*", "@types/react@^17.0.0 || ^18.0.0 || ^19.0.0", "@types/react@^19.0.0", "@types/react@^19.0.8", "@types/react@>=18.0.0":
  version "19.1.8"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/react/-/react-19.1.8.tgz"
  integrity sha1-/4OV8q+3ZFlyZc7RX43dsHIK4cM=
  dependencies:
    csstype "^3.0.2"

"@types/ws@^8.0.0":
  version "8.18.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@types/ws/-/ws-8.18.1.tgz"
  integrity sha1-SEZOS/Ld/RfbE9hFRn9gcP/qSqk=
  dependencies:
    "@types/node" "*"

"@typescript-eslint/eslint-plugin@8.35.1":
  version "8.35.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@typescript-eslint/eslint-plugin/-/eslint-plugin-8.35.1.tgz"
  integrity sha1-BrESn+JtZTKr1Y+ys/6YEL0BaTU=
  dependencies:
    "@eslint-community/regexpp" "^4.10.0"
    "@typescript-eslint/scope-manager" "8.35.1"
    "@typescript-eslint/type-utils" "8.35.1"
    "@typescript-eslint/utils" "8.35.1"
    "@typescript-eslint/visitor-keys" "8.35.1"
    graphemer "^1.4.0"
    ignore "^7.0.0"
    natural-compare "^1.4.0"
    ts-api-utils "^2.1.0"

"@typescript-eslint/parser@^8.35.1", "@typescript-eslint/parser@8.35.1":
  version "8.35.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@typescript-eslint/parser/-/parser-8.35.1.tgz"
  integrity sha1-eHMS6A8PM32F9MKlaUEcRp6FLUQ=
  dependencies:
    "@typescript-eslint/scope-manager" "8.35.1"
    "@typescript-eslint/types" "8.35.1"
    "@typescript-eslint/typescript-estree" "8.35.1"
    "@typescript-eslint/visitor-keys" "8.35.1"
    debug "^4.3.4"

"@typescript-eslint/project-service@8.35.1":
  version "8.35.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@typescript-eslint/project-service/-/project-service-8.35.1.tgz"
  integrity sha1-gVu3cfL2yXeA5EKZQ07OPC5SYSc=
  dependencies:
    "@typescript-eslint/tsconfig-utils" "^8.35.1"
    "@typescript-eslint/types" "^8.35.1"
    debug "^4.3.4"

"@typescript-eslint/scope-manager@8.35.1":
  version "8.35.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@typescript-eslint/scope-manager/-/scope-manager-8.35.1.tgz"
  integrity sha1-sZ+b5lyNEFnoijI6GmVn2/4KGk4=
  dependencies:
    "@typescript-eslint/types" "8.35.1"
    "@typescript-eslint/visitor-keys" "8.35.1"

"@typescript-eslint/tsconfig-utils@^8.35.1", "@typescript-eslint/tsconfig-utils@8.35.1":
  version "8.35.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.35.1.tgz"
  integrity sha1-wtuHFMGBzAcAIWwaLjz1VxnFgAY=

"@typescript-eslint/type-utils@8.35.1":
  version "8.35.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@typescript-eslint/type-utils/-/type-utils-8.35.1.tgz"
  integrity sha1-T5oH1u+g5hemfhiQ0oEX5ozhVL0=
  dependencies:
    "@typescript-eslint/typescript-estree" "8.35.1"
    "@typescript-eslint/utils" "8.35.1"
    debug "^4.3.4"
    ts-api-utils "^2.1.0"

"@typescript-eslint/types@^8.35.1", "@typescript-eslint/types@8.35.1":
  version "8.35.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@typescript-eslint/types/-/types-8.35.1.tgz"
  integrity sha1-Q0Tc+TRJW78lqfg6Bt2f4qzxV4A=

"@typescript-eslint/typescript-estree@8.35.1":
  version "8.35.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@typescript-eslint/typescript-estree/-/typescript-estree-8.35.1.tgz"
  integrity sha1-uA6F/La/vLrLMiSxNn9so/A+YYM=
  dependencies:
    "@typescript-eslint/project-service" "8.35.1"
    "@typescript-eslint/tsconfig-utils" "8.35.1"
    "@typescript-eslint/types" "8.35.1"
    "@typescript-eslint/visitor-keys" "8.35.1"
    debug "^4.3.4"
    fast-glob "^3.3.2"
    is-glob "^4.0.3"
    minimatch "^9.0.4"
    semver "^7.6.0"
    ts-api-utils "^2.1.0"

"@typescript-eslint/utils@8.35.1":
  version "8.35.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@typescript-eslint/utils/-/utils-8.35.1.tgz"
  integrity sha1-qaDO64HJ0TLz91U3rSyn9somZSM=
  dependencies:
    "@eslint-community/eslint-utils" "^4.7.0"
    "@typescript-eslint/scope-manager" "8.35.1"
    "@typescript-eslint/types" "8.35.1"
    "@typescript-eslint/typescript-estree" "8.35.1"

"@typescript-eslint/visitor-keys@8.35.1":
  version "8.35.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@typescript-eslint/visitor-keys/-/visitor-keys-8.35.1.tgz"
  integrity sha1-qseKsiZd0RknvGrz+cWgIbvBZwo=
  dependencies:
    "@typescript-eslint/types" "8.35.1"
    eslint-visitor-keys "^4.2.1"

"@vitejs/plugin-react@^4.3.4":
  version "4.6.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@vitejs/plugin-react/-/plugin-react-4.6.0.tgz"
  integrity sha1-Jwe0hfRIBtQtQcY5IYg8/5xU36o=
  dependencies:
    "@babel/core" "^7.27.4"
    "@babel/plugin-transform-react-jsx-self" "^7.27.1"
    "@babel/plugin-transform-react-jsx-source" "^7.27.1"
    "@rolldown/pluginutils" "1.0.0-beta.19"
    "@types/babel__core" "^7.20.5"
    react-refresh "^0.17.0"

"@whatwg-node/disposablestack@^0.0.6":
  version "0.0.6"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@whatwg-node/disposablestack/-/disposablestack-0.0.6.tgz"
  integrity sha1-IGShQl6mYZTe9t8MehhRtpOcgrs=
  dependencies:
    "@whatwg-node/promise-helpers" "^1.0.0"
    tslib "^2.6.3"

"@whatwg-node/events@^0.0.3":
  version "0.0.3"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@whatwg-node/events/-/events-0.0.3.tgz"
  integrity sha1-E6Zd1PWJP1UoD3ZuKa5IB0knrK0=

"@whatwg-node/fetch@^0.10.0":
  version "0.10.8"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@whatwg-node/fetch/-/fetch-0.10.8.tgz"
  integrity sha1-FGf5UFgm+nJxxn368NclGrjCucw=
  dependencies:
    "@whatwg-node/node-fetch" "^0.7.21"
    urlpattern-polyfill "^10.0.0"

"@whatwg-node/fetch@^0.10.4":
  version "0.10.8"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@whatwg-node/fetch/-/fetch-0.10.8.tgz"
  integrity sha1-FGf5UFgm+nJxxn368NclGrjCucw=
  dependencies:
    "@whatwg-node/node-fetch" "^0.7.21"
    urlpattern-polyfill "^10.0.0"

"@whatwg-node/fetch@^0.8.0":
  version "0.8.8"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@whatwg-node/fetch/-/fetch-0.8.8.tgz"
  integrity sha1-SMatDGt5Uac+gS8J3SLXXp+hjK4=
  dependencies:
    "@peculiar/webcrypto" "^1.4.0"
    "@whatwg-node/node-fetch" "^0.3.6"
    busboy "^1.6.0"
    urlpattern-polyfill "^8.0.0"
    web-streams-polyfill "^3.2.1"

"@whatwg-node/node-fetch@^0.3.6":
  version "0.3.6"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@whatwg-node/node-fetch/-/node-fetch-0.3.6.tgz"
  integrity sha1-4ogWlV81mRbi2DC2imRJMST6ptA=
  dependencies:
    "@whatwg-node/events" "^0.0.3"
    busboy "^1.6.0"
    fast-querystring "^1.1.1"
    fast-url-parser "^1.1.3"
    tslib "^2.3.1"

"@whatwg-node/node-fetch@^0.7.21":
  version "0.7.21"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@whatwg-node/node-fetch/-/node-fetch-0.7.21.tgz"
  integrity sha1-upRO6naEBHyRrH9QCXJDYz9sn18=
  dependencies:
    "@fastify/busboy" "^3.1.1"
    "@whatwg-node/disposablestack" "^0.0.6"
    "@whatwg-node/promise-helpers" "^1.3.2"
    tslib "^2.6.3"

"@whatwg-node/promise-helpers@^1.0.0", "@whatwg-node/promise-helpers@^1.2.1", "@whatwg-node/promise-helpers@^1.2.4", "@whatwg-node/promise-helpers@^1.3.0", "@whatwg-node/promise-helpers@^1.3.2":
  version "1.3.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@whatwg-node/promise-helpers/-/promise-helpers-1.3.2.tgz"
  integrity sha1-O1SYetZRfvbbWSDGam8NraYGWH0=
  dependencies:
    tslib "^2.6.3"

"@wry/caches@^1.0.0":
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@wry/caches/-/caches-1.0.1.tgz"
  integrity sha1-hkH9O24JIwuGzouTVY1Ezx7OflI=
  dependencies:
    tslib "^2.3.0"

"@wry/context@^0.7.0":
  version "0.7.4"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@wry/context/-/context-0.7.4.tgz"
  integrity sha1-4y11D6B1lVxKss+4xICV4dQtWZA=
  dependencies:
    tslib "^2.3.0"

"@wry/equality@^0.5.6":
  version "0.5.7"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@wry/equality/-/equality-0.5.7.tgz"
  integrity sha1-cuwac3YJQ9Q51Wt7HpmFrsXUl7s=
  dependencies:
    tslib "^2.3.0"

"@wry/trie@^0.5.0":
  version "0.5.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/@wry/trie/-/trie-0.5.0.tgz"
  integrity sha1-EeeD86U/bkzR1C0tEyP1vD+pnJQ=
  dependencies:
    tslib "^2.3.0"

acorn-jsx@^5.3.2:
  version "5.3.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/acorn-jsx/-/acorn-jsx-5.3.2.tgz"
  integrity sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=

"acorn@^6.0.0 || ^7.0.0 || ^8.0.0", acorn@^8.15.0:
  version "8.15.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/acorn/-/acorn-8.15.0.tgz"
  integrity sha1-o2CJi8QV7arEbIJB9jg5dbkwuBY=

agent-base@^7.1.0, agent-base@^7.1.2:
  version "7.1.3"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/agent-base/-/agent-base-7.1.3.tgz"
  integrity sha1-KUNeuCG8QZRjOluJ5bxHA7r8JaE=

aggregate-error@^3.0.0:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/aggregate-error/-/aggregate-error-3.1.0.tgz"
  integrity sha1-kmcP9Q9TWb23o+DUDQ7DDFc3aHo=
  dependencies:
    clean-stack "^2.0.0"
    indent-string "^4.0.0"

ajv@^6.12.4:
  version "6.12.6"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/ajv/-/ajv-6.12.6.tgz"
  integrity sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ansi-escapes@^4.2.1, ansi-escapes@^4.3.0:
  version "4.3.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/ansi-escapes/-/ansi-escapes-4.3.2.tgz"
  integrity sha1-ayKR0dt9mLZSHV8e+kLQ86n+tl4=
  dependencies:
    type-fest "^0.21.3"

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/ansi-regex/-/ansi-regex-5.0.1.tgz"
  integrity sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=

ansi-styles@^4.0.0, ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/ansi-styles/-/ansi-styles-4.3.0.tgz"
  integrity sha1-7dgDYornHATIWuegkG7a00tkiTc=
  dependencies:
    color-convert "^2.0.1"

argparse@^2.0.1:
  version "2.0.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/argparse/-/argparse-2.0.1.tgz"
  integrity sha1-JG9Q88p4oyQPbJl+ipvR6sSeSzg=

array-union@^2.1.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/array-union/-/array-union-2.1.0.tgz"
  integrity sha1-t5hCCtvrHego2ErNii4j0+/oXo0=

asap@~2.0.3:
  version "2.0.6"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/asap/-/asap-2.0.6.tgz"
  integrity sha1-5QNHYR1+aQlDIIu9r+vLwvuGbUY=

asn1js@^3.0.5:
  version "3.0.6"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/asn1js/-/asn1js-3.0.6.tgz"
  integrity sha1-U+AC6+AMX3/XfBwEfDVX18BNziU=
  dependencies:
    pvtsutils "^1.3.6"
    pvutils "^1.1.3"
    tslib "^2.8.1"

astral-regex@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/astral-regex/-/astral-regex-2.0.0.tgz"
  integrity sha1-SDFDxWeu7UeFdZwIZXhtx319LjE=

auto-bind@~4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/auto-bind/-/auto-bind-4.0.0.tgz"
  integrity sha1-41ifxsLaj3ykO6n4T6UqdE/Jl/s=

babel-plugin-macros@^3.1.0:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/babel-plugin-macros/-/babel-plugin-macros-3.1.0.tgz"
  integrity sha1-nvbcdN65NLTbNE3Jc+6FHRSMUME=
  dependencies:
    "@babel/runtime" "^7.12.5"
    cosmiconfig "^7.0.0"
    resolve "^1.19.0"

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/balanced-match/-/balanced-match-1.0.2.tgz"
  integrity sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=

base64-js@^1.3.1:
  version "1.5.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/base64-js/-/base64-js-1.5.1.tgz"
  integrity sha1-GxtEAWClv3rUC2UPCVljSBkDkwo=

bl@^4.1.0:
  version "4.1.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/bl/-/bl-4.1.0.tgz"
  integrity sha1-RRU1JkGCvsL7vIOmKrmM8R2fezo=
  dependencies:
    buffer "^5.5.0"
    inherits "^2.0.4"
    readable-stream "^3.4.0"

brace-expansion@^1.1.7:
  version "1.1.12"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/brace-expansion/-/brace-expansion-1.1.12.tgz"
  integrity sha1-q5tFRGblqMw6GHvqrVgEEqnFuEM=
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

brace-expansion@^2.0.1:
  version "2.0.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/brace-expansion/-/brace-expansion-2.0.2.tgz"
  integrity sha1-VPxTI3phPYVMe9N0Y6rRffhyFOc=
  dependencies:
    balanced-match "^1.0.0"

braces@^3.0.3:
  version "3.0.3"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/braces/-/braces-3.0.3.tgz"
  integrity sha1-SQMy9AkZRSJy1VqEgK3AxEE1h4k=
  dependencies:
    fill-range "^7.1.1"

browserslist@^4.24.0, "browserslist@>= 4.21.0":
  version "4.25.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/browserslist/-/browserslist-4.25.1.tgz"
  integrity sha1-up6ObymKHYb4Kcm5deB5SJZ7sRE=
  dependencies:
    caniuse-lite "^1.0.30001726"
    electron-to-chromium "^1.5.173"
    node-releases "^2.0.19"
    update-browserslist-db "^1.1.3"

bser@2.1.1:
  version "2.1.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/bser/-/bser-2.1.1.tgz"
  integrity sha1-5nh9og7OnQeZhTPP2d5vXDj0vAU=
  dependencies:
    node-int64 "^0.4.0"

buffer@^5.5.0:
  version "5.7.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/buffer/-/buffer-5.7.1.tgz"
  integrity sha1-umLnwTEzBTWCGXFghRqPZI6Z7tA=
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.1.13"

busboy@^1.6.0:
  version "1.6.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/busboy/-/busboy-1.6.0.tgz"
  integrity sha1-lm6japUC5DzbkUaWJSO5L1MfaJM=
  dependencies:
    streamsearch "^1.1.0"

callsites@^3.0.0:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/callsites/-/callsites-3.1.0.tgz"
  integrity sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=

camel-case@^4.1.2:
  version "4.1.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/camel-case/-/camel-case-4.1.2.tgz"
  integrity sha1-lygHKpVPgFIoIlpt7qazhGHhvVo=
  dependencies:
    pascal-case "^3.1.2"
    tslib "^2.0.3"

caniuse-lite@^1.0.30001726:
  version "1.0.30001727"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/caniuse-lite/-/caniuse-lite-1.0.30001727.tgz"
  integrity sha1-IulwZCKtN6pQVWr4wQ5A4tk6i4U=

capital-case@^1.0.4:
  version "1.0.4"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/capital-case/-/capital-case-1.0.4.tgz"
  integrity sha1-nRMCkjU8kkn2sA+lhSvuOKcX5mk=
  dependencies:
    no-case "^3.0.4"
    tslib "^2.0.3"
    upper-case-first "^2.0.2"

chalk@^4.0.0, chalk@^4.1.0, chalk@^4.1.1:
  version "4.1.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/chalk/-/chalk-4.1.2.tgz"
  integrity sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

change-case-all@1.0.15:
  version "1.0.15"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/change-case-all/-/change-case-all-1.0.15.tgz"
  integrity sha1-3ik5MWf8EB1kbNdrDvI+J9CXVq0=
  dependencies:
    change-case "^4.1.2"
    is-lower-case "^2.0.2"
    is-upper-case "^2.0.2"
    lower-case "^2.0.2"
    lower-case-first "^2.0.2"
    sponge-case "^1.0.1"
    swap-case "^2.0.2"
    title-case "^3.0.3"
    upper-case "^2.0.2"
    upper-case-first "^2.0.2"

change-case@^4.1.2:
  version "4.1.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/change-case/-/change-case-4.1.2.tgz"
  integrity sha1-/t/F8TYEXiOYwEEO5EH5VwRkHhI=
  dependencies:
    camel-case "^4.1.2"
    capital-case "^1.0.4"
    constant-case "^3.0.4"
    dot-case "^3.0.4"
    header-case "^2.0.4"
    no-case "^3.0.4"
    param-case "^3.0.4"
    pascal-case "^3.1.2"
    path-case "^3.0.4"
    sentence-case "^3.0.4"
    snake-case "^3.0.4"
    tslib "^2.0.3"

chardet@^0.7.0:
  version "0.7.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/chardet/-/chardet-0.7.0.tgz"
  integrity sha1-kAlISfCTfy7twkJdDSip5fDLrZ4=

clean-stack@^2.0.0:
  version "2.2.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/clean-stack/-/clean-stack-2.2.0.tgz"
  integrity sha1-7oRy27Ep5yezHooQpCfe6d/kAIs=

cli-cursor@^3.1.0:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/cli-cursor/-/cli-cursor-3.1.0.tgz"
  integrity sha1-JkMFp65JDR0Dvwybp8kl0XU68wc=
  dependencies:
    restore-cursor "^3.1.0"

cli-spinners@^2.5.0:
  version "2.9.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/cli-spinners/-/cli-spinners-2.9.2.tgz"
  integrity sha1-F3Oo9LnE1qwxVj31Oz/B15Ri/kE=

cli-truncate@^2.1.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/cli-truncate/-/cli-truncate-2.1.0.tgz"
  integrity sha1-w54ovwXtzeW+O5iZKiLe7Vork8c=
  dependencies:
    slice-ansi "^3.0.0"
    string-width "^4.2.0"

cli-width@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/cli-width/-/cli-width-3.0.0.tgz"
  integrity sha1-ovSEN6LKqaIkNueUvwceyeYc7fY=

cliui@^8.0.1:
  version "8.0.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/cliui/-/cliui-8.0.1.tgz"
  integrity sha1-DASwddsCy/5g3I5s8vVIaxo2CKo=
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.1"
    wrap-ansi "^7.0.0"

clone@^1.0.2:
  version "1.0.4"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/clone/-/clone-1.0.4.tgz"
  integrity sha1-2jCcwmPfFZlMaIypAheco8fNfH4=

clsx@^2.0.0, clsx@^2.1.1:
  version "2.1.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/clsx/-/clsx-2.1.1.tgz"
  integrity sha1-7tOXyf2L2IK/sY3qtxAgSaLzKZk=

color-convert@^2.0.1:
  version "2.0.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/color-convert/-/color-convert-2.0.1.tgz"
  integrity sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=
  dependencies:
    color-name "~1.1.4"

color-name@~1.1.4:
  version "1.1.4"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/color-name/-/color-name-1.1.4.tgz"
  integrity sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=

colorette@^2.0.16:
  version "2.0.20"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/colorette/-/colorette-2.0.20.tgz"
  integrity sha1-nreT5oMwZ/cjWQL807CZF6AAqVo=

common-tags@1.8.2:
  version "1.8.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/common-tags/-/common-tags-1.8.2.tgz"
  integrity sha1-lOuzwHbSYDJ0X9VPrOf2iO9aycY=

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/concat-map/-/concat-map-0.0.1.tgz"
  integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=

constant-case@^3.0.4:
  version "3.0.4"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/constant-case/-/constant-case-3.0.4.tgz"
  integrity sha1-O4Sprq9M8x7EXmv13pG9+wWJ+vE=
  dependencies:
    no-case "^3.0.4"
    tslib "^2.0.3"
    upper-case "^2.0.2"

convert-source-map@^1.5.0:
  version "1.9.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/convert-source-map/-/convert-source-map-1.9.0.tgz"
  integrity sha1-f6rmI1P7QhM2bQypg1jSLoNosF8=

convert-source-map@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/convert-source-map/-/convert-source-map-2.0.0.tgz"
  integrity sha1-S1YPZJ/E6RjdCrdc9JYei8iC2Co=

cookie@^1.0.1:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/cookie/-/cookie-1.0.2.tgz"
  integrity sha1-JzYHAVMhFr0/H5QWkp0Xav4eRhA=

cosmiconfig@^7.0.0:
  version "7.1.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/cosmiconfig/-/cosmiconfig-7.1.0.tgz"
  integrity sha1-FEO5r6WWtnAILqRsvY9qYrhGNfY=
  dependencies:
    "@types/parse-json" "^4.0.0"
    import-fresh "^3.2.1"
    parse-json "^5.0.0"
    path-type "^4.0.0"
    yaml "^1.10.0"

cosmiconfig@^8.1.0, cosmiconfig@^8.1.3:
  version "8.3.6"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/cosmiconfig/-/cosmiconfig-8.3.6.tgz"
  integrity sha1-Bgorhx1m26bIU46hEYuhrBb1+uM=
  dependencies:
    import-fresh "^3.3.0"
    js-yaml "^4.1.0"
    parse-json "^5.2.0"
    path-type "^4.0.0"

cross-fetch@^3.1.5:
  version "3.2.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/cross-fetch/-/cross-fetch-3.2.0.tgz"
  integrity sha1-NOkZL1O8dX1mFDBNnl5vtO23guM=
  dependencies:
    node-fetch "^2.7.0"

cross-inspect@1.0.1:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/cross-inspect/-/cross-inspect-1.0.1.tgz"
  integrity sha1-Ffb2XkypY89MwaK1/vGPbKMocSs=
  dependencies:
    tslib "^2.4.0"

cross-spawn@^7.0.6:
  version "7.0.6"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/cross-spawn/-/cross-spawn-7.0.6.tgz"
  integrity sha1-ilj+ePANzXDDcEUXWd+/rwPo7p8=
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

csstype@^3.0.2, csstype@^3.1.3:
  version "3.1.3"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/csstype/-/csstype-3.1.3.tgz"
  integrity sha1-2A/ylNEU+w5qxQD7+FtgE31+/4E=

d3-array@^3.1.6, "d3-array@2 - 3", "d3-array@2.10.0 - 3":
  version "3.2.4"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/d3-array/-/d3-array-3.2.4.tgz"
  integrity sha1-Ff7DOyN/l6xdfJhtx32ic6jtC7U=
  dependencies:
    internmap "1 - 2"

"d3-color@1 - 3":
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/d3-color/-/d3-color-3.1.0.tgz"
  integrity sha1-OVsoM9+scVB/EqwvevI7+BneJOI=

d3-ease@^3.0.1:
  version "3.0.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/d3-ease/-/d3-ease-3.0.1.tgz"
  integrity sha1-llisOKIUDVnTRhYPH2ww/aC9EvQ=

"d3-format@1 - 3":
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/d3-format/-/d3-format-3.1.0.tgz"
  integrity sha1-kmDiOijqXLEJ6TshoG4k4uvVVkE=

d3-interpolate@^3.0.1, "d3-interpolate@1.2.0 - 3":
  version "3.0.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/d3-interpolate/-/d3-interpolate-3.0.1.tgz"
  integrity sha1-PEeqWzLFs9+1bvP9Q0IHimMrQA0=
  dependencies:
    d3-color "1 - 3"

d3-path@^3.1.0:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/d3-path/-/d3-path-3.1.0.tgz"
  integrity sha1-It+TkDL7WnGuixgA1h3beFHEJSY=

d3-scale@^4.0.2:
  version "4.0.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/d3-scale/-/d3-scale-4.0.2.tgz"
  integrity sha1-grOOjo/3CAdk+Nzsd71L45Nok5Y=
  dependencies:
    d3-array "2.10.0 - 3"
    d3-format "1 - 3"
    d3-interpolate "1.2.0 - 3"
    d3-time "2.1.1 - 3"
    d3-time-format "2 - 4"

d3-shape@^3.1.0:
  version "3.2.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/d3-shape/-/d3-shape-3.2.0.tgz"
  integrity sha1-oag5y9m6RfKGdMadf4Vbz5HfxqU=
  dependencies:
    d3-path "^3.1.0"

"d3-time-format@2 - 4":
  version "4.1.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/d3-time-format/-/d3-time-format-4.1.0.tgz"
  integrity sha1-erUlelBB0R7LT+cKXH0WoZW7QIo=
  dependencies:
    d3-time "1 - 3"

d3-time@^3.0.0, "d3-time@1 - 3", "d3-time@2.1.1 - 3":
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/d3-time/-/d3-time-3.1.0.tgz"
  integrity sha1-kxDbVumS48AXXh7zheVF5Iqbtcc=
  dependencies:
    d3-array "2 - 3"

d3-timer@^3.0.1:
  version "3.0.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/d3-timer/-/d3-timer-3.0.1.tgz"
  integrity sha1-YoTSonCChbGrt+IB7aQ4CvNeY7A=

data-uri-to-buffer@^4.0.0:
  version "4.0.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/data-uri-to-buffer/-/data-uri-to-buffer-4.0.1.tgz"
  integrity sha1-2P6ysogeak9YwuCKz9Dig04mIi4=

dataloader@^2.2.3:
  version "2.2.3"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/dataloader/-/dataloader-2.2.3.tgz"
  integrity sha1-QtELSRNRX1s3xqzty0lg1q4bFRc=

dayjs@^1.10.7, dayjs@^1.11.13:
  version "1.11.13"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/dayjs/-/dayjs-1.11.13.tgz"
  integrity sha1-kkMLATkFXD67YBUKoT6GCktaNmw=

debounce@^1.2.0:
  version "1.2.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/debounce/-/debounce-1.2.1.tgz"
  integrity sha1-OIgdj0FmpcWEgCDBGCe4NLyz4KU=

debug@^4.1.0, debug@^4.3.1, debug@^4.3.2, debug@^4.3.4, debug@4:
  version "4.4.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/debug/-/debug-4.4.1.tgz"
  integrity sha1-5ai8bLxMbNPmQwiwaTo9T6VQGJs=
  dependencies:
    ms "^2.1.3"

decimal.js-light@^2.4.1:
  version "2.5.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/decimal.js-light/-/decimal.js-light-2.5.1.tgz"
  integrity sha1-E0/TJQjxniCPT7L42sDSYmqGeTQ=

deep-is@^0.1.3:
  version "0.1.4"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/deep-is/-/deep-is-0.1.4.tgz"
  integrity sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=

defaults@^1.0.3:
  version "1.0.4"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/defaults/-/defaults-1.0.4.tgz"
  integrity sha1-sLAgYsHiqmL/XZUo8PmLqpCXjXo=
  dependencies:
    clone "^1.0.2"

dependency-graph@^0.11.0:
  version "0.11.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/dependency-graph/-/dependency-graph-0.11.0.tgz"
  integrity sha1-rAzn7WilTaIhZahel6AdU/XrLic=

detect-indent@^6.0.0:
  version "6.1.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/detect-indent/-/detect-indent-6.1.0.tgz"
  integrity sha1-WSSF67v2s7GrK+F1yDk9BMoNV+Y=

dir-glob@^3.0.1:
  version "3.0.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/dir-glob/-/dir-glob-3.0.1.tgz"
  integrity sha1-Vtv3PZkqSpO6FYT0U0Bj/S5BcX8=
  dependencies:
    path-type "^4.0.0"

dom-helpers@^5.0.1:
  version "5.2.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/dom-helpers/-/dom-helpers-5.2.1.tgz"
  integrity sha1-2UAFNrK/giWtmP4FLgKUUaxA6QI=
  dependencies:
    "@babel/runtime" "^7.8.7"
    csstype "^3.0.2"

dot-case@^3.0.4:
  version "3.0.4"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/dot-case/-/dot-case-3.0.4.tgz"
  integrity sha1-mytnDQCkMWZ6inW6Kc0bmICc51E=
  dependencies:
    no-case "^3.0.4"
    tslib "^2.0.3"

dotenv@^16.0.0:
  version "16.6.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/dotenv/-/dotenv-16.6.1.tgz"
  integrity sha1-dz8OaVJ6gxXHKF1e5zxEWdIKgCA=

dset@^3.1.2, dset@^3.1.4:
  version "3.1.4"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/dset/-/dset-3.1.4.tgz"
  integrity sha1-+Or18CPwaKA20IzQfcn/t9AGUkg=

electron-to-chromium@^1.5.173:
  version "1.5.179"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/electron-to-chromium/-/electron-to-chromium-1.5.179.tgz"
  integrity sha1-RT1T82ABSiYE1AzNQcTqCm4xuZo=

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/emoji-regex/-/emoji-regex-8.0.0.tgz"
  integrity sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=

error-ex@^1.3.1:
  version "1.3.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/error-ex/-/error-ex-1.3.2.tgz"
  integrity sha1-tKxAZIEH/c3PriQvQovqihTU8b8=
  dependencies:
    is-arrayish "^0.2.1"

esbuild@^0.25.0:
  version "0.25.5"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/esbuild/-/esbuild-0.25.5.tgz"
  integrity sha1-cQdQVJk/3652xmWG+bnB+Nft1DA=
  optionalDependencies:
    "@esbuild/aix-ppc64" "0.25.5"
    "@esbuild/android-arm" "0.25.5"
    "@esbuild/android-arm64" "0.25.5"
    "@esbuild/android-x64" "0.25.5"
    "@esbuild/darwin-arm64" "0.25.5"
    "@esbuild/darwin-x64" "0.25.5"
    "@esbuild/freebsd-arm64" "0.25.5"
    "@esbuild/freebsd-x64" "0.25.5"
    "@esbuild/linux-arm" "0.25.5"
    "@esbuild/linux-arm64" "0.25.5"
    "@esbuild/linux-ia32" "0.25.5"
    "@esbuild/linux-loong64" "0.25.5"
    "@esbuild/linux-mips64el" "0.25.5"
    "@esbuild/linux-ppc64" "0.25.5"
    "@esbuild/linux-riscv64" "0.25.5"
    "@esbuild/linux-s390x" "0.25.5"
    "@esbuild/linux-x64" "0.25.5"
    "@esbuild/netbsd-arm64" "0.25.5"
    "@esbuild/netbsd-x64" "0.25.5"
    "@esbuild/openbsd-arm64" "0.25.5"
    "@esbuild/openbsd-x64" "0.25.5"
    "@esbuild/sunos-x64" "0.25.5"
    "@esbuild/win32-arm64" "0.25.5"
    "@esbuild/win32-ia32" "0.25.5"
    "@esbuild/win32-x64" "0.25.5"

escalade@^3.1.1, escalade@^3.2.0:
  version "3.2.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/escalade/-/escalade-3.2.0.tgz"
  integrity sha1-ARo/aYVroYnf+n3I/M6Z0qh5A+U=

escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz"
  integrity sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=

escape-string-regexp@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz"
  integrity sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ=

eslint-config-prettier@^10.0.1, "eslint-config-prettier@>= 7.0.0 <10.0.0 || >=10.1.0":
  version "10.1.5"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/eslint-config-prettier/-/eslint-config-prettier-10.1.5.tgz"
  integrity sha1-AMGNciUEO2+85qZlaXN3mY1FN4I=

eslint-plugin-prettier@^5.2.3:
  version "5.5.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/eslint-plugin-prettier/-/eslint-plugin-prettier-5.5.1.tgz"
  integrity sha1-Rwgglk3prts36c5iwyZtLSbQjRU=
  dependencies:
    prettier-linter-helpers "^1.0.0"
    synckit "^0.11.7"

eslint-plugin-react-hooks@^5.0.0:
  version "5.2.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/eslint-plugin-react-hooks/-/eslint-plugin-react-hooks-5.2.0.tgz"
  integrity sha1-G+AICQHmrDHOeXG+7T0+wKQj2eM=

eslint-plugin-react-refresh@^0.4.18:
  version "0.4.20"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/eslint-plugin-react-refresh/-/eslint-plugin-react-refresh-0.4.20.tgz"
  integrity sha1-O7+1yGN+KNGc40Q2hkReUC7NGLo=

eslint-scope@^8.4.0:
  version "8.4.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/eslint-scope/-/eslint-scope-8.4.0.tgz"
  integrity sha1-iOZGogf61hQ2/6OetQUUcgBlXII=
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^5.2.0"

eslint-visitor-keys@^3.4.3:
  version "3.4.3"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz"
  integrity sha1-DNcv6FUOPC6uFWqWpN3c0cisWAA=

eslint-visitor-keys@^4.2.1:
  version "4.2.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/eslint-visitor-keys/-/eslint-visitor-keys-4.2.1.tgz"
  integrity sha1-TP6mD+fdCtjoFuHtAmwdUlG1EsE=

"eslint@^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0 || ^9.0.0", "eslint@^6.0.0 || ^7.0.0 || >=8.0.0", "eslint@^8.57.0 || ^9.0.0", eslint@^9.19.0, eslint@>=7.0.0, eslint@>=8.0.0, eslint@>=8.40:
  version "9.30.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/eslint/-/eslint-9.30.1.tgz"
  integrity sha1-1BB7OZZEEqzZtcB0Txxt9RT6EhE=
  dependencies:
    "@eslint-community/eslint-utils" "^4.2.0"
    "@eslint-community/regexpp" "^4.12.1"
    "@eslint/config-array" "^0.21.0"
    "@eslint/config-helpers" "^0.3.0"
    "@eslint/core" "^0.14.0"
    "@eslint/eslintrc" "^3.3.1"
    "@eslint/js" "9.30.1"
    "@eslint/plugin-kit" "^0.3.1"
    "@humanfs/node" "^0.16.6"
    "@humanwhocodes/module-importer" "^1.0.1"
    "@humanwhocodes/retry" "^0.4.2"
    "@types/estree" "^1.0.6"
    "@types/json-schema" "^7.0.15"
    ajv "^6.12.4"
    chalk "^4.0.0"
    cross-spawn "^7.0.6"
    debug "^4.3.2"
    escape-string-regexp "^4.0.0"
    eslint-scope "^8.4.0"
    eslint-visitor-keys "^4.2.1"
    espree "^10.4.0"
    esquery "^1.5.0"
    esutils "^2.0.2"
    fast-deep-equal "^3.1.3"
    file-entry-cache "^8.0.0"
    find-up "^5.0.0"
    glob-parent "^6.0.2"
    ignore "^5.2.0"
    imurmurhash "^0.1.4"
    is-glob "^4.0.0"
    json-stable-stringify-without-jsonify "^1.0.1"
    lodash.merge "^4.6.2"
    minimatch "^3.1.2"
    natural-compare "^1.4.0"
    optionator "^0.9.3"

espree@^10.0.1, espree@^10.4.0:
  version "10.4.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/espree/-/espree-10.4.0.tgz"
  integrity sha1-1U9JSdRikAWh+haNk3w/8ffiqDc=
  dependencies:
    acorn "^8.15.0"
    acorn-jsx "^5.3.2"
    eslint-visitor-keys "^4.2.1"

esquery@^1.5.0:
  version "1.6.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/esquery/-/esquery-1.6.0.tgz"
  integrity sha1-kUGSNPgE2FKoLc7sPhbNwiz52uc=
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.3.0:
  version "4.3.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/esrecurse/-/esrecurse-4.3.0.tgz"
  integrity sha1-eteWTWeauyi+5yzsY3WLHF0smSE=
  dependencies:
    estraverse "^5.2.0"

estraverse@^5.1.0, estraverse@^5.2.0:
  version "5.3.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/estraverse/-/estraverse-5.3.0.tgz"
  integrity sha1-LupSkHAvJquP5TcDcP+GyWXSESM=

esutils@^2.0.2:
  version "2.0.3"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/esutils/-/esutils-2.0.3.tgz"
  integrity sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=

eventemitter3@^4.0.1:
  version "4.0.7"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/eventemitter3/-/eventemitter3-4.0.7.tgz"
  integrity sha1-Lem2j2Uo1WRO9cWVJqG0oHMGFp8=

external-editor@^3.0.3:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/external-editor/-/external-editor-3.1.0.tgz"
  integrity sha1-ywP3QL764D6k0oPK7SdBqD8zVJU=
  dependencies:
    chardet "^0.7.0"
    iconv-lite "^0.4.24"
    tmp "^0.0.33"

fast-decode-uri-component@^1.0.1:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/fast-decode-uri-component/-/fast-decode-uri-component-1.0.1.tgz"
  integrity sha1-Rvi2wisw/3qBNX1PWav66TggJUM=

fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
  version "3.1.3"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz"
  integrity sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=

fast-diff@^1.1.2:
  version "1.3.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/fast-diff/-/fast-diff-1.3.0.tgz"
  integrity sha1-7OQH+lUKZNY4U2zXJ+EpxhYW4PA=

fast-equals@^5.0.1:
  version "5.2.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/fast-equals/-/fast-equals-5.2.2.tgz"
  integrity sha1-iF17+wefrAzg6EUDdLzinpt0JIQ=

fast-glob@^3.2.9, fast-glob@^3.3.2:
  version "3.3.3"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/fast-glob/-/fast-glob-3.3.3.tgz"
  integrity sha1-0G1YXOjbqQoWsFBcVDw8z7OuuBg=
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.8"

fast-json-stable-stringify@^2.0.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz"
  integrity sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=

fast-levenshtein@^2.0.6:
  version "2.0.6"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz"
  integrity sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=

fast-querystring@^1.1.1:
  version "1.1.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/fast-querystring/-/fast-querystring-1.1.2.tgz"
  integrity sha1-ptJJN7T8b3kbTuMdy29Trq+4n1M=
  dependencies:
    fast-decode-uri-component "^1.0.1"

fast-url-parser@^1.1.3:
  version "1.1.3"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/fast-url-parser/-/fast-url-parser-1.1.3.tgz"
  integrity sha1-9K8+qfNNiicc9YrSs3WfQx8LMY0=
  dependencies:
    punycode "^1.3.2"

fastq@^1.6.0:
  version "1.19.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/fastq/-/fastq-1.19.1.tgz"
  integrity sha1-1Q6rqAPIhGqIPBZJKCHrzSzaVfU=
  dependencies:
    reusify "^1.0.4"

fb-watchman@^2.0.0:
  version "2.0.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/fb-watchman/-/fb-watchman-2.0.2.tgz"
  integrity sha1-6VJO5rXHfp5QAa8PhfOtu4YjJVw=
  dependencies:
    bser "2.1.1"

fbjs-css-vars@^1.0.0:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/fbjs-css-vars/-/fbjs-css-vars-1.0.2.tgz"
  integrity sha1-IWVRE2rgL+JVkyw+yHdfGOLAeLg=

fbjs@^3.0.0:
  version "3.0.5"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/fbjs/-/fbjs-3.0.5.tgz"
  integrity sha1-qg7bfVyqY0ABF5C9kknb74qBEo0=
  dependencies:
    cross-fetch "^3.1.5"
    fbjs-css-vars "^1.0.0"
    loose-envify "^1.0.0"
    object-assign "^4.1.0"
    promise "^7.1.1"
    setimmediate "^1.0.5"
    ua-parser-js "^1.0.35"

fdir@^6.4.4:
  version "6.4.6"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/fdir/-/fdir-6.4.6.tgz"
  integrity sha1-KyaMAjJpcGMRG78/ZIEKKnQbooE=

fetch-blob@^3.1.2, fetch-blob@^3.1.4:
  version "3.2.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/fetch-blob/-/fetch-blob-3.2.0.tgz"
  integrity sha1-8JuNS71Frcbwwgt+eH55PjCdzOk=
  dependencies:
    node-domexception "^1.0.0"
    web-streams-polyfill "^3.0.3"

figures@^3.0.0:
  version "3.2.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/figures/-/figures-3.2.0.tgz"
  integrity sha1-YlwYvSk8YE3EqN2y/r8MiDQXRq8=
  dependencies:
    escape-string-regexp "^1.0.5"

file-entry-cache@^8.0.0:
  version "8.0.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/file-entry-cache/-/file-entry-cache-8.0.0.tgz"
  integrity sha1-d4e93PETG/+5JjbGlFe7wO3W2B8=
  dependencies:
    flat-cache "^4.0.0"

file-saver@^2.0.5:
  version "2.0.5"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/file-saver/-/file-saver-2.0.5.tgz"
  integrity sha1-1hz+LOBZ9BTYmendbUEH7iVnDDg=

fill-range@^7.1.1:
  version "7.1.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/fill-range/-/fill-range-7.1.1.tgz"
  integrity sha1-RCZdPKwH4+p9wkdRY4BkN1SgUpI=
  dependencies:
    to-regex-range "^5.0.1"

find-root@^1.1.0:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/find-root/-/find-root-1.1.0.tgz"
  integrity sha1-q8/Iunb3CMQql7PWhbfpRQv7nOQ=

find-up@^5.0.0:
  version "5.0.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/find-up/-/find-up-5.0.0.tgz"
  integrity sha1-TJKBnstwg1YeT0okCoa+UZj1Nvw=
  dependencies:
    locate-path "^6.0.0"
    path-exists "^4.0.0"

flat-cache@^4.0.0:
  version "4.0.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/flat-cache/-/flat-cache-4.0.1.tgz"
  integrity sha1-Ds45/LFO4BL0sEEL0z3ZwfAREnw=
  dependencies:
    flatted "^3.2.9"
    keyv "^4.5.4"

flatted@^3.2.9:
  version "3.3.3"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/flatted/-/flatted-3.3.3.tgz"
  integrity sha1-Z8j62VRUp8er6/dLt47nSkQCM1g=

formdata-polyfill@^4.0.10:
  version "4.0.10"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/formdata-polyfill/-/formdata-polyfill-4.0.10.tgz"
  integrity sha1-JIB8McnUAuACqz2McgFEzriEhCM=
  dependencies:
    fetch-blob "^3.1.2"

fsevents@~2.3.2, fsevents@~2.3.3:
  version "2.3.3"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/fsevents/-/fsevents-2.3.3.tgz"
  integrity sha1-ysZAd4XQNnWipeGlMFxpezR9kNY=

function-bind@^1.1.2:
  version "1.1.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/function-bind/-/function-bind-1.1.2.tgz"
  integrity sha1-LALYZNl/PqbIgwxGTL0Rq26rehw=

gensync@^1.0.0-beta.2:
  version "1.0.0-beta.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/gensync/-/gensync-1.0.0-beta.2.tgz"
  integrity sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=

get-caller-file@^2.0.5:
  version "2.0.5"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/get-caller-file/-/get-caller-file-2.0.5.tgz"
  integrity sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=

glob-parent@^5.1.2:
  version "5.1.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/glob-parent/-/glob-parent-5.1.2.tgz"
  integrity sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=
  dependencies:
    is-glob "^4.0.1"

glob-parent@^6.0.2:
  version "6.0.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/glob-parent/-/glob-parent-6.0.2.tgz"
  integrity sha1-bSN9mQg5UMeSkPJMdkKj3poo+eM=
  dependencies:
    is-glob "^4.0.3"

globals@^14.0.0:
  version "14.0.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/globals/-/globals-14.0.0.tgz"
  integrity sha1-iY10E8Kbq89rr+Vvyt3thYrack4=

globals@^15.14.0:
  version "15.15.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/globals/-/globals-15.15.0.tgz"
  integrity sha1-fEdhKZ1BwysHVxWkzh7eeJf/cqg=

globby@^11.0.3:
  version "11.1.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/globby/-/globby-11.1.0.tgz"
  integrity sha1-vUvpi7BC+D15b344EZkfvoKg00s=
  dependencies:
    array-union "^2.1.0"
    dir-glob "^3.0.1"
    fast-glob "^3.2.9"
    ignore "^5.2.0"
    merge2 "^1.4.1"
    slash "^3.0.0"

graphemer@^1.4.0:
  version "1.4.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/graphemer/-/graphemer-1.4.0.tgz"
  integrity sha1-+y8dVeDjoYSa7/yQxPoN1ToOZsY=

graphql-config@^5.0.2:
  version "5.1.5"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/graphql-config/-/graphql-config-5.1.5.tgz"
  integrity sha1-NOC/uojnS27v2IlxapN4CG9ZX38=
  dependencies:
    "@graphql-tools/graphql-file-loader" "^8.0.0"
    "@graphql-tools/json-file-loader" "^8.0.0"
    "@graphql-tools/load" "^8.1.0"
    "@graphql-tools/merge" "^9.0.0"
    "@graphql-tools/url-loader" "^8.0.0"
    "@graphql-tools/utils" "^10.0.0"
    cosmiconfig "^8.1.0"
    jiti "^2.0.0"
    minimatch "^9.0.5"
    string-env-interpolation "^1.0.1"
    tslib "^2.4.0"

graphql-request@^6.0.0:
  version "6.1.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/graphql-request/-/graphql-request-6.1.0.tgz"
  integrity sha1-9OshB5Z688elkH6zExxnHqyJvk8=
  dependencies:
    "@graphql-typed-document-node/core" "^3.2.0"
    cross-fetch "^3.1.5"

graphql-tag@^2.11.0, graphql-tag@^2.12.6:
  version "2.12.6"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/graphql-tag/-/graphql-tag-2.12.6.tgz"
  integrity sha1-1EGlacHSU37xDKPRYztIclMptfE=
  dependencies:
    tslib "^2.1.0"

"graphql-ws@^5.5.5 || ^6.0.3", graphql-ws@^6.0.3:
  version "6.0.5"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/graphql-ws/-/graphql-ws-6.0.5.tgz"
  integrity sha1-JK3PREYC34NHe54HzUtX9BGtoCQ=

graphql@*, "graphql@^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0", "graphql@^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0", "graphql@^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0", "graphql@^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0", "graphql@^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0", "graphql@^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0", "graphql@^15.0.0 || ^16.0.0", "graphql@^15.10.1 || ^16", graphql@^16.10.0, "graphql@14 - 16":
  version "16.11.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/graphql/-/graphql-16.11.0.tgz"
  integrity sha1-ltF/ZjcGeAJ/31my1MILTvqopjM=

has-flag@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/has-flag/-/has-flag-4.0.0.tgz"
  integrity sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=

hasown@^2.0.2:
  version "2.0.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/hasown/-/hasown-2.0.2.tgz"
  integrity sha1-AD6vkb563DcuhOxZ3DclLO24AAM=
  dependencies:
    function-bind "^1.1.2"

header-case@^2.0.4:
  version "2.0.4"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/header-case/-/header-case-2.0.4.tgz"
  integrity sha1-WkLmO1UXc0nPQFvrjXdayruSwGM=
  dependencies:
    capital-case "^1.0.4"
    tslib "^2.0.3"

hoist-non-react-statics@^3.3.1, hoist-non-react-statics@^3.3.2:
  version "3.3.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/hoist-non-react-statics/-/hoist-non-react-statics-3.3.2.tgz"
  integrity sha1-7OCsr3HWLClpwuxZ/v9CpLGoW0U=
  dependencies:
    react-is "^16.7.0"

html-parse-stringify@^3.0.1:
  version "3.0.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/html-parse-stringify/-/html-parse-stringify-3.0.1.tgz"
  integrity sha1-38EBc0fOn3fIFBpQfyMwQMWcVdI=
  dependencies:
    void-elements "3.1.0"

http-proxy-agent@^7.0.0:
  version "7.0.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/http-proxy-agent/-/http-proxy-agent-7.0.2.tgz"
  integrity sha1-mosfJGhmwChQlIZYX2K48sGMJw4=
  dependencies:
    agent-base "^7.1.0"
    debug "^4.3.4"

https-proxy-agent@^7.0.0:
  version "7.0.6"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/https-proxy-agent/-/https-proxy-agent-7.0.6.tgz"
  integrity sha1-2o3+rH2hMLBcK6S1nJts1mYRprk=
  dependencies:
    agent-base "^7.1.2"
    debug "4"

i18next@^24.2.2, "i18next@>= 23.2.3":
  version "24.2.3"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/i18next/-/i18next-24.2.3.tgz"
  integrity sha1-OgX3JhXL18ANfjSGZ+KqvvHfdTs=
  dependencies:
    "@babel/runtime" "^7.26.10"

iconv-lite@^0.4.24:
  version "0.4.24"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/iconv-lite/-/iconv-lite-0.4.24.tgz"
  integrity sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

ieee754@^1.1.13:
  version "1.2.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/ieee754/-/ieee754-1.2.1.tgz"
  integrity sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I=

ignore@^5.2.0:
  version "5.3.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/ignore/-/ignore-5.3.2.tgz"
  integrity sha1-PNQOcp82Q/2HywTlC/DrcivFlvU=

ignore@^7.0.0:
  version "7.0.5"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/ignore/-/ignore-7.0.5.tgz"
  integrity sha1-TLX2zX1MerA2VzjHrqiIuqbX79k=

immutable@~3.7.6:
  version "3.7.6"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/immutable/-/immutable-3.7.6.tgz"
  integrity sha1-E7TTyxK++hVIKib+Gy665kAHHks=

import-fresh@^3.2.1, import-fresh@^3.3.0:
  version "3.3.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/import-fresh/-/import-fresh-3.3.1.tgz"
  integrity sha1-nOy1ZQPAraHydB271lRuSxO1fM8=
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

import-from@4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/import-from/-/import-from-4.0.0.tgz"
  integrity sha1-JxC41mgX0jLhb0Fm4xkkjT1UkuI=

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/imurmurhash/-/imurmurhash-0.1.4.tgz"
  integrity sha1-khi5srkoojixPcT7a21XbyMUU+o=

indent-string@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/indent-string/-/indent-string-4.0.0.tgz"
  integrity sha1-Yk+PRJfWGbLZdoUx1Y9BIoVNclE=

inherits@^2.0.3, inherits@^2.0.4:
  version "2.0.4"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/inherits/-/inherits-2.0.4.tgz"
  integrity sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=

inquirer@^8.0.0:
  version "8.2.6"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/inquirer/-/inquirer-8.2.6.tgz"
  integrity sha1-czt0iIGV2NQApnrDMgEbX65epWI=
  dependencies:
    ansi-escapes "^4.2.1"
    chalk "^4.1.1"
    cli-cursor "^3.1.0"
    cli-width "^3.0.0"
    external-editor "^3.0.3"
    figures "^3.0.0"
    lodash "^4.17.21"
    mute-stream "0.0.8"
    ora "^5.4.1"
    run-async "^2.4.0"
    rxjs "^7.5.5"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"
    through "^2.3.6"
    wrap-ansi "^6.0.1"

"internmap@1 - 2":
  version "2.0.3"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/internmap/-/internmap-2.0.3.tgz"
  integrity sha1-ZoXyN1XkPFJOJR0py8lySOMGEAk=

invariant@^2.2.4:
  version "2.2.4"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/invariant/-/invariant-2.2.4.tgz"
  integrity sha1-YQ88ksk1nOHbYW5TgAjSP/NRWOY=
  dependencies:
    loose-envify "^1.0.0"

is-absolute@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/is-absolute/-/is-absolute-1.0.0.tgz"
  integrity sha1-OV4a6EsR8mrReV5zwXN45IowFXY=
  dependencies:
    is-relative "^1.0.0"
    is-windows "^1.0.1"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/is-arrayish/-/is-arrayish-0.2.1.tgz"
  integrity sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=

is-core-module@^2.16.0:
  version "2.16.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/is-core-module/-/is-core-module-2.16.1.tgz"
  integrity sha1-KpiAGoSfQ+Kt1kT7trxiKbGaTvQ=
  dependencies:
    hasown "^2.0.2"

is-extglob@^2.1.1:
  version "2.1.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/is-extglob/-/is-extglob-2.1.1.tgz"
  integrity sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz"
  integrity sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@^4.0.3, is-glob@4.0.3:
  version "4.0.3"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/is-glob/-/is-glob-4.0.3.tgz"
  integrity sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=
  dependencies:
    is-extglob "^2.1.1"

is-interactive@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/is-interactive/-/is-interactive-1.0.0.tgz"
  integrity sha1-zqbmrlyHCnsKAAQHC3tYfgJSkS4=

is-lower-case@^2.0.2:
  version "2.0.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/is-lower-case/-/is-lower-case-2.0.2.tgz"
  integrity sha1-HAiE0wEshBVWJDSDql1SL0c5bSo=
  dependencies:
    tslib "^2.0.3"

is-number@^7.0.0:
  version "7.0.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/is-number/-/is-number-7.0.0.tgz"
  integrity sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=

is-relative@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/is-relative/-/is-relative-1.0.0.tgz"
  integrity sha1-obtpNc6MXboei5dUubLcwCDiJg0=
  dependencies:
    is-unc-path "^1.0.0"

is-unc-path@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/is-unc-path/-/is-unc-path-1.0.0.tgz"
  integrity sha1-1zHoiY7QkKEsNSrS6u1Qla0yLJ0=
  dependencies:
    unc-path-regex "^0.1.2"

is-unicode-supported@^0.1.0:
  version "0.1.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/is-unicode-supported/-/is-unicode-supported-0.1.0.tgz"
  integrity sha1-PybHaoCVk7Ur+i7LVxDtJ3m1Iqc=

is-upper-case@^2.0.2:
  version "2.0.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/is-upper-case/-/is-upper-case-2.0.2.tgz"
  integrity sha1-8RBc7R/k3pBqXzlVPn04A/2ARkk=
  dependencies:
    tslib "^2.0.3"

is-windows@^1.0.1:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/is-windows/-/is-windows-1.0.2.tgz"
  integrity sha1-0YUOuXkezRjmGCzhKjDzlmNLsZ0=

isexe@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/isexe/-/isexe-2.0.0.tgz"
  integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=

isomorphic-ws@^5.0.0:
  version "5.0.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/isomorphic-ws/-/isomorphic-ws-5.0.0.tgz"
  integrity sha1-5VKRSJEuy5tFG0btRNU9rhzgS78=

jiti@*, jiti@^1.17.1, jiti@>=1.21.0:
  version "1.21.7"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/jiti/-/jiti-1.21.7.tgz"
  integrity sha1-ndgQQ0JKPShFixk9ll8NGKIwC6k=

jiti@^2.0.0:
  version "2.4.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/jiti/-/jiti-2.4.2.tgz"
  integrity sha1-0Zt3Muu2EWsG4gONp0pVNm+u9WA=

jose@^5.0.0:
  version "5.10.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/jose/-/jose-5.10.0.tgz"
  integrity sha1-w3NGoJnWRnxAE1GpoMIWHg9SxL4=

"js-tokens@^3.0.0 || ^4.0.0", js-tokens@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/js-tokens/-/js-tokens-4.0.0.tgz"
  integrity sha1-GSA/tZmR35jjoocFDUZHzerzJJk=

js-yaml@^4.0.0, js-yaml@^4.1.0:
  version "4.1.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/js-yaml/-/js-yaml-4.1.0.tgz"
  integrity sha1-wftl+PUBeQHN0slRhkuhhFihBgI=
  dependencies:
    argparse "^2.0.1"

jsesc@^3.0.2:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/jsesc/-/jsesc-3.1.0.tgz"
  integrity sha1-dNM1ojT2ftGZB/2t+sfM+dQJgl0=

json-buffer@3.0.1:
  version "3.0.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/json-buffer/-/json-buffer-3.0.1.tgz"
  integrity sha1-kziAKjDTtmBfvgYT4JQAjKjAWhM=

json-parse-even-better-errors@^2.3.0:
  version "2.3.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz"
  integrity sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0=

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz"
  integrity sha1-afaofZUTq4u4/mO9sJecRI5oRmA=

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz"
  integrity sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=

json-to-pretty-yaml@^1.2.2:
  version "1.2.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/json-to-pretty-yaml/-/json-to-pretty-yaml-1.2.2.tgz"
  integrity sha1-9M0L0KXo/h3yWq9boRiwmf2ZLVs=
  dependencies:
    remedial "^1.0.7"
    remove-trailing-spaces "^1.0.6"

json5@^2.2.3:
  version "2.2.3"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/json5/-/json5-2.2.3.tgz"
  integrity sha1-eM1vGhm9wStz21rQxh79ZsHikoM=

keyv@^4.5.4:
  version "4.5.4"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/keyv/-/keyv-4.5.4.tgz"
  integrity sha1-qHmpnilFL5QkOfKkBeOvizHU3pM=
  dependencies:
    json-buffer "3.0.1"

levn@^0.4.1:
  version "0.4.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/levn/-/levn-0.4.1.tgz"
  integrity sha1-rkViwAdHO5MqYgDUAyaN0v/8at4=
  dependencies:
    prelude-ls "^1.2.1"
    type-check "~0.4.0"

lines-and-columns@^1.1.6:
  version "1.2.4"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/lines-and-columns/-/lines-and-columns-1.2.4.tgz"
  integrity sha1-7KKE910pZQeTCdwK2SVauy68FjI=

listr2@^4.0.5:
  version "4.0.5"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/listr2/-/listr2-4.0.5.tgz"
  integrity sha1-ncxQIhWD6LTHHEP5x9/Q71RrddU=
  dependencies:
    cli-truncate "^2.1.0"
    colorette "^2.0.16"
    log-update "^4.0.0"
    p-map "^4.0.0"
    rfdc "^1.3.0"
    rxjs "^7.5.5"
    through "^2.3.8"
    wrap-ansi "^7.0.0"

locate-path@^6.0.0:
  version "6.0.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/locate-path/-/locate-path-6.0.0.tgz"
  integrity sha1-VTIeswn+u8WcSAHZMackUqaB0oY=
  dependencies:
    p-locate "^5.0.0"

lodash.debounce@^4.0.8:
  version "4.0.8"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/lodash.debounce/-/lodash.debounce-4.0.8.tgz"
  integrity sha1-gteb/zCmfEAF/9XiUVMArZyk168=

lodash.merge@^4.6.2:
  version "4.6.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/lodash.merge/-/lodash.merge-4.6.2.tgz"
  integrity sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo=

lodash.sortby@^4.7.0:
  version "4.7.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/lodash.sortby/-/lodash.sortby-4.7.0.tgz"
  integrity sha1-7dFMgk4sycHgsKG0K7UhBRakJDg=

lodash@^4.17.20, lodash@^4.17.21, lodash@~4.17.0:
  version "4.17.21"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/lodash/-/lodash-4.17.21.tgz"
  integrity sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=

log-symbols@^4.0.0, log-symbols@^4.1.0:
  version "4.1.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/log-symbols/-/log-symbols-4.1.0.tgz"
  integrity sha1-P727lbRoOsn8eFER55LlWNSr1QM=
  dependencies:
    chalk "^4.1.0"
    is-unicode-supported "^0.1.0"

log-update@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/log-update/-/log-update-4.0.0.tgz"
  integrity sha1-WJ7NNSRx8qHAxXAodUOmTf0g4KE=
  dependencies:
    ansi-escapes "^4.3.0"
    cli-cursor "^3.1.0"
    slice-ansi "^4.0.0"
    wrap-ansi "^6.2.0"

loose-envify@^1.0.0, loose-envify@^1.4.0:
  version "1.4.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/loose-envify/-/loose-envify-1.4.0.tgz"
  integrity sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8=
  dependencies:
    js-tokens "^3.0.0 || ^4.0.0"

lower-case-first@^2.0.2:
  version "2.0.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/lower-case-first/-/lower-case-first-2.0.2.tgz"
  integrity sha1-ZMIySiJQv3w3xZAedqW1MJMBFgs=
  dependencies:
    tslib "^2.0.3"

lower-case@^2.0.2:
  version "2.0.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/lower-case/-/lower-case-2.0.2.tgz"
  integrity sha1-b6I3xj29xKgsoP2ILkci3F5jTig=
  dependencies:
    tslib "^2.0.3"

lru-cache@^5.1.1:
  version "5.1.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/lru-cache/-/lru-cache-5.1.1.tgz"
  integrity sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=
  dependencies:
    yallist "^3.0.2"

map-cache@^0.2.0:
  version "0.2.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/map-cache/-/map-cache-0.2.2.tgz"
  integrity sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8=

merge2@^1.3.0, merge2@^1.4.1:
  version "1.4.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/merge2/-/merge2-1.4.1.tgz"
  integrity sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=

meros@^1.2.1:
  version "1.3.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/meros/-/meros-1.3.1.tgz"
  integrity sha1-IaCQ1ZwCr7HkGOQGMbeKo3ohrGk=

micromatch@^4.0.5, micromatch@^4.0.8:
  version "4.0.8"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/micromatch/-/micromatch-4.0.8.tgz"
  integrity sha1-1m+hjzpHB2eJMgubGvMr2G2fogI=
  dependencies:
    braces "^3.0.3"
    picomatch "^2.3.1"

mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/mimic-fn/-/mimic-fn-2.1.0.tgz"
  integrity sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=

minimatch@^3.1.2:
  version "3.1.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/minimatch/-/minimatch-3.1.2.tgz"
  integrity sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=
  dependencies:
    brace-expansion "^1.1.7"

minimatch@^9.0.4:
  version "9.0.5"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/minimatch/-/minimatch-9.0.5.tgz"
  integrity sha1-10+d1rV9g9jpjPuCEzsDl4vJKeU=
  dependencies:
    brace-expansion "^2.0.1"

minimatch@^9.0.5:
  version "9.0.5"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/minimatch/-/minimatch-9.0.5.tgz"
  integrity sha1-10+d1rV9g9jpjPuCEzsDl4vJKeU=
  dependencies:
    brace-expansion "^2.0.1"

ms@^2.1.3:
  version "2.1.3"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/ms/-/ms-2.1.3.tgz"
  integrity sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=

mute-stream@0.0.8:
  version "0.0.8"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/mute-stream/-/mute-stream-0.0.8.tgz"
  integrity sha1-FjDEKyJR/4HiooPelqVJfqkuXg0=

nanoid@^3.3.11:
  version "3.3.11"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/nanoid/-/nanoid-3.3.11.tgz"
  integrity sha1-T08RLO++MDIC8hmYOBKJNiZtGFs=

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/natural-compare/-/natural-compare-1.4.0.tgz"
  integrity sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=

no-case@^3.0.4:
  version "3.0.4"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/no-case/-/no-case-3.0.4.tgz"
  integrity sha1-02H9XJgA9VhVGoNp/A3NRmK2Ek0=
  dependencies:
    lower-case "^2.0.2"
    tslib "^2.0.3"

node-domexception@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/node-domexception/-/node-domexception-1.0.0.tgz"
  integrity sha1-aIjbRqH3HAt2s/dVUBa2P+ZHZuU=

node-fetch@^2.7.0:
  version "2.7.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/node-fetch/-/node-fetch-2.7.0.tgz"
  integrity sha1-0PD6bj4twdJ+/NitmdVQvalNGH0=
  dependencies:
    whatwg-url "^5.0.0"

node-fetch@^3.3.2:
  version "3.3.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/node-fetch/-/node-fetch-3.3.2.tgz"
  integrity sha1-0eiJus33M7T/OyskPrehKGagt4s=
  dependencies:
    data-uri-to-buffer "^4.0.0"
    fetch-blob "^3.1.4"
    formdata-polyfill "^4.0.10"

node-int64@^0.4.0:
  version "0.4.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/node-int64/-/node-int64-0.4.0.tgz"
  integrity sha1-h6kGXNs1XTGC2PlM4RGIuCXGijs=

node-releases@^2.0.19:
  version "2.0.19"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/node-releases/-/node-releases-2.0.19.tgz"
  integrity sha1-nkRaUpUJUexNF32EOvNwtBHK8xQ=

normalize-path@^2.1.1:
  version "2.1.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/normalize-path/-/normalize-path-2.1.1.tgz"
  integrity sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=
  dependencies:
    remove-trailing-separator "^1.0.1"

nullthrows@^1.1.1:
  version "1.1.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/nullthrows/-/nullthrows-1.1.1.tgz"
  integrity sha1-eBgliEOFaulx6uQgitfX6xmkMbE=

object-assign@^4.1.0, object-assign@^4.1.1:
  version "4.1.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/object-assign/-/object-assign-4.1.1.tgz"
  integrity sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=

onetime@^5.1.0:
  version "5.1.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/onetime/-/onetime-5.1.2.tgz"
  integrity sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=
  dependencies:
    mimic-fn "^2.1.0"

optimism@^0.18.0:
  version "0.18.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/optimism/-/optimism-0.18.1.tgz"
  integrity sha1-XPFoR5IUE9uwrICZBzcDiLnGM18=
  dependencies:
    "@wry/caches" "^1.0.0"
    "@wry/context" "^0.7.0"
    "@wry/trie" "^0.5.0"
    tslib "^2.3.0"

optionator@^0.9.3:
  version "0.9.4"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/optionator/-/optionator-0.9.4.tgz"
  integrity sha1-fqHBpdkddk+yghOciP4R4YKjpzQ=
  dependencies:
    deep-is "^0.1.3"
    fast-levenshtein "^2.0.6"
    levn "^0.4.1"
    prelude-ls "^1.2.1"
    type-check "^0.4.0"
    word-wrap "^1.2.5"

ora@^5.4.1:
  version "5.4.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/ora/-/ora-5.4.1.tgz"
  integrity sha1-GyZ4Qmr0rEpQkAjl5KyemVnbnhg=
  dependencies:
    bl "^4.1.0"
    chalk "^4.1.0"
    cli-cursor "^3.1.0"
    cli-spinners "^2.5.0"
    is-interactive "^1.0.0"
    is-unicode-supported "^0.1.0"
    log-symbols "^4.1.0"
    strip-ansi "^6.0.0"
    wcwidth "^1.0.1"

os-tmpdir@~1.0.2:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/os-tmpdir/-/os-tmpdir-1.0.2.tgz"
  integrity sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=

p-limit@^3.0.2, p-limit@3.1.0:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/p-limit/-/p-limit-3.1.0.tgz"
  integrity sha1-4drMvnjQ0TiMoYxk/qOOPlfjcGs=
  dependencies:
    yocto-queue "^0.1.0"

p-locate@^5.0.0:
  version "5.0.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/p-locate/-/p-locate-5.0.0.tgz"
  integrity sha1-g8gxXGeFAF470CGDlBHJ4RDm2DQ=
  dependencies:
    p-limit "^3.0.2"

p-map@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/p-map/-/p-map-4.0.0.tgz"
  integrity sha1-uy+Vpe2i7BaOySdOBqdHw+KQTSs=
  dependencies:
    aggregate-error "^3.0.0"

param-case@^3.0.4:
  version "3.0.4"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/param-case/-/param-case-3.0.4.tgz"
  integrity sha1-fRf+SqEr3jTUp32RrPtiGcqtAcU=
  dependencies:
    dot-case "^3.0.4"
    tslib "^2.0.3"

parent-module@^1.0.0:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/parent-module/-/parent-module-1.0.1.tgz"
  integrity sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=
  dependencies:
    callsites "^3.0.0"

parse-filepath@^1.0.2:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/parse-filepath/-/parse-filepath-1.0.2.tgz"
  integrity sha1-pjISf1Oq89FYdvWHLz/6x2PWyJE=
  dependencies:
    is-absolute "^1.0.0"
    map-cache "^0.2.0"
    path-root "^0.1.1"

parse-json@^5.0.0, parse-json@^5.2.0:
  version "5.2.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/parse-json/-/parse-json-5.2.0.tgz"
  integrity sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-even-better-errors "^2.3.0"
    lines-and-columns "^1.1.6"

pascal-case@^3.1.2:
  version "3.1.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/pascal-case/-/pascal-case-3.1.2.tgz"
  integrity sha1-tI4O8rmOIF58Ha50fQsVCCN2YOs=
  dependencies:
    no-case "^3.0.4"
    tslib "^2.0.3"

path-case@^3.0.4:
  version "3.0.4"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/path-case/-/path-case-3.0.4.tgz"
  integrity sha1-kWhkUzTrlCZYN1xW+AtMDLX4LG8=
  dependencies:
    dot-case "^3.0.4"
    tslib "^2.0.3"

path-exists@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/path-exists/-/path-exists-4.0.0.tgz"
  integrity sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=

path-key@^3.1.0:
  version "3.1.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/path-key/-/path-key-3.1.1.tgz"
  integrity sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=

path-parse@^1.0.7:
  version "1.0.7"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/path-parse/-/path-parse-1.0.7.tgz"
  integrity sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=

path-root-regex@^0.1.0:
  version "0.1.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/path-root-regex/-/path-root-regex-0.1.2.tgz"
  integrity sha1-v8zcjfWxLcUsi0PsONGNcsBLqW0=

path-root@^0.1.1:
  version "0.1.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/path-root/-/path-root-0.1.1.tgz"
  integrity sha1-mkpoFMrBwM1zNgqV8yCDyOpHRbc=
  dependencies:
    path-root-regex "^0.1.0"

path-type@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/path-type/-/path-type-4.0.0.tgz"
  integrity sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs=

picocolors@^1.1.1:
  version "1.1.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/picocolors/-/picocolors-1.1.1.tgz"
  integrity sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s=

picomatch@^2.3.1:
  version "2.3.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/picomatch/-/picomatch-2.3.1.tgz"
  integrity sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=

"picomatch@^3 || ^4", picomatch@^4.0.2:
  version "4.0.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/picomatch/-/picomatch-4.0.2.tgz"
  integrity sha1-d8dCkx6PO4gglGx2zQwfE3MNHas=

postcss@^8.5.3:
  version "8.5.6"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/postcss/-/postcss-8.5.6.tgz"
  integrity sha1-KCUAZhWmGbT2Kp50JswSCzSajzw=
  dependencies:
    nanoid "^3.3.11"
    picocolors "^1.1.1"
    source-map-js "^1.2.1"

prelude-ls@^1.2.1:
  version "1.2.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/prelude-ls/-/prelude-ls-1.2.1.tgz"
  integrity sha1-3rxkidem5rDnYRiIzsiAM30xY5Y=

prettier-linter-helpers@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/prettier-linter-helpers/-/prettier-linter-helpers-1.0.0.tgz"
  integrity sha1-0j1B/hN1ZG3i0BBNNFSjAIgCz3s=
  dependencies:
    fast-diff "^1.1.2"

prettier@^3.5.1, prettier@>=3.0.0:
  version "3.6.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/prettier/-/prettier-3.6.2.tgz"
  integrity sha1-zNoCoQA+u7K/2m+DoHSXj2CLk5M=

promise@^7.1.1:
  version "7.3.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/promise/-/promise-7.3.1.tgz"
  integrity sha1-BktyYCsY+Q8pGSuLG8QY/9Hr078=
  dependencies:
    asap "~2.0.3"

prop-types@^15.6.2, prop-types@^15.7.2, prop-types@^15.8.1:
  version "15.8.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/prop-types/-/prop-types-15.8.1.tgz"
  integrity sha1-Z9h78aaU9IQ1zzMsJK8QIUoxQLU=
  dependencies:
    loose-envify "^1.4.0"
    object-assign "^4.1.1"
    react-is "^16.13.1"

punycode@^1.3.2:
  version "1.4.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/punycode/-/punycode-1.4.1.tgz"
  integrity sha1-wNWmOycYgArY4esPpSachN1BhF4=

punycode@^2.1.0:
  version "2.3.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/punycode/-/punycode-2.3.1.tgz"
  integrity sha1-AnQi4vrsCyXhVJw+G9gwm5EztuU=

pvtsutils@^1.3.5, pvtsutils@^1.3.6:
  version "1.3.6"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/pvtsutils/-/pvtsutils-1.3.6.tgz"
  integrity sha1-7EbjTbdCK55P3FSQV4wYg2V9YAE=
  dependencies:
    tslib "^2.8.1"

pvutils@^1.1.3:
  version "1.1.3"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/pvutils/-/pvutils-1.1.3.tgz"
  integrity sha1-81/B0n5809+9OcCCbRc+gGoD9aM=

queue-microtask@^1.2.2:
  version "1.2.3"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/queue-microtask/-/queue-microtask-1.2.3.tgz"
  integrity sha1-SSkii7xyTfrEPg77BYyve2z7YkM=

"react-dom@^16.0.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom@^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom@^16.8.0 || ^17.0.0 || ^18.0.0 || >=19.0.0-rc", "react-dom@^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom@^18 || ^19", react-dom@^19.0.0, react-dom@>=16.6.0, react-dom@>=18:
  version "19.1.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/react-dom/-/react-dom-19.1.0.tgz"
  integrity sha1-EzVY3so3+h1oJwjfiQSyUYZ5NiM=
  dependencies:
    scheduler "^0.26.0"

react-i18next@^15.4.1:
  version "15.6.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/react-i18next/-/react-i18next-15.6.0.tgz"
  integrity sha1-8XBVh2Ib08wckq8BRZSc2tj9LhU=
  dependencies:
    "@babel/runtime" "^7.27.6"
    html-parse-stringify "^3.0.1"

react-is@^16.13.1:
  version "16.13.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/react-is/-/react-is-16.13.1.tgz"
  integrity sha1-eJcppNw23imZ3BVt1sHZwYzqVqQ=

react-is@^16.7.0:
  version "16.13.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/react-is/-/react-is-16.13.1.tgz"
  integrity sha1-eJcppNw23imZ3BVt1sHZwYzqVqQ=

react-is@^18.3.1:
  version "18.3.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/react-is/-/react-is-18.3.1.tgz"
  integrity sha1-6DVX3BLq5jqZ4AOkY4ix3LtE234=

react-is@^19.1.0:
  version "19.1.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/react-is/-/react-is-19.1.0.tgz"
  integrity sha1-gFvOMhVGt+FMCEmJx3AiNRu90Rs=

react-refresh@^0.17.0:
  version "0.17.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/react-refresh/-/react-refresh-0.17.0.tgz"
  integrity sha1-t+V5w2V/I9BOzL5K0uWKjtUeflM=

react-router-dom@^7.2.0:
  version "7.6.3"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/react-router-dom/-/react-router-dom-7.6.3.tgz"
  integrity sha1-RWhucbuVjPgN2Tyjq/QRH+tO3TU=
  dependencies:
    react-router "7.6.3"

react-router@7.6.3:
  version "7.6.3"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/react-router/-/react-router-7.6.3.tgz"
  integrity sha1-ek6ltHm2bSxJqPAAgSsjGbTQpto=
  dependencies:
    cookie "^1.0.1"
    set-cookie-parser "^2.6.0"

react-smooth@^4.0.4:
  version "4.0.4"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/react-smooth/-/react-smooth-4.0.4.tgz"
  integrity sha1-pYdfi7YZY8phuBnO3FadwkU4lLQ=
  dependencies:
    fast-equals "^5.0.1"
    prop-types "^15.8.1"
    react-transition-group "^4.4.5"

react-toastify@^11.0.5:
  version "11.0.5"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/react-toastify/-/react-toastify-11.0.5.tgz"
  integrity sha1-zkxC0Q7rQzmIqyJk0+RFxOnRMxM=
  dependencies:
    clsx "^2.1.1"

react-transition-group@^4.4.5:
  version "4.4.5"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/react-transition-group/-/react-transition-group-4.4.5.tgz"
  integrity sha1-5T1OPzNE2oUhSJ+++PJYHUK+zdE=
  dependencies:
    "@babel/runtime" "^7.5.5"
    dom-helpers "^5.0.1"
    loose-envify "^1.4.0"
    prop-types "^15.6.2"

react@*, "react@^16.0.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react@^16.8.0 || ^17 || ^18 || ^19", "react@^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react@^16.8.0 || ^17.0.0 || ^18.0.0 || >=19.0.0-rc", "react@^17.0.0 || ^18.0.0 || ^19.0.0", "react@^18 || ^19", react@^19.0.0, react@^19.1.0, "react@>= 16.8.0", react@>=16.6.0, react@>=16.8.0, react@>=18, react@>=18.0.0:
  version "19.1.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/react/-/react-19.1.0.tgz"
  integrity sha1-kmhktsSNp2J/AEeV1szlDpB5O3U=

readable-stream@^3.4.0:
  version "3.6.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/readable-stream/-/readable-stream-3.6.2.tgz"
  integrity sha1-VqmzbqllwAxak+8x6xEaDxEFaWc=
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

recharts-scale@^0.4.4:
  version "0.4.5"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/recharts-scale/-/recharts-scale-0.4.5.tgz"
  integrity sha1-CWknHxTnMuZC/MW9SrJw1uh90dk=
  dependencies:
    decimal.js-light "^2.4.1"

recharts@^2.15.1:
  version "2.15.4"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/recharts/-/recharts-2.15.4.tgz"
  integrity sha1-DtPmbAhDvPLZ+aFyyvl7HQUSel8=
  dependencies:
    clsx "^2.0.0"
    eventemitter3 "^4.0.1"
    lodash "^4.17.21"
    react-is "^18.3.1"
    react-smooth "^4.0.4"
    recharts-scale "^0.4.4"
    tiny-invariant "^1.3.1"
    victory-vendor "^36.6.8"

rehackt@^0.1.0:
  version "0.1.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/rehackt/-/rehackt-0.1.0.tgz"
  integrity sha1-p8XiichzRfcNqHKKfrh45dA8aWs=

relay-runtime@12.0.0:
  version "12.0.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/relay-runtime/-/relay-runtime-12.0.0.tgz"
  integrity sha1-HgOSgr214MG5p9x/a5oJ1PT/gjc=
  dependencies:
    "@babel/runtime" "^7.0.0"
    fbjs "^3.0.0"
    invariant "^2.2.4"

remedial@^1.0.7:
  version "1.0.8"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/remedial/-/remedial-1.0.8.tgz"
  integrity sha1-peT9UqDklWrbr2LaY6WkanjFeKA=

remove-trailing-separator@^1.0.1:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/remove-trailing-separator/-/remove-trailing-separator-1.1.0.tgz"
  integrity sha1-wkvOKig62tW8P1jg1IJJuSN52O8=

remove-trailing-spaces@^1.0.6:
  version "1.0.9"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/remove-trailing-spaces/-/remove-trailing-spaces-1.0.9.tgz"
  integrity sha1-OcJwownqFv2oQlP/vesbWvoKonE=

require-directory@^2.1.1:
  version "2.1.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/require-directory/-/require-directory-2.1.1.tgz"
  integrity sha1-jGStX9MNqxyXbiNE/+f3kqam30I=

reselect@^5.1.1:
  version "5.1.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/reselect/-/reselect-5.1.1.tgz"
  integrity sha1-x2ax611VgpHl5VApitsL7MJLty4=

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/resolve-from/-/resolve-from-4.0.0.tgz"
  integrity sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=

resolve-from@5.0.0:
  version "5.0.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/resolve-from/-/resolve-from-5.0.0.tgz"
  integrity sha1-w1IlhD3493bfIcV1V7wIfp39/Gk=

resolve@^1.19.0:
  version "1.22.10"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/resolve/-/resolve-1.22.10.tgz"
  integrity sha1-tmPoP/sJu/I4aURza6roAwKbizk=
  dependencies:
    is-core-module "^2.16.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

restore-cursor@^3.1.0:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/restore-cursor/-/restore-cursor-3.1.0.tgz"
  integrity sha1-OfZ8VLOnpYzqUjbZXPADQjljH34=
  dependencies:
    onetime "^5.1.0"
    signal-exit "^3.0.2"

reusify@^1.0.4:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/reusify/-/reusify-1.1.0.tgz"
  integrity sha1-D+E7lSLhRz9RtVjueW4I8R+bSJ8=

rfdc@^1.3.0:
  version "1.4.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/rfdc/-/rfdc-1.4.1.tgz"
  integrity sha1-d492xPtzHZNBTo+SX77PZMzn9so=

rollup@^4.34.9:
  version "4.44.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/rollup/-/rollup-4.44.2.tgz"
  integrity sha1-+u2yfLKqZ0JTDDlmgJLuy694xIg=
  dependencies:
    "@types/estree" "1.0.8"
  optionalDependencies:
    "@rollup/rollup-android-arm-eabi" "4.44.2"
    "@rollup/rollup-android-arm64" "4.44.2"
    "@rollup/rollup-darwin-arm64" "4.44.2"
    "@rollup/rollup-darwin-x64" "4.44.2"
    "@rollup/rollup-freebsd-arm64" "4.44.2"
    "@rollup/rollup-freebsd-x64" "4.44.2"
    "@rollup/rollup-linux-arm-gnueabihf" "4.44.2"
    "@rollup/rollup-linux-arm-musleabihf" "4.44.2"
    "@rollup/rollup-linux-arm64-gnu" "4.44.2"
    "@rollup/rollup-linux-arm64-musl" "4.44.2"
    "@rollup/rollup-linux-loongarch64-gnu" "4.44.2"
    "@rollup/rollup-linux-powerpc64le-gnu" "4.44.2"
    "@rollup/rollup-linux-riscv64-gnu" "4.44.2"
    "@rollup/rollup-linux-riscv64-musl" "4.44.2"
    "@rollup/rollup-linux-s390x-gnu" "4.44.2"
    "@rollup/rollup-linux-x64-gnu" "4.44.2"
    "@rollup/rollup-linux-x64-musl" "4.44.2"
    "@rollup/rollup-win32-arm64-msvc" "4.44.2"
    "@rollup/rollup-win32-ia32-msvc" "4.44.2"
    "@rollup/rollup-win32-x64-msvc" "4.44.2"
    fsevents "~2.3.2"

run-async@^2.4.0:
  version "2.4.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/run-async/-/run-async-2.4.1.tgz"
  integrity sha1-hEDsz5nqPnC9QJ1JqriOEMGJpFU=

run-parallel@^1.1.9:
  version "1.2.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/run-parallel/-/run-parallel-1.2.0.tgz"
  integrity sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4=
  dependencies:
    queue-microtask "^1.2.2"

rxjs@^7.5.5:
  version "7.8.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/rxjs/-/rxjs-7.8.2.tgz"
  integrity sha1-lVvEc+2K8RoAKivlIHG/R1Y4YHs=
  dependencies:
    tslib "^2.1.0"

safe-buffer@~5.2.0:
  version "5.2.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/safe-buffer/-/safe-buffer-5.2.1.tgz"
  integrity sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=

"safer-buffer@>= 2.1.2 < 3":
  version "2.1.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/safer-buffer/-/safer-buffer-2.1.2.tgz"
  integrity sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=

scheduler@^0.26.0:
  version "0.26.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/scheduler/-/scheduler-0.26.0.tgz"
  integrity sha1-TOiowqIJXxPqEb+aRFvlDFVdYzc=

scuid@^1.1.0:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/scuid/-/scuid-1.1.0.tgz"
  integrity sha1-0/n5IJVuc3pg9y0OStKAvzJNXas=

semver@^6.3.1:
  version "6.3.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/semver/-/semver-6.3.1.tgz"
  integrity sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=

semver@^7.6.0:
  version "7.7.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/semver/-/semver-7.7.2.tgz"
  integrity sha1-Z9mf3NNc7CHm+Lh6f9UVoz+YK1g=

sentence-case@^3.0.4:
  version "3.0.4"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/sentence-case/-/sentence-case-3.0.4.tgz"
  integrity sha1-NkWnuMEXx4f96HAgViJbtipFEx8=
  dependencies:
    no-case "^3.0.4"
    tslib "^2.0.3"
    upper-case-first "^2.0.2"

set-cookie-parser@^2.6.0:
  version "2.7.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/set-cookie-parser/-/set-cookie-parser-2.7.1.tgz"
  integrity sha1-MBbxUAciAt++kPre4FNXPMidKUM=

setimmediate@^1.0.5:
  version "1.0.5"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/setimmediate/-/setimmediate-1.0.5.tgz"
  integrity sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU=

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/shebang-command/-/shebang-command-2.0.0.tgz"
  integrity sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/shebang-regex/-/shebang-regex-3.0.0.tgz"
  integrity sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=

shell-quote@^1.7.3:
  version "1.8.3"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/shell-quote/-/shell-quote-1.8.3.tgz"
  integrity sha1-VeQO8zz1xomQI1Oj2M0aZyXwi0s=

signal-exit@^3.0.2:
  version "3.0.7"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/signal-exit/-/signal-exit-3.0.7.tgz"
  integrity sha1-qaF2f4r4QVURTqq9c/mSc8j1mtk=

signedsource@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/signedsource/-/signedsource-1.0.0.tgz"
  integrity sha1-HdrOSYF5j5O9gzlzgD2A1S6TrWo=

slash@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/slash/-/slash-3.0.0.tgz"
  integrity sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ=

slice-ansi@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/slice-ansi/-/slice-ansi-3.0.0.tgz"
  integrity sha1-Md3BCTCht+C2ewjJbC9Jt3p4l4c=
  dependencies:
    ansi-styles "^4.0.0"
    astral-regex "^2.0.0"
    is-fullwidth-code-point "^3.0.0"

slice-ansi@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/slice-ansi/-/slice-ansi-4.0.0.tgz"
  integrity sha1-UA6N0P1VsFgVCGJVsxla3ypF/ms=
  dependencies:
    ansi-styles "^4.0.0"
    astral-regex "^2.0.0"
    is-fullwidth-code-point "^3.0.0"

snake-case@^3.0.4:
  version "3.0.4"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/snake-case/-/snake-case-3.0.4.tgz"
  integrity sha1-Tyu9Vo6ZNavf1ZPzTGkdrbScRSw=
  dependencies:
    dot-case "^3.0.4"
    tslib "^2.0.3"

source-map-js@^1.2.1:
  version "1.2.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/source-map-js/-/source-map-js-1.2.1.tgz"
  integrity sha1-HOVlD93YerwJnto33P8CTCZnrkY=

source-map@^0.5.7:
  version "0.5.7"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/source-map/-/source-map-0.5.7.tgz"
  integrity sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=

sponge-case@^1.0.1:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/sponge-case/-/sponge-case-1.0.1.tgz"
  integrity sha1-JggzuGRTiD2XT4SFTNtjrsxa70w=
  dependencies:
    tslib "^2.0.3"

streamsearch@^1.1.0:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/streamsearch/-/streamsearch-1.1.0.tgz"
  integrity sha1-QE3R4iR8qUr1VOhBqO8OqiONp2Q=

string_decoder@^1.1.1:
  version "1.3.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/string_decoder/-/string_decoder-1.3.0.tgz"
  integrity sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4=
  dependencies:
    safe-buffer "~5.2.0"

string-env-interpolation@^1.0.1:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/string-env-interpolation/-/string-env-interpolation-1.0.1.tgz"
  integrity sha1-rUOXrkrFP+bJHRQCrW9qUoYscVI=

string-width@^4.1.0, string-width@^4.2.0, string-width@^4.2.3:
  version "4.2.3"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/string-width/-/string-width-4.2.3.tgz"
  integrity sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/strip-ansi/-/strip-ansi-6.0.1.tgz"
  integrity sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=
  dependencies:
    ansi-regex "^5.0.1"

strip-json-comments@^3.1.1:
  version "3.1.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/strip-json-comments/-/strip-json-comments-3.1.1.tgz"
  integrity sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=

stylis@4.2.0:
  version "4.2.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/stylis/-/stylis-4.2.0.tgz"
  integrity sha1-edruAgiWTI/mlaQvz/ysYzohGlE=

supports-color@^7.1.0:
  version "7.2.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/supports-color/-/supports-color-7.2.0.tgz"
  integrity sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=
  dependencies:
    has-flag "^4.0.0"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz"
  integrity sha1-btpL00SjyUrqN21MwxvHcxEDngk=

swap-case@^2.0.2:
  version "2.0.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/swap-case/-/swap-case-2.0.2.tgz"
  integrity sha1-Zxrts8nBN+KYXvUcUfnphEW/cNk=
  dependencies:
    tslib "^2.0.3"

symbol-observable@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/symbol-observable/-/symbol-observable-4.0.0.tgz"
  integrity sha1-W0JfGSJ56H8vm5N6yFQNGYSzkgU=

sync-fetch@0.6.0-2:
  version "0.6.0-2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/sync-fetch/-/sync-fetch-0.6.0-2.tgz"
  integrity sha1-2C1tyO+vLRA6kBXnvXugv8jgePI=
  dependencies:
    node-fetch "^3.3.2"
    timeout-signal "^2.0.0"
    whatwg-mimetype "^4.0.0"

synckit@^0.11.7:
  version "0.11.8"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/synckit/-/synckit-0.11.8.tgz"
  integrity sha1-sqqumYpO9H3tYHc60G58uCH1VFc=
  dependencies:
    "@pkgr/core" "^0.2.4"

through@^2.3.6, through@^2.3.8:
  version "2.3.8"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/through/-/through-2.3.8.tgz"
  integrity sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=

timeout-signal@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/timeout-signal/-/timeout-signal-2.0.0.tgz"
  integrity sha1-IyB+pEjVAli7De/jvupKRnZDq7o=

tiny-invariant@^1.3.1:
  version "1.3.3"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/tiny-invariant/-/tiny-invariant-1.3.3.tgz"
  integrity sha1-RmgLeoc6DV0QAFmV65CnDXTWASc=

tinyglobby@^0.2.13:
  version "0.2.14"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/tinyglobby/-/tinyglobby-0.2.14.tgz"
  integrity sha1-UoCwzz+XKwUOdK6IQGwKalj0B50=
  dependencies:
    fdir "^6.4.4"
    picomatch "^4.0.2"

title-case@^3.0.3:
  version "3.0.3"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/title-case/-/title-case-3.0.3.tgz"
  integrity sha1-vGibRvAuQR8dHh0IH3w97KBImYI=
  dependencies:
    tslib "^2.0.3"

tmp@^0.0.33:
  version "0.0.33"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/tmp/-/tmp-0.0.33.tgz"
  integrity sha1-bTQzWIl2jSGyvNoKonfO07G/rfk=
  dependencies:
    os-tmpdir "~1.0.2"

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/to-regex-range/-/to-regex-range-5.0.1.tgz"
  integrity sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=
  dependencies:
    is-number "^7.0.0"

tr46@~0.0.3:
  version "0.0.3"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/tr46/-/tr46-0.0.3.tgz"
  integrity sha1-gYT9NH2snNwYWZLzpmIuFLnZq2o=

ts-api-utils@^2.1.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/ts-api-utils/-/ts-api-utils-2.1.0.tgz"
  integrity sha1-WV9wlORu7TZME/0j51+VE9Kbr5E=

ts-invariant@^0.10.3:
  version "0.10.3"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/ts-invariant/-/ts-invariant-0.10.3.tgz"
  integrity sha1-PgSP+W6RRZ/8oBME28f2HB9kL2w=
  dependencies:
    tslib "^2.1.0"

ts-log@^2.2.3:
  version "2.2.7"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/ts-log/-/ts-log-2.2.7.tgz"
  integrity sha1-T0USFEiYt3yZhOkVhwdvy4UYaI4=

tslib@^2.0.0, tslib@^2.0.3, tslib@^2.1.0, tslib@^2.3.0, tslib@^2.3.1, tslib@^2.4.0, tslib@^2.5.0, tslib@^2.6.2, tslib@^2.6.3, tslib@^2.7.0, tslib@^2.8.1:
  version "2.8.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/tslib/-/tslib-2.8.1.tgz"
  integrity sha1-YS7+TtI11Wfoq6Xypfq3AoCt6D8=

tslib@~2.6.0:
  version "2.6.3"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/tslib/-/tslib-2.6.3.tgz"
  integrity sha1-BDj4EK16ntzeeiQcPYDbaTyMv+A=

type-check@^0.4.0, type-check@~0.4.0:
  version "0.4.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/type-check/-/type-check-0.4.0.tgz"
  integrity sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE=
  dependencies:
    prelude-ls "^1.2.1"

type-fest@^0.21.3:
  version "0.21.3"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/type-fest/-/type-fest-0.21.3.tgz"
  integrity sha1-0mCiSwGYQ24TP6JqUkptZfo7Ljc=

typescript-eslint@^8.22.0:
  version "8.35.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/typescript-eslint/-/typescript-eslint-8.35.1.tgz"
  integrity sha1-Td7aXFd3p72GUWKA2Ama2gYFXy8=
  dependencies:
    "@typescript-eslint/eslint-plugin" "8.35.1"
    "@typescript-eslint/parser" "8.35.1"
    "@typescript-eslint/utils" "8.35.1"

typescript@^5, typescript@>=4.8.4, "typescript@>=4.8.4 <5.9.0", typescript@>=4.9.5, typescript@~5.7.2:
  version "5.7.3"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/typescript/-/typescript-5.7.3.tgz"
  integrity sha1-kZtEp9u4WDqbhW0WK+JKVL+ABz4=

ua-parser-js@^1.0.35:
  version "1.0.40"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/ua-parser-js/-/ua-parser-js-1.0.40.tgz"
  integrity sha1-rGr/T9jqPnlKaqdD7Jwvwp51tnU=

unc-path-regex@^0.1.2:
  version "0.1.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/unc-path-regex/-/unc-path-regex-0.1.2.tgz"
  integrity sha1-5z3T17DXxe2G+6xrCufYxqadUPo=

undici-types@~6.21.0:
  version "6.21.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/undici-types/-/undici-types-6.21.0.tgz"
  integrity sha1-aR0ArzkJvpOn+qE75hs6W1DvEss=

unixify@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/unixify/-/unixify-1.0.0.tgz"
  integrity sha1-OmQcjC/7zk2mg6XHDwOkYpQMIJA=
  dependencies:
    normalize-path "^2.1.1"

update-browserslist-db@^1.1.3:
  version "1.1.3"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz"
  integrity sha1-NIN33SRSFvnnBg/1CxWht0C3VCA=
  dependencies:
    escalade "^3.2.0"
    picocolors "^1.1.1"

upper-case-first@^2.0.2:
  version "2.0.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/upper-case-first/-/upper-case-first-2.0.2.tgz"
  integrity sha1-mSwyc/iCq9GdHgKJTMFHEX+EQyQ=
  dependencies:
    tslib "^2.0.3"

upper-case@^2.0.2:
  version "2.0.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/upper-case/-/upper-case-2.0.2.tgz"
  integrity sha1-2JgQgj+qsd8VSbfZenb4Ziuub3o=
  dependencies:
    tslib "^2.0.3"

uri-js@^4.2.2:
  version "4.4.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/uri-js/-/uri-js-4.4.1.tgz"
  integrity sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=
  dependencies:
    punycode "^2.1.0"

urlpattern-polyfill@^10.0.0:
  version "10.1.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/urlpattern-polyfill/-/urlpattern-polyfill-10.1.0.tgz"
  integrity sha1-GyUX5hQTbHO6MpSNXno6Bjy6jnQ=

urlpattern-polyfill@^8.0.0:
  version "8.0.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/urlpattern-polyfill/-/urlpattern-polyfill-8.0.2.tgz"
  integrity sha1-mfCW417/i/S1oqp9WKFSPW68fOU=

use-sync-external-store@^1.0.0, use-sync-external-store@>=1.2.0:
  version "1.5.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/use-sync-external-store/-/use-sync-external-store-1.5.0.tgz"
  integrity sha1-VRIuKj7dKmwQYXTCdIXg/Vm8/KA=

util-deprecate@^1.0.1:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/util-deprecate/-/util-deprecate-1.0.2.tgz"
  integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=

victory-vendor@^36.6.8:
  version "36.9.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/victory-vendor/-/victory-vendor-36.9.2.tgz"
  integrity sha1-ZosCpEj6TqD3iNv0Iot+ZGaf+AE=
  dependencies:
    "@types/d3-array" "^3.0.3"
    "@types/d3-ease" "^3.0.0"
    "@types/d3-interpolate" "^3.0.1"
    "@types/d3-scale" "^4.0.2"
    "@types/d3-shape" "^3.1.0"
    "@types/d3-time" "^3.0.0"
    "@types/d3-timer" "^3.0.0"
    d3-array "^3.1.6"
    d3-ease "^3.0.1"
    d3-interpolate "^3.0.1"
    d3-scale "^4.0.2"
    d3-shape "^3.1.0"
    d3-time "^3.0.0"
    d3-timer "^3.0.1"

"vite@^4.2.0 || ^5.0.0 || ^6.0.0 || ^7.0.0-beta.0", vite@^6.1.0:
  version "6.3.5"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/vite/-/vite-6.3.5.tgz"
  integrity sha1-/sc4eQE8nAEoyNKEUExtGUENEqM=
  dependencies:
    esbuild "^0.25.0"
    fdir "^6.4.4"
    picomatch "^4.0.2"
    postcss "^8.5.3"
    rollup "^4.34.9"
    tinyglobby "^0.2.13"
  optionalDependencies:
    fsevents "~2.3.3"

void-elements@3.1.0:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/void-elements/-/void-elements-3.1.0.tgz"
  integrity sha1-YU9/v42AHwu18GYfWy9XhXUOTwk=

wcwidth@^1.0.1:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/wcwidth/-/wcwidth-1.0.1.tgz"
  integrity sha1-8LDc+RW8X/FSivrbLA4XtTLaL+g=
  dependencies:
    defaults "^1.0.3"

web-streams-polyfill@^3.0.3, web-streams-polyfill@^3.2.1:
  version "3.3.3"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/web-streams-polyfill/-/web-streams-polyfill-3.3.3.tgz"
  integrity sha1-IHO5Gi/bH7+9QB594KyfghTOy0s=

webcrypto-core@^1.8.0:
  version "1.8.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/webcrypto-core/-/webcrypto-core-1.8.1.tgz"
  integrity sha1-CdW9ipxI6fvK9BLgax/xpXUUzoY=
  dependencies:
    "@peculiar/asn1-schema" "^2.3.13"
    "@peculiar/json-schema" "^1.1.12"
    asn1js "^3.0.5"
    pvtsutils "^1.3.5"
    tslib "^2.7.0"

webidl-conversions@^3.0.0:
  version "3.0.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/webidl-conversions/-/webidl-conversions-3.0.1.tgz"
  integrity sha1-JFNCdeKnvGvnvIZhHMFq4KVlSHE=

whatwg-mimetype@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/whatwg-mimetype/-/whatwg-mimetype-4.0.0.tgz"
  integrity sha1-vBv5SphdxQOI1UqSWKxAXDyi/Ao=

whatwg-url@^5.0.0:
  version "5.0.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/whatwg-url/-/whatwg-url-5.0.0.tgz"
  integrity sha1-lmRU6HZUYuN2RNNib2dCzotwll0=
  dependencies:
    tr46 "~0.0.3"
    webidl-conversions "^3.0.0"

which@^2.0.1:
  version "2.0.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/which/-/which-2.0.2.tgz"
  integrity sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=
  dependencies:
    isexe "^2.0.0"

word-wrap@^1.2.5:
  version "1.2.5"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/word-wrap/-/word-wrap-1.2.5.tgz"
  integrity sha1-0sRcbdT7zmIaZvE2y+Mor9BBCzQ=

wrap-ansi@^6.0.1, wrap-ansi@^6.2.0:
  version "6.2.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/wrap-ansi/-/wrap-ansi-6.2.0.tgz"
  integrity sha1-6Tk7oHEC5skaOyIUePAlfNKFblM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^7.0.0:
  version "7.0.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/wrap-ansi/-/wrap-ansi-7.0.0.tgz"
  integrity sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

ws@*, ws@^8, ws@^8.17.1:
  version "8.18.3"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/ws/-/ws-8.18.3.tgz"
  integrity sha1-tWuIq//eYnkcY5FwQAyT3LDJVHI=

y18n@^5.0.5:
  version "5.0.8"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/y18n/-/y18n-5.0.8.tgz"
  integrity sha1-f0k00PfKjFb5UxSTndzS3ZHOHVU=

yallist@^3.0.2:
  version "3.1.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/yallist/-/yallist-3.1.1.tgz"
  integrity sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=

yaml-ast-parser@^0.0.43:
  version "0.0.43"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/yaml-ast-parser/-/yaml-ast-parser-0.0.43.tgz"
  integrity sha1-6KI+b7TDgHarkplcXcoz89PXybs=

yaml@^1.10.0:
  version "1.10.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/yaml/-/yaml-1.10.2.tgz"
  integrity sha1-IwHF/78StGfejaIzOkWeKeeSDks=

yaml@^2.3.1, yaml@^2.4.2:
  version "2.8.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/yaml/-/yaml-2.8.0.tgz"
  integrity sha1-FfjJhmIRvcLTeBoIkORNT6Gl//Y=

yargs-parser@^21.1.1:
  version "21.1.1"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/yargs-parser/-/yargs-parser-21.1.1.tgz"
  integrity sha1-kJa87r+ZDSG7MfqVFuDt4pSnfTU=

yargs@^17.0.0:
  version "17.7.2"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/yargs/-/yargs-17.7.2.tgz"
  integrity sha1-mR3zmspnWhkrgW4eA2P5110qomk=
  dependencies:
    cliui "^8.0.1"
    escalade "^3.1.1"
    get-caller-file "^2.0.5"
    require-directory "^2.1.1"
    string-width "^4.2.3"
    y18n "^5.0.5"
    yargs-parser "^21.1.1"

yocto-queue@^0.1.0:
  version "0.1.0"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/yocto-queue/-/yocto-queue-0.1.0.tgz"
  integrity sha1-ApTrPe4FAo0x7hpfosVWpqrxChs=

zen-observable-ts@^1.2.5:
  version "1.2.5"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/zen-observable-ts/-/zen-observable-ts-1.2.5.tgz"
  integrity sha1-bG2eo9OoQoEsbpUZIJNloSK6i1g=
  dependencies:
    zen-observable "0.8.15"

zen-observable@0.8.15:
  version "0.8.15"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/zen-observable/-/zen-observable-0.8.15.tgz"
  integrity sha1-lkFcUS2OP/2SCv04iWBOMLnqrBU=

zustand@^5.0.3:
  version "5.0.6"
  resolved "https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/zustand/-/zustand-5.0.6.tgz"
  integrity sha1-otpD2Nw9MeMUJ55brsBil76nClw=
