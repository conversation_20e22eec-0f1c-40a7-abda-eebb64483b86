.npmrc

registry=https://pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/         
always-auth=true

; begin auth token
//pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/:username=agencytech
//pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/:_password=OTlsaW9od2RlemJBWXVObFl5R3gzZ0VjYXZTRUZrMVZhRXlHMUhBbDFWcENEaVRLS0l4a0pRUUo5OUJGQUNBQUFBQTd5Sm9xQUFBU0FaRE8yRkY2
//pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/:email=<EMAIL>
//pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/:username=agencytech
//pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/:_password=OTlsaW9od2RlemJBWXVObFl5R3gzZ0VjYXZTRUZrMVZhRXlHMUhBbDFWcENEaVRLS0l4a0pRUUo5OUJGQUNBQUFBQTd5Sm9xQUFBU0FaRE8yRkY2
//pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/:email=<EMAIL>
; end auth token